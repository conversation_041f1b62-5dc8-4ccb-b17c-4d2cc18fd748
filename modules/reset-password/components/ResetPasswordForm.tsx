import { useUpdatePasswordMutation } from '@/apis/auth';
import { Spacer } from '@/components/Spacer';
import { Button } from '@/components/ui/Button';
import TextInput from '@/components/ui/TextInput';
import { toastError } from '@/utils/toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { router } from 'expo-router';
import { Controller, useForm } from 'react-hook-form';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { ResetPasswordFormData, resetPasswordSchema } from '../schema';

type Props = {
  resetToken: string;
  identifier: string;
};

export const ResetPasswordForm = ({ identifier, resetToken }: Props) => {
  const { styles } = useStyles(stylesheet);

  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting: formSubmitting },
  } = useForm<ResetPasswordFormData>({
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
    resolver: zodResolver(resetPasswordSchema),
  });

  const { mutateAsync: updatePassword, isPending: updatingPassword } = useUpdatePasswordMutation();

  const onSubmit = async (formData: ResetPasswordFormData) => {
    try {
      await updatePassword({
        identifier,
        newPassword: formData.password,
        resetToken,
      });

      router.replace('/reset-password-success');
    } catch (error) {
      toastError(error);
    }
  };

  return (
    <View style={styles.formContainer}>
      <Controller
        control={control}
        name='password'
        render={({ field: { onChange, value } }) => (
          <TextInput
            label='Password'
            placeholder='Input your password'
            isPassword
            value={value}
            onChangeText={onChange}
            error={errors.password?.message}
          />
        )}
      />

      <Spacer height={32} />

      <Controller
        control={control}
        name='confirmPassword'
        render={({ field: { onChange, value } }) => (
          <TextInput
            label='Confirm Password'
            placeholder='Re input your password'
            isPassword
            value={value}
            onChangeText={onChange}
            error={errors.confirmPassword?.message}
          />
        )}
      />
      <Spacer height={48} />

      <Button
        isLoading={updatingPassword}
        disabled={formSubmitting || updatingPassword}
        style={styles.button}
        type='default'
        onPress={handleSubmit(onSubmit)}
      >
        Reset Password
      </Button>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  formContainer: {
    width: '100%',
  },
  button: {
    marginHorizontal: 31,
  },
}));
