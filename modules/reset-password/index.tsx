import { Show } from '@/components/Show';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { Header } from '@/components/ui/Header';
import { REGEX } from '@/constants/regex';
import { useLocalSearchParams } from 'expo-router/build/hooks';
import { View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { ResetPasswordForm } from './components/ResetPasswordForm';

const ResetPassword = () => {
  const { styles } = useStyles(stylesheet);
  const localParams = useLocalSearchParams();
  const emailOrPhone = (localParams?.emailOrPhone as string)?.trim() ?? '';
  const resetToken = (localParams?.token as string) ?? '';
  const isValidEmail = REGEX.EMAIL.test(emailOrPhone ?? '');

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Header isBack />

        <Spacer height={52} />

        <ThemedText style={styles.title} type='titleSemiBold'>
          Create New Password
        </ThemedText>

        <Spacer height={16} />

        <View style={styles.emailContainer}>
          <ThemedText style={styles.emailOrText}>
            Create a strong password for updating the {isValidEmail ? 'email' : 'phone number'}{' '}
            <ThemedText>{`${emailOrPhone}`}</ThemedText>
            <Show when={isValidEmail}>
              <ThemedText>.</ThemedText>
            </Show>
          </ThemedText>
        </View>

        <Spacer height={40} />

        <ResetPasswordForm resetToken={resetToken} identifier={emailOrPhone} />
      </View>
    </SafeAreaView>
  );
};

export default ResetPassword;

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    paddingHorizontal: 24,
  },
  content: {
    flex: 1,
  },
  title: {
    fontSize: 24,
    ...theme.fw600,
    textAlign: 'center',
  },
  emailContainer: {
    alignItems: 'center',
    ...theme.fw500,
    fontSize: 14,
  },
  emailOrText: {
    fontSize: 14,
    ...theme.fw500,
    color: theme.colors.neutralLightGrey,
    marginBottom: 8,
    textAlign: 'center',
  },
}));
