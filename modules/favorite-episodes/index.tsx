import { View, TouchableOpacity } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { ThemedText } from '@/components/ThemedText';
import { router } from 'expo-router';
import { useState, useCallback, useEffect } from 'react';
import * as Haptics from 'expo-haptics';
import DeleteEpisodeModal from './components/DeleteEpisodeModal';
import { EpisodeItem } from './components/EpisodeItem';
import { Header } from '@/components/ui/Header';
import { Spacer } from '@/components/Spacer';
import {
  useGetFavoriteEpisodesInfiniteQuery,
  useRemoveFavoriteEpisodeMutation,
  useUpdateFavoriteEpisodeOrderMutation,
} from '@/apis/favorite';
import { useGetProfileQuery } from '@/apis/auth/queries';
import { useLocalSearchParams } from 'expo-router';
import { InfiniteData, useQueryClient } from '@tanstack/react-query';
import { IFavoriteEpisode, IGetFavoriteEpisodesResponse } from '@/apis/favorite/types';
import { toastError, toastSuccess } from '@/utils/toast';
import { checkIsOwner } from '@/utils/func';
import { getUserIdForFavorites } from '@/utils/func';
import { IconLoading } from '@/components/IconLoading';
import { Show } from '@/components/Show';
import { Empty } from '@/components/Empty';
import Animated from 'react-native-reanimated';

import ReorderableList, { ReorderableListReorderEvent, reorderItems } from 'react-native-reorderable-list';
import { ListRenderItemInfo } from 'react-native';
import queryKeys from '@/utils/queryKeys';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';

export const DraggableFavoriteEpisodes = () => {
  const { styles, theme } = useStyles(stylesheet);
  const [episodes, setEpisodes] = useState<IFavoriteEpisode[]>([]);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [episodeToDelete, setEpisodeToDelete] = useState<{ id: number; title: string } | null>(null);
  const [isPendingDelete, setIsPendingDelete] = useState(false);
  const { userId: routerUserId } = useLocalSearchParams<{ userId?: string }>();
  const { data: userProfile } = useGetProfileQuery();
  const queryClient = useQueryClient();

  const { onCheckAccountRestricted } = useCheckRestrictAccount();

  // Determine the target userId and check ownership
  const targetUserId = getUserIdForFavorites(routerUserId, userProfile?.id);
  const isOwner = checkIsOwner(userProfile?.id, targetUserId);

  const {
    data: favoriteEpisodesData,
    isFetching,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useGetFavoriteEpisodesInfiniteQuery(
    {
      userId: Number(targetUserId),
      limit: 20,
    },
    {
      enabled: !!targetUserId,
    } as any
  );

  const removeFavoriteMutation = useRemoveFavoriteEpisodeMutation({
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.favorites.episodesInfinite() });
      setIsPendingDelete(false);
      setShowDeleteModal(false);
      setEpisodeToDelete(null);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      toastSuccess({
        description: 'Removed episode from favorites successfully',
      });
    },
    onError: (error) => {
      setShowDeleteModal(false);
      toastError(error);
      setIsPendingDelete(false);
    },
  });

  const updateOrderMutation = useUpdateFavoriteEpisodeOrderMutation({
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.favorites.episodesInfinite() });
    },
    onError: (error) => {
      const infiniteData = favoriteEpisodesData as unknown as InfiniteData<IGetFavoriteEpisodesResponse>;
      const allEpisodes = infiniteData?.pages?.flatMap((page) => page.data) || [];
      setEpisodes(allEpisodes);
      toastError(error);
    },
  });

  useEffect(() => {
    const infiniteData = favoriteEpisodesData as unknown as InfiniteData<IGetFavoriteEpisodesResponse>;
    const allEpisodes = infiniteData?.pages?.flatMap((page) => page.data) || [];
    setEpisodes(allEpisodes);
  }, [favoriteEpisodesData]);

  const handleAddEpisodes = useCallback(async () => {
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    router.push({
      pathname: '/(app)/discover-search',
      params: { searchAction: 'add-episodes' },
    });
  }, [onCheckAccountRestricted]);

  const handleRemoveEpisode = useCallback(
    (episodeId: number, episodeTitle: string) => {
      const isRestricted = onCheckAccountRestricted();
      if (isRestricted) return;

      setEpisodeToDelete({ id: episodeId, title: episodeTitle });
      setShowDeleteModal(true);
    },
    [onCheckAccountRestricted]
  );

  const handleConfirmDelete = useCallback(async () => {
    if (!episodeToDelete) return;
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    setIsPendingDelete(true);

    removeFavoriteMutation.mutate(episodeToDelete.id);
  }, [episodeToDelete, removeFavoriteMutation, onCheckAccountRestricted]);

  const handleCloseDeleteModal = useCallback(() => {
    setShowDeleteModal(false);
    setEpisodeToDelete(null);
  }, []);

  const renderItem = useCallback(
    ({ item, index }: ListRenderItemInfo<IFavoriteEpisode>) => {
      return <EpisodeItem item={item} index={index} onRemove={handleRemoveEpisode} isOwner={isOwner} />;
    },
    [handleRemoveEpisode, isOwner]
  );

  const keyExtractor = useCallback((item: IFavoriteEpisode) => item?.id?.toString(), []);

  const handleReorder = ({ from, to }: ReorderableListReorderEvent) => {
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    updateOrderMutation.mutate({
      id: episodes[from].id,
      index: to,
    });

    setEpisodes((value) => reorderItems(value, from, to));
  };

  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  return (
    <View style={styles.container}>
      <Header isBack title='' leftStyle={styles.headerButton} />

      <Spacer height={24} />

      <View style={styles.header}>
        <View style={styles.leftSection}>
          <ThemedText style={styles.title}>Favorite Episodes</ThemedText>
        </View>

        {isOwner && (
          <TouchableOpacity onPress={handleAddEpisodes} style={styles.addButton}>
            <ThemedText style={styles.addButtonText}>Add Episodes</ThemedText>
          </TouchableOpacity>
        )}
      </View>

      <Spacer height={40} />

      <Show when={isFetching}>
        <Animated.View style={styles.loadingContainer}>
          <IconLoading />
        </Animated.View>
      </Show>

      <Show when={!isFetching && episodes.length === 0}>
        <Spacer height={73} />

        <Empty
          type='like'
          containerStyle={styles.emptyContainer}
          emptyText={`${isOwner ? 'You' : userProfile?.username} ${isOwner ? "don't" : "doesn't"} have any favorite episodes yet.`}
        />
      </Show>

      <Show when={!isFetching && episodes.length > 0}>
        <ReorderableList
          data={episodes}
          renderItem={renderItem}
          keyExtractor={keyExtractor}
          onReorder={handleReorder}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContent}
          maxToRenderPerBatch={8}
          windowSize={8}
          initialNumToRender={6}
          updateCellsBatchingPeriod={50}
          getItemLayout={undefined}
          ItemSeparatorComponent={() => (
            <View style={styles.separator}>
              <View style={styles.separatorLine} />
            </View>
          )}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
          ListFooterComponent={isFetchingNextPage ? <IconLoading /> : null}
        />

        <DeleteEpisodeModal
          visible={showDeleteModal}
          onClose={handleCloseDeleteModal}
          onDelete={handleConfirmDelete}
          isPendingDelete={isPendingDelete}
          episodeTitle={episodeToDelete?.title}
        />
      </Show>
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    backgroundColor: theme.colors.background,
    flex: 1,
    paddingTop: rt.insets.top + 12,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    backgroundColor: theme.colors.background,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  backButton: {
    marginRight: 8,
    padding: 4,
  },
  title: {
    fontSize: 28,
    ...theme.fw500,
    lineHeight: 32,
    letterSpacing: 0,
    color: theme.colors.neutralWhite,
  },
  addButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 16,
    paddingVertical: 7,
    borderRadius: 9999,
  },
  addButtonText: {
    color: theme.colors.neutralBackground,
    fontSize: 12,
    ...theme.fw600,
    lineHeight: 24,
  },
  listContainer: {
    flex: 1,
  },
  listContent: {
    paddingBottom: 20,
  },
  separator: {
    paddingHorizontal: 24,
    paddingVertical: 24,
    backgroundColor: theme.colors.background,
  },
  separatorLine: {
    flex: 1,
    height: 1,
    backgroundColor: theme.colors.whiteOpacity10,
  },
  headerButton: {
    marginHorizontal: 24,
  },
  loadingContainer: {},
  loadingText: {
    fontSize: 16,
    color: theme.colors.neutralWhite,
  },
  emptyContainer: {},
  emptyText: {
    fontSize: 18,
    ...theme.fw500,
    color: theme.colors.neutralWhite,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: theme.colors.neutralWhite,
    opacity: 0.7,
    textAlign: 'center',
  },
  backButtonText: {
    color: theme.colors.neutralWhite,
  },
}));
