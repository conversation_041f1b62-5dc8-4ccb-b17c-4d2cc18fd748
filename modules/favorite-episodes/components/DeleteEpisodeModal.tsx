import BottomModal from '@/components/BottomModal';
import { ThemedText } from '@/components/ThemedText';
import { CustomButton } from '@/components/ui/CustomButton';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { useCallback } from 'react';

type DeleteEpisodeModalProps = {
  visible: boolean;
  onClose: () => void;
  onDelete: () => void;
  isPendingDelete?: boolean;
  episodeTitle?: string;
};

function DeleteEpisodeModal({
  visible,
  onClose,
  onDelete,
  isPendingDelete = false,
  episodeTitle,
}: DeleteEpisodeModalProps) {
  const { styles } = useStyles(stylesheet);

  const handleDelete = useCallback(() => {
    onDelete();
  }, [onDelete]);

  const handleClose = useCallback(() => {
    onClose();
  }, [onClose]);

  return (
    <BottomModal isVisible={visible} onClose={onClose}>
      <View style={styles.container}>
        {episodeTitle ? (
          <ThemedText type='subtitleMedium' style={styles.title}>
            Are you sure you want to delete
            <ThemedText type='subtitleMedium' style={styles.episodeTitle}>
              {' '}
              {episodeTitle}{' '}
            </ThemedText>
            from your favorite episodes?
          </ThemedText>
        ) : (
          <ThemedText type='subtitleMedium' style={styles.title}>
            Are you sure you want to delete this episode from your favorites?
          </ThemedText>
        )}

        <CustomButton type='danger' onPress={handleDelete} style={{ marginBottom: 14 }} isLoading={isPendingDelete}>
          Yes
        </CustomButton>

        <CustomButton type='outlined' onPress={handleClose}>
          Cancel
        </CustomButton>
      </View>
    </BottomModal>
  );
}

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    padding: 24,
  },
  episodeTitle: {
    color: theme.colors.neutralWhite,
    marginBottom: 28,
    ...theme.fw600,
    fontSize: 20,
    letterSpacing: 0,
  },
  title: {
    color: '#FFFFFFCC',
    fontSize: 20,
    marginBottom: 28,
    ...theme.fw500,
    letterSpacing: 0,
  },
}));

export default DeleteEpisodeModal;
