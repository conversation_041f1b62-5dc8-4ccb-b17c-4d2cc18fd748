import { View, TouchableOpacity, Animated } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { ThemedText } from '@/components/ThemedText';
import React, { useCallback } from 'react';
import * as Haptics from 'expo-haptics';
import DeleteOutline from '@/assets/icons/delete-outline';
import { Icons } from '@/assets/icons';
import { IFavoriteEpisode } from '@/apis/favorite';
import { useReorderableDrag } from 'react-native-reorderable-list';
import { episodeDetailDirect } from '@/utils/router-prefetch';
import { useQueryClient } from '@tanstack/react-query';
import { ExpoImage } from '@/components/ui/Image';
import bigDecimal from 'js-big-decimal';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';

interface EpisodeItemProps {
  item: IFavoriteEpisode;
  index?: number;
  onRemove: (id: number, title: string) => void;
  isOwner?: boolean;
}

export const EpisodeItem = React.memo(({ item, index, onRemove, isOwner }: EpisodeItemProps) => {
  const { styles, theme } = useStyles(itemStylesheet);
  const queryClient = useQueryClient();
  const { onCheckAccountRestricted } = useCheckRestrictAccount();

  const drag = useReorderableDrag();

  const handleRemove = useCallback(() => {
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    onRemove(item.id, item.title);
  }, [item.id, item.title, onRemove, onCheckAccountRestricted]);

  const handleDragStart = useCallback(() => {
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    drag();
  }, [drag, onCheckAccountRestricted]);

  const handleEpisodePress = useCallback(async () => {
    await episodeDetailDirect(queryClient, item?.episodeId?.toString());
  }, [item?.episodeId, queryClient]);

  const userRate = bigDecimal.round(item?.userRate, 1, bigDecimal.RoundingModes.HALF_UP);
  const avgRate = item.avgRate != null ? bigDecimal.round(item?.avgRate, 1, bigDecimal.RoundingModes.HALF_UP) : '0.0';

  return (
    <TouchableOpacity activeOpacity={0.7} onPress={handleEpisodePress}>
      <Animated.View style={[styles.container]}>
        <View style={styles.numberContainer}>
          <ThemedText style={styles.numberText}>{(index ?? 0) + 1}</ThemedText>
        </View>

        <ExpoImage source={{ uri: item.imageUrl }} style={styles.episodeImage} />

        <View style={styles.episodeInfoContainer}>
          <View style={styles.episodeInfo}>
            <ThemedText style={styles.episodeTitle} numberOfLines={2}>
              {item.title || ''}
            </ThemedText>

            <ThemedText style={styles.podcastTitle} numberOfLines={1}>
              {item.podcastTitle || ''}
            </ThemedText>

            <View style={styles.ratingContainer}>
              <Icons.StarRateFill color={theme.colors.stateWarning} width={16} height={16} />
              <ThemedText style={styles.ratingText}>{avgRate}</ThemedText>
              {item.userRate != null && (
                <>
                  <Icons.StarRateFill color={theme.colors.primary} width={16} height={16} />
                  <ThemedText style={styles.reviewCountText}>{userRate}</ThemedText>
                </>
              )}
            </View>
          </View>

          {isOwner && (
            <View style={styles.actionsContainer}>
              <TouchableOpacity
                onPressIn={handleDragStart}
                style={styles.dragHandle}
                hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
                activeOpacity={0.7}
              >
                <Icons.DotDrag color={theme.colors.neutralLightGrey} size={24} />
              </TouchableOpacity>

              <TouchableOpacity onPress={handleRemove} style={styles.actionButton} activeOpacity={0.7}>
                <DeleteOutline color={theme.colors.stateError} width={20} height={20} />
              </TouchableOpacity>
            </View>
          )}
        </View>
      </Animated.View>
    </TouchableOpacity>
  );
});

const itemStylesheet = createStyleSheet((theme) => ({
  container: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingHorizontal: 24,
    backgroundColor: theme.colors.background,
  },
  numberContainer: {
    width: 44,
    height: 44,
    alignItems: 'center',
    marginRight: 12,
    borderRadius: 1111,
    backgroundColor: theme.colors.neutralCard,
    padding: 10,
  },
  numberText: {
    fontSize: 14,
    ...theme.fw500,
    color: theme.colors.neutralWhite,
  },
  episodeImage: {
    width: 44,
    height: 44,
    borderRadius: 8,
    marginRight: 16,
  },
  episodeInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  episodeTitle: {
    fontSize: 16,
    ...theme.fw500,
    color: theme.colors.neutralWhite,
    marginBottom: 4,
    lineHeight: 24,
  },
  podcastTitle: {
    fontSize: 14,
    color: theme.colors.neutralLightGrey,
    marginBottom: 4,
    lineHeight: 20,
    ...theme.fw500,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  ratingText: {
    fontSize: 12,
    color: theme.colors.neutralWhite,
    ...theme.fw500,
  },
  reviewCountText: {
    fontSize: 12,
    color: theme.colors.neutralLightGrey,
  },
  actionsContainer: {
    flexDirection: 'column',
    alignItems: 'flex-end',
  },
  actionButton: {
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  dragHandle: {
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    flex: 1,
  },
  episodeInfoContainer: {
    flex: 1,
    justifyContent: 'flex-start',
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
}));
