import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { AccountTabs } from './components/AccountTabs';

type Props = {};
export const Account = (props: Props) => {
  const { styles } = useStyles(stylesheet);

  return (
    <View style={styles.container}>
      <AccountTabs />
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.neutralBackground,
  },
}));
