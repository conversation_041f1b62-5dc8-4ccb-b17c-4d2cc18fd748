import { Icons } from '@/assets/icons';
import { Spacer } from '@/components/Spacer';
import { getItemSizeFlatList } from '@/utils/func';
import { useCallback } from 'react';
import { router } from 'expo-router';
import { FlatList, ListRenderItem, TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { FavoriteImage } from './FavoriteImage';
import { TitleAction } from './TitleAction';
import { IFavoriteEpisode, useGetFavoriteEpisodesInfiniteQuery } from '@/apis/favorite';
import { Skeleton } from 'moti/skeleton';
import { useIsYou } from '@/hooks/useIsYou';
import { AddItem } from './about/AddItem';
import { episodeDetailDirect } from '@/utils/router-prefetch';
import { useQueryClient } from '@tanstack/react-query';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';

type Props = {
  userId?: string | number;
};

const itemSize = getItemSizeFlatList(24, 3, 11);

export const FavoriteEpisodesList = ({ userId }: Props) => {
  const { styles } = useStyles(stylesheet);
  const queryClient = useQueryClient();
  const { onCheckAccountRestricted } = useCheckRestrictAccount();

  const isYou = useIsYou({
    userId: (userId || '')?.toString(),
  });

  const { data: favoriteEpisodesData, isPending: isPendingFavoriteEpisodes } = useGetFavoriteEpisodesInfiniteQuery(
    { userId: Number(userId || 0), limit: 3 },
    { enabled: Number(userId || 0) > 0 } as any
  );

  const favoriteEpisodes = favoriteEpisodesData?.pages?.flatMap((page) => page.data) ?? [];

  const handleShowFavoriteEpisodes = () => {
    router.push({
      pathname: `/(app)/favorite-episodes`,
      params: {
        userId,
      },
    });
  };

  const handleDirect = useCallback(
    (item: IFavoriteEpisode) => {
      episodeDetailDirect(queryClient, item.episodeId.toString());
    },
    [queryClient]
  );

  const renderItem = useCallback<ListRenderItem<IFavoriteEpisode>>(
    ({ item }) => <FavoriteImage onPress={() => handleDirect(item)} image={item.imageUrl} itemSize={itemSize} />,
    [handleDirect]
  );

  const keyExtractor = useCallback((item: IFavoriteEpisode) => item.id.toString(), []);

  const renderSkeleton = useCallback(() => {
    return (
      <>
        <Skeleton width={itemSize} height={itemSize} radius={8} />

        <Skeleton width={itemSize} height={itemSize} radius={8} />

        <Skeleton width={itemSize} height={itemSize} radius={8} />
      </>
    );
  }, []);

  const handleDirectAddEpisode = useCallback(() => {
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    router.push({
      pathname: '/(app)/discover-search',
      params: { searchAction: 'add-episodes' },
    });
  }, [onCheckAccountRestricted]);

  const countAddItems = 3 - (favoriteEpisodes?.length ?? 0);
  const renderAddItems = useCallback(() => {
    return (
      <>
        {Array(countAddItems)
          .fill(0)
          .map((_, index) => (
            <AddItem onPress={handleDirectAddEpisode} key={index} itemSize={itemSize} />
          ))}
      </>
    );
  }, [countAddItems, handleDirectAddEpisode]);

  if (!isYou && !isPendingFavoriteEpisodes && favoriteEpisodes?.length === 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      <TitleAction
        label={'Favorite Episodes'}
        rightAction={
          userId ? (
            <TouchableOpacity activeOpacity={0.7} onPress={handleShowFavoriteEpisodes}>
              {isYou ? <Icons.Edit size={24} /> : <Icons.ArrowRight size={24} />}
            </TouchableOpacity>
          ) : null
        }
      />

      <Spacer height={16} />

      <FlatList
        horizontal
        bounces={false}
        contentContainerStyle={styles.listContainer}
        showsHorizontalScrollIndicator={false}
        data={favoriteEpisodes}
        renderItem={renderItem}
        ListHeaderComponent={isPendingFavoriteEpisodes ? renderSkeleton : null}
        ListHeaderComponentStyle={styles.skeletonContainer}
        keyExtractor={keyExtractor}
        ListFooterComponent={isYou && countAddItems > 0 ? renderAddItems : null}
        ListFooterComponentStyle={styles.skeletonContainer}
      />

      <Spacer height={32} />
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {},
  listContainer: {
    gap: 11,
  },
  skeletonContainer: {
    flexDirection: 'row',
    gap: 11,
  },
}));
