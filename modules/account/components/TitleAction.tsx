import { ThemedText } from '@/components/ThemedText';
import { ReactNode } from 'react';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  label: string;
  rightAction?: ReactNode;
};

export const TitleAction = ({ label, rightAction }: Props) => {
  const { styles } = useStyles(stylesheet);

  return (
    <View style={styles.container}>
      <ThemedText type='subtitleMedium'>{label}</ThemedText>

      {rightAction}
    </View>
  );
};

const stylesheet = createStyleSheet(() => ({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 1,
  },
}));
