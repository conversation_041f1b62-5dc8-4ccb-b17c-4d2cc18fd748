import MaskedView from '@react-native-masked-view/masked-view';
import { ImageBackground } from 'expo-image';
import { LinearGradient } from 'expo-linear-gradient';
import { PropsWithChildren } from 'react';
import { Text, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {};

export const PatronContainer = ({ children }: PropsWithChildren<Props>) => {
  const { styles } = useStyles(stylesheet);

  return (
    <ImageBackground source={require('@/assets/images/bg_profile_founder.png')} style={styles.gradientContainer}>
      <MaskedView
        style={styles.maskedViewContainer}
        maskElement={
          <View style={styles.textMaskContainer}>
            <Text style={styles.textMask}>PATRON</Text>
          </View>
        }
      >
        <LinearGradient
          colors={['#FFFFFF04', '#FFFFFF0D']}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1 }}
          style={{ flex: 1 }}
        />
      </MaskedView>

      <View style={styles.container}>{children}</View>
    </ImageBackground>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    paddingTop: rt.insets.top,
    paddingHorizontal: 24,
    flex: 1,
  },
  gradientContainer: {
    minHeight: 180,
    objectFit: 'cover',
  },
  maskedViewContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
  },
  textMaskContainer: {
    backgroundColor: 'transparent',
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  textMask: {
    lineHeight: 97,
    fontSize: 80,
    ...theme.fw900,
  },
}));
