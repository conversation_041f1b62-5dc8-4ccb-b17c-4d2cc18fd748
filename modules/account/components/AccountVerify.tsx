import { useGetProfileQuery } from '@/apis/auth/queries';
import { Icons } from '@/assets/icons';
import { CustomButton } from '@/components/ui/CustomButton';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';
import { router, useLocalSearchParams } from 'expo-router';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  isVerified: boolean;
  isYouTab: boolean;
};

export const AccountVerify = ({ isVerified }: Props) => {
  const { styles, theme } = useStyles(stylesheet);
  const localSearch = useLocalSearchParams<{ userId: string }>();
  const userId = localSearch?.userId;
  const { data: userProfile } = useGetProfileQuery();

  const { onCheckAccountRestricted } = useCheckRestrictAccount();

  const handleUpgrade = () => {
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    router.push({
      pathname: '/(app)/choose-plan',
      params: {
        isAccountUpdate: 'true',
      },
    });
  };

  if (isVerified) return <Icons.Verify size={28} color={theme.colors.primary} />;
  if (userId && userProfile?.id != Number(userId)) return null;

  return (
    <CustomButton
      style={styles.upgradeBtn}
      type='outlined'
      textType='tinyMedium'
      textStyle={styles.upgradeText}
      onPress={handleUpgrade}
    >
      Upgrade
    </CustomButton>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  verifyContainer: {
    position: 'absolute',
    right: 0,
    top: '50%',
    paddingLeft: 12,
    transform: [{ translateX: '100%' }, { translateY: '-50%' }],
  },
  upgradeBtn: {
    minHeight: 28,
    borderColor: theme.colors.primary,
    minWidth: 70,
  },
  upgradeText: {
    fontSize: 10,
    ...theme.fw600,
    lineHeight: 22,
  },
}));
