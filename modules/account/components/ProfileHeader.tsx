import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { PatronContainer } from './PatronContainer';

type Props = {
  isYouTab: boolean;
  isPatron?: boolean;
};

export const ProfileHeader = ({ isYouTab, isPatron }: Props) => {
  const { styles } = useStyles(stylesheet);

  if (isPatron) {
    return <PatronContainer>{/* <AccountHeader isYouTab={isYouTab} /> */}</PatronContainer>;
  }

  return <View style={styles.container}>{/* <AccountHeader isYouTab={isYouTab} /> */}</View>;
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    paddingTop: rt.insets.top,
    paddingHorizontal: 24,
    minHeight: 180,
    position: 'relative',
  },
}));
