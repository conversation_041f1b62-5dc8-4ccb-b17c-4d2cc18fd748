import { ThemedText } from '@/components/ThemedText';
import { ExpoImage } from '@/components/ui/Image';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  podcastImage: string;
  podcastName: string;
  preTitle?: string;
};

export const ReplyPostCast = ({ podcastImage, podcastName, preTitle }: Props) => {
  const { styles } = useStyles(stylesheet);

  return (
    <View style={styles.container}>
      <ExpoImage source={{ uri: podcastImage }} style={styles.image} />

      <View style={styles.fullFlex}>
        <ThemedText type='small' style={styles.showName}>
          {podcastName}
        </ThemedText>

        {preTitle ? (
          <ThemedText type='small' style={styles.showPreTitle}>
            {preTitle}
          </ThemedText>
        ) : null}
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    gap: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  showPreTitle: {
    color: theme.colors.whiteOpacity80,
    ...theme.fw500,
  },
  fullFlex: {
    flex: 1,
  },
  image: {
    borderRadius: 8,
    objectFit: 'cover',
    width: 64,
    height: 64,
  },
  showName: {},
}));

export default ReplyPostCast;
