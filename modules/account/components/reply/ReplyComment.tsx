import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { Avatar } from '@/components/ui/Avatar';
import { useIsYou } from '@/hooks/useIsYou';
import { useMemo } from 'react';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  replierId: string;
  replierUsername: string;
  replierContent: string;
  replierAvatar: string;
};

export const ReplyComment = ({ replierId, replierUsername, replierContent, replierAvatar }: Props) => {
  const { styles } = useStyles(stylesheet);

  const isYou = useIsYou({
    userId: replierId,
  });

  const usernameDisplay = useMemo(() => {
    if (isYou) return 'You';

    return replierUsername;
  }, [replierUsername, isYou]);

  return (
    <View style={styles.container}>
      <Avatar image={replierAvatar} size={48} />

      <Spacer width={24} />

      <View style={styles.replyContainer}>
        <ThemedText type='small' style={[styles.replyUsername, isYou && styles.textPrimary]}>
          {usernameDisplay}
        </ThemedText>

        <Spacer height={8} />

        <ThemedText type='small'>{replierContent}</ThemedText>
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  replyUsername: {
    color: theme.colors.neutralLightGrey,
  },
  textPrimary: {
    color: theme.colors.primary,
  },
  replyContainer: {
    flex: 1,
  },
}));
