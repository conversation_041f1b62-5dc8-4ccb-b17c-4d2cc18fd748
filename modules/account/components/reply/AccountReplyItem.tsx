import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { PostInfo } from '@/modules/reply-post/components/PostInfo';
import { TouchableOpacity } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { ReplyComment } from './ReplyComment';
import ReplyPostCast from './ReplyPostCast';
import { ReplyHistory } from '@/apis/user';
import { format } from 'date-fns';
import { router } from 'expo-router';

type Props = {
  replyItem: ReplyHistory;
};

export const AccountReplyItem = ({ replyItem }: Props) => {
  const { styles } = useStyles(stylesheet);

  const formatTime = format(new Date(replyItem.updatedAt || replyItem.createdAt), 'hh:mm a • MMM dd');

  const handleDirect = () => {
    if (replyItem.type === 'episode') {
      return router.push({
        pathname: '/(app)/episode/[episodeId]/review/[postId]',
        params: {
          episodeId: replyItem.mediaId,
          postId: replyItem.commentId,
          source: replyItem.source,
        },
      });
    }

    router.push({
      pathname: '/(app)/podcast/[podcastId]/review/[postId]',
      params: {
        podcastId: replyItem.mediaId,
        postId: replyItem.commentId,
        source: replyItem.source,
      },
    });
  };

  const handleDirectDetail = () => {
    if (replyItem.type === 'episode') {
      router.push({
        pathname: '/(app)/episode/[episodeId]/detail',
        params: { episodeId: replyItem.mediaId },
      });
      return;
    }

    router.push({
      pathname: '/(app)/podcast/[podcastId]',
      params: { podcastId: replyItem.mediaId },
    });
  };
  return (
    <>
      <TouchableOpacity onPress={handleDirectDetail} activeOpacity={0.7}>
        <ReplyPostCast
          podcastImage={replyItem.mediaImageUrl}
          podcastName={replyItem.mediaTitle}
          preTitle={replyItem.parentMediaTitle}
        />
      </TouchableOpacity>
      <Spacer height={28} />
      <TouchableOpacity onPress={handleDirect} activeOpacity={0.7}>
        <PostInfo
          postInfo={{
            title: replyItem.commentTitle,
            content: replyItem.commentContent,
            user: {
              id: +replyItem.commentUserId,
              username: replyItem.commentUsername,
              avatar: replyItem.commentUserAvatar,
            },
            source: replyItem.source,
          }}
          usernameStyle={styles.username}
        />
        <ReplyComment
          replierId={replyItem.userId}
          replierUsername={replyItem.username}
          replierContent={replyItem.content}
          replierAvatar={replyItem.avatar}
        />
      </TouchableOpacity>

      <Spacer height={20} />

      <ThemedText type='small' style={styles.time}>
        {formatTime}
      </ThemedText>
    </>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  image: {
    borderRadius: 8,
    objectFit: 'cover',
    width: 64,
    height: 64,
  },
  username: {
    color: theme.colors.neutralLightGrey,
  },
  time: {
    color: theme.colors.whiteOpacity56,
  },
}));
