import { Icons } from '@/assets/icons';
import { TouchableOpacity } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  onPress?: () => void;
  itemSize: number;
};

export const AddItem = (props: Props) => {
  const { styles } = useStyles(stylesheet);

  return (
    <TouchableOpacity
      style={[styles.container, { width: props.itemSize, height: props.itemSize }]}
      onPress={props.onPress}
      activeOpacity={0.7}
    >
      <Icons.Plus size={24} color='#fff' />
    </TouchableOpacity>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.neutralCard,
  },
}));
