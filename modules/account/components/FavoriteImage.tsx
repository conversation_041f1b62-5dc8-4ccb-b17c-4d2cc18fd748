import { ExpoImage } from '@/components/ui/Image';
import { TouchableOpacity } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  itemSize: number;
  image: string;
  onPress?: () => void;
};

export const FavoriteImage = ({ itemSize, image, onPress }: Props) => {
  const { styles } = useStyles(stylesheet);

  return (
    <TouchableOpacity
      onPress={onPress}
      activeOpacity={0.7}
      style={[styles.container, { width: itemSize, height: itemSize }]}
    >
      <ExpoImage source={{ uri: image }} style={styles.image} />
    </TouchableOpacity>
  );
};

const stylesheet = createStyleSheet(() => ({
  container: {},
  image: {
    borderRadius: 8,
    flex: 1,
    objectFit: 'cover',
  },
}));
