import { Icons } from '@/assets/icons';
import { router } from 'expo-router';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { FavoriteEpisodesList } from '../FavoriteEpisodesList';
import { FavoriteShowsList } from '../FavoriteShowsList';
import { TouchLink } from '../TouchLink';
import { IUserProfileById } from '@/apis/user';
import { memo } from 'react';
import { TabScrollView } from '@/components/collapsing-tabs';
import { useQueryClient } from '@tanstack/react-query';
import queryKeys from '@/utils/queryKeys';
import { BOTTOM_TAB_HEIGHT } from '@/utils/const';

type Props = {
  profile?: IUserProfileById;
};

export const AboutTab = memo(({ profile }: Props) => {
  const { styles } = useStyles(stylesheet);
  const queryClient = useQueryClient();

  const handleUpVotePress = () => {
    if (!profile?.id) return;

    router.push({
      pathname: '/(app)/[userId]/account-upvote',
      params: {
        userId: profile?.id,
      },
    });
  };

  const handleLikesPress = () => {
    if (!profile?.id) return;

    router.push({
      pathname: '/(app)/[userId]/account-likes',
      params: {
        userId: profile?.id,
      },
    });
  };

  const handleReviewsPress = () => {
    if (!profile?.id) return;

    router.push({
      pathname: '/(app)/[userId]/account-reviews',
      params: {
        userId: profile?.id,
      },
    });
  };

  const handleWatchlistPress = () => {
    if (!profile?.id) return;

    router.push({
      pathname: '/(app)/[userId]/account-watchlist',
      params: {
        userId: profile?.id,
      },
    });
  };

  const handleWatchedPress = () => {
    if (!profile?.id) return;

    router.push({
      pathname: '/(app)/[userId]/account-watched',
      params: {
        userId: profile?.id,
      },
    });
  };

  const handleRefetch = async () => {
    await Promise.all([
      queryClient.invalidateQueries({ queryKey: queryKeys.auth.profile() }),
      queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.byUserId(profile?.id ?? '') }),
      queryClient.resetQueries({ queryKey: queryKeys.favorites.podcastsInfinite() }),
      queryClient.resetQueries({ queryKey: queryKeys.favorites.episodesInfinite() }),
    ]);
  };

  return (
    <TabScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      showsVerticalScrollIndicator={false}
      onRefresh={handleRefetch}
    >
      <FavoriteShowsList userId={profile?.id} />

      <FavoriteEpisodesList userId={profile?.id} />

      <View style={styles.linksContainer}>
        <TouchLink label='Likes' icon={<Icons.HeartLikeOutline size={24} />} onPress={handleLikesPress} />

        <View style={styles.indicatorStyle} />

        <TouchLink label='Upvote' icon={<Icons.ArrowUpCircleIcon size={24} />} onPress={handleUpVotePress} />

        <View style={styles.indicatorStyle} />

        <TouchLink label='Reviews' icon={<Icons.StarRateOutlineV2 size={24} />} onPress={handleReviewsPress} />

        <View style={styles.indicatorStyle} />

        <TouchLink label='Watchlist' icon={<Icons.BookmarkOutline size={24} />} onPress={handleWatchlistPress} />

        <View style={styles.indicatorStyle} />

        <TouchLink label='Watched' icon={<Icons.CheckCircleOutline size={24} />} onPress={handleWatchedPress} />
      </View>
    </TabScrollView>
  );
});

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    paddingTop: 24,
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingBottom: BOTTOM_TAB_HEIGHT,
  },
  linksContainer: {
    gap: 24,
    paddingBottom: 48,
  },
  indicatorStyle: {
    backgroundColor: theme.colors.whiteOpacity10,
    width: '100%',
    height: 1,
  },
}));
