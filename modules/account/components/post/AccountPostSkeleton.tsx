import { Spacer } from '@/components/Spacer';
import { Skeleton } from 'moti/skeleton';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {};

export const AccountPostSkeleton = (props: Props) => {
  const { styles } = useStyles(stylesheet);

  return (
    <View>
      <View style={styles.rowHCenter}>
        <Skeleton width={64} height={64} radius={8} />

        <Spacer width={16} />

        <View style={styles.fullFlex}>
          <Skeleton width={'70%'} height={24} />

          <Spacer height={4} />

          <Skeleton width={'90%'} height={24} />
        </View>
      </View>

      <Spacer height={16} />

      <View style={styles.rowHCenter}>
        <Skeleton width={16 * 5} height={16} radius={8} />

        <Spacer width={12} />

        <Skeleton width={16} height={16} radius={8} />
      </View>

      <Spacer height={16} />

      <Skeleton width={'70%'} height={24} />

      <Spacer height={8} />

      <Skeleton width={'100%'} height={14} />

      <Spacer height={6} />

      <Skeleton width={'90%'} height={14} />

      <Spacer height={6} />

      <Skeleton width={'80%'} height={14} />
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {},
  rowHCenter: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  fullFlex: {
    flex: 1,
  },
}));
