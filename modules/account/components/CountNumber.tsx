import { ThemedText } from '@/components/ThemedText';
import { formatCompactNumber } from '@/utils/func';
import { TouchableOpacity } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  label: string;
  count: number;
  onPress?: () => void;
};

export const CountNumber = ({ label, count, onPress }: Props) => {
  const { styles } = useStyles(stylesheet);

  return (
    <TouchableOpacity activeOpacity={0.7} onPress={onPress} style={styles.container}>
      <ThemedText type='defaultBold' style={styles.count}>
        {formatCompactNumber(count)}
      </ThemedText>

      <ThemedText style={styles.label} type='tinyMedium'>
        {label}
      </ThemedText>
    </TouchableOpacity>
  );
};
const stylesheet = createStyleSheet((theme) => ({
  container: {
    flexDirection: 'column',
    alignItems: 'center',
  },
  label: {
    color: theme.colors.neutralLightGrey,
  },
  count: {
    ...theme.fw700,
  },
}));
