import { Icons } from '@/assets/icons';
import { Spacer } from '@/components/Spacer';
import { getItemSizeFlatList } from '@/utils/func';
import { router } from 'expo-router';
import { useCallback } from 'react';
import { FlatList, ListRenderItem, TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { FavoriteImage } from './FavoriteImage';
import { TitleAction } from './TitleAction';
import { IFavoritePodcast, useGetFavoritePodcastsInfiniteQuery } from '@/apis/favorite';
import { Skeleton } from 'moti/skeleton';
import { useIsYou } from '@/hooks/useIsYou';
import { AddItem } from './about/AddItem';
import { showDetailDirect } from '@/utils/router-prefetch';
import { useQueryClient } from '@tanstack/react-query';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';

type Props = {
  userId?: string | number;
};

const itemSize = getItemSizeFlatList(24, 3, 11);

export const FavoriteShowsList = ({ userId }: Props) => {
  const { styles } = useStyles(stylesheet);
  const queryClient = useQueryClient();
  const { onCheckAccountRestricted } = useCheckRestrictAccount();

  const isYou = useIsYou({
    userId: (userId || '')?.toString(),
  });

  const { data: favoritePodcastsData, isPending: isPendingFavoritePodcasts } = useGetFavoritePodcastsInfiniteQuery(
    { userId: Number(userId || 0), limit: 3 },
    { enabled: Number(userId || 0) > 0 } as any
  );

  const favoritePodcasts = favoritePodcastsData?.pages?.flatMap((page) => page.data) ?? [];

  const handleShowFavoriteShows = () => {
    router.push({
      pathname: `/(app)/favorite-shows`,
      params: {
        userId,
      },
    });
  };

  const handleDirect = useCallback(
    (item: IFavoritePodcast) => {
      showDetailDirect(queryClient, item.podcastId.toString());
    },
    [queryClient]
  );

  const renderItem = useCallback<ListRenderItem<IFavoritePodcast>>(
    ({ item }) => <FavoriteImage onPress={() => handleDirect(item)} image={item.imageUrl} itemSize={itemSize} />,
    [handleDirect]
  );

  const keyExtractor = useCallback((item: IFavoritePodcast) => item.id.toString(), []);

  const renderSkeleton = useCallback(() => {
    return (
      <>
        <Skeleton width={itemSize} height={itemSize} radius={8} />

        <Skeleton width={itemSize} height={itemSize} radius={8} />

        <Skeleton width={itemSize} height={itemSize} radius={8} />
      </>
    );
  }, []);

  const handleDirectAddShow = useCallback(() => {
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    router.push({
      pathname: '/(app)/discover-search',
      params: { searchAction: 'add-shows' },
    });
  }, [onCheckAccountRestricted]);

  const countAddItems = 3 - (favoritePodcasts?.length ?? 0);
  const renderAddItems = useCallback(() => {
    return (
      <>
        {Array(countAddItems)
          .fill(0)
          .map((_, index) => (
            <AddItem onPress={handleDirectAddShow} key={index} itemSize={itemSize} />
          ))}
      </>
    );
  }, [countAddItems, handleDirectAddShow]);

  if (!isYou && !isPendingFavoritePodcasts && favoritePodcasts?.length === 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      <TitleAction
        label={'Favorite Shows'}
        rightAction={
          userId ? (
            <TouchableOpacity activeOpacity={0.7} onPress={handleShowFavoriteShows}>
              {isYou ? <Icons.Edit size={24} /> : <Icons.ArrowRight size={24} />}
            </TouchableOpacity>
          ) : null
        }
      />

      <Spacer height={16} />

      <FlatList
        horizontal
        bounces={false}
        data={favoritePodcasts}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.contentContainer}
        ListHeaderComponent={isPendingFavoritePodcasts ? renderSkeleton : null}
        ListHeaderComponentStyle={styles.skeletonContainer}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        ListFooterComponent={isYou && countAddItems > 0 ? renderAddItems : null}
        ListFooterComponentStyle={styles.skeletonContainer}
      />

      <Spacer height={32} />
    </View>
  );
};

const stylesheet = createStyleSheet(() => ({
  container: {},
  contentContainer: {
    gap: 11,
  },
  skeletonContainer: {
    flexDirection: 'row',
    gap: 11,
  },
}));
