import { Icons } from '@/assets/icons';
import { CustomButton } from '@/components/ui/CustomButton';
import { Header } from '@/components/ui/Header';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';
import { router } from 'expo-router';
import { TouchableOpacity } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  isYouTab: boolean;
};

export const AccountHeader = ({ isYouTab }: Props) => {
  const { styles } = useStyles(stylesheet);
  const { onCheckAccountRestricted } = useCheckRestrictAccount();

  const handleEditProfile = () => {
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    router.push('/(app)/account-update');
  };

  const handleDirectSetting = () => {
    router.push({
      pathname: '/(app)/settings',
    });
  };

  return (
    <Header
      isBack={!isYouTab}
      leftAction={
        <TouchableOpacity onPress={handleDirectSetting}>
          <Icons.Setting size={24} />
        </TouchableOpacity>
      }
      title=''
      rightAction={
        isYouTab ? (
          <CustomButton
            textType={'tinyMedium'}
            type='primaryOpacity10'
            style={styles.editProfile}
            onPress={handleEditProfile}
          >
            Edit Profile
          </CustomButton>
        ) : null
      }
    />
  );
};

const stylesheet = createStyleSheet(() => ({
  editProfile: {
    paddingHorizontal: 16,
    minHeight: 38,
  },
}));
