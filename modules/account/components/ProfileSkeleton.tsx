import { Spacer } from '@/components/Spacer';
import { Skeleton } from 'moti/skeleton';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {};

export const ProfileSkeleton = (props: Props) => {
  const { styles } = useStyles(stylesheet);

  return (
    <View style={styles.container}>
      <Spacer height={130} />

      <Skeleton width={100} height={100} radius={999} />

      <Spacer height={28} />

      <Skeleton width={150} height={28} />

      <Spacer height={12} />

      <Skeleton width={120} height={38} />

      <Spacer height={28} />

      <View style={styles.rowCenter}>
        <Skeleton width={70} height={40} />
        <Spacer width={20} />
        <Skeleton width={70} height={40} />
        <Spacer width={20} />
        <Skeleton width={70} height={40} />
      </View>

      <Spacer height={28} />

      <Skeleton width={150} height={16} />
    </View>
  );
};

const stylesheet = createStyleSheet(() => ({
  container: {
    alignItems: 'center',
  },
  commentBox: {
    flex: 1,
    gap: 4,
  },
  rowCenter: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
}));
