import { ThemedText } from '@/components/ThemedText';
import { ReactNode } from 'react';
import { TouchableOpacity } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  label: string;
  icon: ReactNode;
  onPress?: () => void;
};

export const TouchLink = ({ label, icon, onPress }: Props) => {
  const { styles } = useStyles(stylesheet);

  return (
    <TouchableOpacity style={styles.container} activeOpacity={0.7} onPress={onPress}>
      {icon}

      <ThemedText type='defaultMedium'>{label}</ThemedText>
    </TouchableOpacity>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 24,
  },
}));
