import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { Header } from '@/components/ui/Header';
import { SocialsGridList } from '@/modules/signup/components/SocialsGridList';
import { router } from 'expo-router';
import { ScrollView, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { SignInEmailForm } from '../components/SignInEmailForm';
import { SignInPhone } from '../components/SignInPhone';

export default function () {
  const { styles } = useStyles(stylesheet);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.box} contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
        <View>
          <Header />

          <Spacer height={48} />

          <ThemedText style={[styles.welcome, styles.textCenter]}>Welcome to Rabid</ThemedText>

          <Spacer height={16} />

          <ThemedText style={styles.textCenter} type='title'>
            Sign In to Your Account
          </ThemedText>

          <Spacer height={40} />

          <SignInEmailForm />

          <Spacer height={40} />

          <View style={styles.orBox}>
            <View style={styles.orLine} />

            <ThemedText type='tinyMedium'>Or Using</ThemedText>

            <View style={styles.orLine} />
          </View>

          <Spacer height={24} />

          <SignInPhone />

          <Spacer height={20} />

          <SocialsGridList />
        </View>

        <View style={styles.footer}>
          <ThemedText type='small'>Don't Have an Account?</ThemedText>
          <TouchableOpacity onPress={() => router.replace('/sign-up')} activeOpacity={0.7}>
            <ThemedText style={styles.signUpText}>Sign Up</ThemedText>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const stylesheet = createStyleSheet((theme) => ({
  container: {
    backgroundColor: theme.colors.neutralBackground,
    flex: 1,
  },
  box: {
    flex: 1,
  },
  welcome: {
    opacity: 0.56,
  },
  textCenter: {
    textAlign: 'center',
  },
  scrollContent: {
    flexGrow: 1,
    flexDirection: 'column',
    justifyContent: 'space-between',
    paddingTop: 4,
    paddingBottom: 32,
    paddingHorizontal: 24,
    gap: 24,
  },
  footer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 'auto',
    flexDirection: 'row',
    gap: 8,
  },
  signUpText: {
    ...theme.fw500,
    color: theme.colors.primary,
    fontSize: 14,
  },
  orBox: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 24,
  },
  orLine: {
    flex: 1,
    height: 2,
    backgroundColor: theme.colors.neutralGrey,
  },
}));
