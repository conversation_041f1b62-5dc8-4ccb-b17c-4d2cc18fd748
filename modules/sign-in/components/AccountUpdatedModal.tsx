import { useCreateNewAccountMutation } from '@/apis/auth/mutations';
import { ConfirmationModal } from '@/components/ui/ConfirmationModal';
import { useUserStore } from '@/store/user';
import { router } from 'expo-router';

type Props = {
  isVisible: boolean;
  onClose: () => void;
  token: string;
  provider: 'google' | 'facebook' | 'apple' | 'email' | 'spotify' | 'phone';
  type?: 'authentication' | 'access_token';
};

const AccountUpdatedModal = ({ isVisible, onClose, token, provider, type }: Props) => {
  const { mutateAsync: createNewAccount } = useCreateNewAccountMutation();
  const setAccessToken = useUserStore.use.setAccessToken();
  const setRefreshToken = useUserStore.use.setRefreshToken();

  const handleCreateNewAccount = async () => {
    const res = await createNewAccount({
      token,
      provider,
      type,
    });

    if (res?.user && res?.tokens) {
      setAccessToken(res.tokens.accessToken);
      setRefreshToken(res.tokens.refreshToken);

      onClose();
      router.replace('/(app)/(tabs)');
    }
  };

  return (
    <ConfirmationModal
      isVisible={isVisible}
      onClose={onClose}
      onCancel={() => {
        onClose();
        router.dismissAll();
      }}
      onConfirm={handleCreateNewAccount}
      title='Your email account has been updated'
      description='Please sign in using the Google/Social account linked with your primary email or create a new account with this login method'
      cancelText='Back to Sign In'
      confirmText='Create Account'
    />
  );
};

export default AccountUpdatedModal;
