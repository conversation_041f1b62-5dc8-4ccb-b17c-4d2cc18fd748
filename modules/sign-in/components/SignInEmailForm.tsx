import { useLoginMutation } from '@/apis/auth';
import { ThemedText } from '@/components/ThemedText';
import { Button } from '@/components/ui/Button';
import TextInput from '@/components/ui/TextInput';
import { useUserStore } from '@/store/user';
import { toastError, toastSuccess } from '@/utils/toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { useQueryClient } from '@tanstack/react-query';
import { Link, router } from 'expo-router';
import { Controller, useForm } from 'react-hook-form';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { SignInEmailFormData, signInEmailSchema } from '../with-email/schema';
import queryKeys from '@/utils/queryKeys';
import { useGetProfileQuery } from '@/apis/auth/queries';

type Props = {};

export const SignInEmailForm = (props: Props) => {
  const { styles } = useStyles(stylesheet);
  const { control, handleSubmit, errors, onSubmit, isSigningIn } = useSignInPhone();

  return (
    <View style={styles.container}>
      <Controller
        control={control}
        name='email'
        render={({ field: { onChange, onBlur, value } }) => (
          <TextInput
            label='Email'
            placeholder='Input Your Email'
            onChangeText={(value) => onChange(value.replace(/\s/g, ''))}
            onBlur={onBlur}
            value={value}
            autoCapitalize='none'
            error={errors.email?.message}
          />
        )}
      />

      <View style={styles.passwordBox}>
        <Controller
          control={control}
          name='password'
          render={({ field: { onChange, onBlur, value } }) => (
            <TextInput
              label='Password'
              isPassword
              placeholder='Input Your Password'
              onChangeText={onChange}
              onBlur={onBlur}
              value={value}
              autoCapitalize='none'
              error={errors.password?.message}
            />
          )}
        />

        <View style={styles.forgotPassBox}>
          <Link href='/forgot-password'>
            <ThemedText type='small'>Forgot Password</ThemedText>
          </Link>
        </View>
      </View>

      <View style={styles.signInBtnBox}>
        <Button type='default' onPress={handleSubmit(onSubmit)} isLoading={isSigningIn}>
          <ThemedText type='defaultBold' style={styles.signInText}>
            Sign In
          </ThemedText>
        </Button>
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    gap: 28,
  },
  passwordBox: {
    gap: 12,
  },
  forgotPassBox: {
    justifyContent: 'flex-end',
    flexDirection: 'row',
  },
  signInText: {
    color: theme.colors.neutralBackground,
  },
  signInBtnBox: {
    paddingHorizontal: 31,
  },
}));

const useSignInPhone = () => {
  const queryClient = useQueryClient();

  const setAccessToken = useUserStore((state) => state.setAccessToken);
  const setRefreshToken = useUserStore((state) => state.setRefreshToken);
  const { data: userProfile } = useGetProfileQuery();

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<SignInEmailFormData>({
    resolver: zodResolver(signInEmailSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const { mutate: login, isPending: isSigningIn } = useLoginMutation({
    onSuccess: async (data) => {
      setRefreshToken(data.tokens.refreshToken);
      setAccessToken(data.tokens.accessToken);
      await Promise.all([
        queryClient.refetchQueries({ queryKey: queryKeys.auth.profile() }),
        queryClient.refetchQueries({ queryKey: queryKeys.userProfile.byUserId(userProfile?.id || '') }),
      ]);
      toastSuccess({
        description: 'Sign in successfully',
      });

      if (router.canDismiss()) router.dismissAll();
      router.replace('/(app)/(tabs)');
    },
    onError: (error) => {
      toastError(error);
    },
  });

  const onSubmit = (data: SignInEmailFormData) => {
    login({
      provider: 'email',
      subject: data.email,
      password: data.password,
    });
  };

  return { control, handleSubmit, errors, onSubmit, isSigningIn };
};
