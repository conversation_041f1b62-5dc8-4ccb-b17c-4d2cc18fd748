import { ILoginBody, useLoginMutation } from '@/apis/auth';
import { useAddMethodSocialMutation, useUpdateMethodSocialMutation } from '@/apis/user/mutations';
import { Icons } from '@/assets/icons';
import { useUserStore } from '@/store/user';
import { toastError } from '@/utils/toast';
import { router } from 'expo-router';
import { Platform } from 'react-native';
import { AccessToken, AuthenticationToken, LoginManager } from 'react-native-fbsdk-next';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { SignInSocialButton } from './SignInSocialButton';
import { forwardRef, useImperativeHandle, useState } from 'react';
import AccountUpdatedModal from './AccountUpdatedModal';

type Props = {
  isOnlyIcon?: boolean;
  isAddMethodMode?: boolean;
  isChangeMethodMode?: boolean;
  currentProviderType?: string;
  onMethodAdded?: () => void;
  onMethodChanged?: () => void;
  onMethodCancelled?: () => void;
};

export interface SignInFacebookRef {
  onLogin: () => void;
}

export const SignInFacebook = forwardRef<SignInFacebookRef, Props>(
  (
    {
      isOnlyIcon,
      isAddMethodMode = false,
      isChangeMethodMode = false,
      currentProviderType,
      onMethodAdded,
      onMethodChanged,
      onMethodCancelled,
    },
    ref
  ) => {
    const { mutateAsync: loginMutation, isPending: isPendingLogin } = useLoginMutation();
    const { mutateAsync: addMethodMutation, isPending: isPendingAddMethod } = useAddMethodSocialMutation();
    const { mutateAsync: updateMethodMutation, isPending: isPendingUpdateMethod } = useUpdateMethodSocialMutation();

    const setAccessToken = useUserStore.use.setAccessToken();
    const setRefreshToken = useUserStore.use.setRefreshToken();
    const { styles } = useStyles(stylesheet);

    const [isAccountUpdatedModalVisible, setIsAccountUpdatedModalVisible] = useState(false);
    const [accountUpdatedToken, setAccountUpdatedToken] = useState<string>('');
    const [accountUpdatedType, setAccountUpdatedType] = useState<'authentication' | 'access_token'>('authentication');

    const handleFacebookSignInWithLogout = async () => {
      // Logout first to allow user to select different account
      LoginManager.logOut();
      await handleFacebookSignIn();
    };

    useImperativeHandle(ref, () => ({
      onLogin: handleFacebookSignInWithLogout,
    }));

    const signIn = async (idToken: string, type: ILoginBody['type']) => {
      if (isAddMethodMode) {
        await addMethodMutation({
          provider: 'facebook',
          token: idToken,
          type: type as 'authentication' | 'access_token',
        });
        onMethodAdded?.();
      } else if (isChangeMethodMode) {
        await updateMethodMutation({
          provider: 'facebook',
          token: idToken,
          type: type as 'authentication' | 'access_token',
        });
        onMethodChanged?.();
      } else {
        const loginResult = await loginMutation({
          provider: 'facebook',
          token: idToken,
          type,
        });
        const { user, tokens } = loginResult;
        setRefreshToken(tokens.refreshToken);
        setAccessToken(tokens.accessToken);

        if (router.canDismiss()) router.dismissAll();
        if (user.status === 'complete') {
          router.replace({ pathname: '/(app)/(tabs)' });
        } else if (user.status === 'update_profile') {
          router.replace({ pathname: '/(app)/update-info' });
        } else if (user.status === 'plan_payment') {
          router.replace({ pathname: '/(app)/choose-plan' });
        } else if (user.status === 'choose_interest') {
          router.replace({ pathname: '/(app)/choose-interest' });
        } else if (user.status === 'choose_podcast') {
          router.replace({ pathname: '/(app)/choose-podcast' });
        }
      }
    };

    const handleFacebookSignIn = async () => {
      let authenticationToken = '';
      let accessToken = '';
      try {
        const result = await LoginManager.logInWithPermissions(['public_profile'], 'limited');
        if (result.isCancelled) {
          if (isChangeMethodMode || isAddMethodMode) {
            onMethodCancelled?.();
          }
          return;
        }

        if (Platform.OS === 'ios') {
          // This token **cannot** be used to access the Graph API.
          // https://developers.facebook.com/docs/facebook-login/limited-login/
          const result = await AuthenticationToken.getAuthenticationTokenIOS();
          if (!result?.authenticationToken) throw new Error('Authentication denied');
          authenticationToken = result.authenticationToken;
          return await signIn(result?.authenticationToken, 'authentication');
        } else {
          // This token can be used to access the Graph API.
          const result = await AccessToken.getCurrentAccessToken();

          if (!result?.accessToken) throw new Error('Authentication denied');
          accessToken = result.accessToken;
          return await signIn(result?.accessToken, 'access_token');
        }
      } catch (error: any) {
        if (error.message == 'Your email account has been updated') {
          if (Platform.OS === 'ios') {
            if (authenticationToken) {
              setAccountUpdatedToken(authenticationToken);
              setAccountUpdatedType('authentication');
            }
          } else {
            if (accessToken) {
              setAccountUpdatedToken(accessToken);
              setAccountUpdatedType('access_token');
            }
          }
          setIsAccountUpdatedModalVisible(true);
          return;
        }
        toastError(error);
        if (isChangeMethodMode || isAddMethodMode) {
          onMethodCancelled?.();
        }
      } finally {
        LoginManager.logOut();
      }
    };

    return (
      <>
        <SignInSocialButton
          disabled={isPendingLogin || isPendingAddMethod || isPendingUpdateMethod}
          onPress={handleFacebookSignIn}
          Icon={<Icons.Facebook size={24} />}
          title='Facebook'
          containerStyle={styles.container}
          isOnlyIcon={isOnlyIcon}
          textStyle={styles.text}
        />
        <AccountUpdatedModal
          isVisible={isAccountUpdatedModalVisible}
          onClose={() => setIsAccountUpdatedModalVisible(false)}
          token={accountUpdatedToken}
          provider='facebook'
          type={accountUpdatedType}
        />
      </>
    );
  }
);

SignInFacebook.displayName = 'SignInFacebook';

const stylesheet = createStyleSheet((theme) => ({
  container: {
    backgroundColor: theme.colors.facebook,
  },
  text: {
    color: theme.colors.neutralWhite,
  },
}));
