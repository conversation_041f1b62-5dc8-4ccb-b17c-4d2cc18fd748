import { Show } from '@/components/Show';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { ReactNode } from 'react';
import { TextStyle, TouchableOpacityProps, ViewStyle } from 'react-native';
import { TouchableOpacity } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  Icon: ReactNode;
  title?: string;
  isOnlyIcon?: boolean;
  containerStyle?: ViewStyle;
  textStyle?: TextStyle;
} & TouchableOpacityProps;

export const SignInSocialButton = ({
  containerStyle,
  textStyle,
  disabled,
  onPress,
  Icon,
  title,
  isOnlyIcon,
}: Props) => {
  const { styles } = useStyles(stylesheet);

  return (
    <TouchableOpacity
      disabled={disabled}
      style={[styles.container, isOnlyIcon && styles.styleOnlyIcon, containerStyle]}
      onPress={onPress}
    >
      {Icon}

      <Show when={!isOnlyIcon}>
        <>
          <Spacer width={16} />

          <ThemedText style={[styles.text, textStyle]}>{title}</ThemedText>
        </>
      </Show>
    </TouchableOpacity>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    backgroundColor: theme.colors.neutralWhite,
    flex: 1,
    height: 48,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 49,
    paddingVertical: 12,
    paddingHorizontal: 42.5,
  },
  text: {
    color: theme.colors.neutralBackground,
    ...theme.fw700,
    textAlign: 'center',
  },
  styleOnlyIcon: {
    justifyContent: 'center',
  },
}));
