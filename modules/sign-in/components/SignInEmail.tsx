import { Icons } from '@/assets/icons';
import { router } from 'expo-router';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { SignInSocialButton } from './SignInSocialButton';

type Props = { isOnlyIcon?: boolean };

export const SignInEmail = ({ isOnlyIcon }: Props) => {
  const { styles } = useStyles(stylesheet);

  const handleSignInWithEmail = () => {
    router.replace('/sign-in-with-email');
  };

  return (
    <SignInSocialButton
      onPress={handleSignInWithEmail}
      Icon={<Icons.Email size={24} />}
      title='Email'
      containerStyle={styles.container}
      textStyle={styles.text}
      isOnlyIcon={isOnlyIcon}
    />
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: theme.colors.whiteOpacity24,
  },
  text: {
    color: theme.colors.neutralWhite,
  },
}));
