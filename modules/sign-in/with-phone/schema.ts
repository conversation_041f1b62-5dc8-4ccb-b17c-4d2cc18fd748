import { ZOD_ERRORS } from '@/config/zodError';
import { REGEX } from '@/constants/regex';
import { z } from 'zod';

export const signInPhoneSchema = z.object({
  phone: z
    .string({ message: ZOD_ERRORS.required })
    .trim()
    .min(1, ZOD_ERRORS.required)
    .refine(
      (val) => {
        const hasOnlyValidChars = REGEX.PHONE_CHARS_ONLY.test(val);
        if (!hasOnlyValidChars) {
          return false;
        }

        const looksLikePhone = val.startsWith('+') && REGEX.DIGIT_ONLY.test(val.slice(1));
        const looksLikePhoneWithoutCountryCode = REGEX.DIGIT_ONLY.test(val);

        if (looksLikePhone) {
          const isValidPhone = REGEX.PHONE.test(val);
          // ignore character '+'
          if (val.length < 10 || val.length > 16) {
            return false;
          }
          if (!isValidPhone) {
            return false;
          }
          return true;
        } else if (looksLikePhoneWithoutCountryCode) {
          const isValidPhone = REGEX.PHONE_NO_COUNTRY_CODE.test(val);

          if (!isValidPhone) return false;

          return true;
        }

        return false;
      },
      (val) => {
        const hasOnlyValidChars = REGEX.PHONE_CHARS_ONLY.test(val);
        if (!hasOnlyValidChars) {
          return { message: ZOD_ERRORS.invalidPhoneNumber };
        }

        const looksLikePhone = val.startsWith('+') && REGEX.DIGIT_ONLY.test(val.slice(1));
        const looksLikePhoneWithoutCountryCode = REGEX.DIGIT_ONLY.test(val);

        if (looksLikePhone) {
          const isValidPhone = REGEX.PHONE.test(val);
          // ignore character '+'
          if (val.length < 10 || val.length > 16) {
            return { message: ZOD_ERRORS.phoneNumberLength };
          }
          if (!isValidPhone) {
            return { message: ZOD_ERRORS.invalidPhoneNumber };
          }
        } else if (looksLikePhoneWithoutCountryCode) {
          const isValidPhone = REGEX.PHONE_NO_COUNTRY_CODE.test(val);

          if (!isValidPhone) return { message: ZOD_ERRORS.phoneNumberNoCodeLength };
        }
        return { message: ZOD_ERRORS.invalidPhoneNumber };
      }
    ),
  password: z.string().min(1, ZOD_ERRORS.required),
});

export type SignInPhoneFormData = z.infer<typeof signInPhoneSchema>;
