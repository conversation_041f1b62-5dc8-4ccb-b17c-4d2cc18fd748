import { HeaderStyle } from '@/components/HeaderStyle';
import { Spacer } from '@/components/Spacer';
import { useDevMode } from '@/hooks/useDevMode';
import { router } from 'expo-router';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { TurnOffDevMode } from './components/TurnOffDevMode';
import { EnvTarget } from './components/EnvTarget';
import Animated from 'react-native-reanimated';
import { useHeaderStyleAnimated } from '@/hooks/useHeaderStyleAnimated';

type Props = {};

export const DevMode = (props: Props) => {
  const { styles } = useStyles(stylesheet);

  const { onScroll, scrollY, headerHeight, onHeaderLayout } = useHeaderStyleAnimated();

  const { toggleDevMode, isDevModeOpen, toggleStagingENV, isStagingENV } = useDevMode();

  const handleTurnOffDevMode = () => {
    toggleDevMode(false);
    router.back();
  };

  return (
    <View style={styles.container}>
      <HeaderStyle title='Dev Mode' scrollY={scrollY} onHeaderLayout={onHeaderLayout} />

      <Spacer height={24} />

      <Animated.ScrollView
        style={styles.box}
        onScroll={onScroll}
        contentContainerStyle={{ paddingTop: headerHeight + 24 }}
      >
        <TurnOffDevMode isOpen={isDevModeOpen} onToggle={handleTurnOffDevMode} />

        <Spacer height={24} />

        <EnvTarget isStaging={isStagingENV} onToggleEnv={toggleStagingENV} />
      </Animated.ScrollView>
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.neutralBackground,
    paddingBottom: rt.insets.bottom,
  },
  box: {
    paddingHorizontal: 24,
    gap: 24,
  },
}));
