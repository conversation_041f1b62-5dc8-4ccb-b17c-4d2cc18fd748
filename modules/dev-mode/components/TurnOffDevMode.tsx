import { ThemedText } from '@/components/ThemedText';
import { Switch, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  isOpen: boolean;
  onToggle: () => void;
};

export const TurnOffDevMode = ({ isOpen, onToggle }: Props) => {
  const { styles, theme } = useStyles(stylesheet);

  return (
    <View style={styles.box}>
      <ThemedText type='defaultMedium'>Turn Off Dev Mode</ThemedText>

      <Switch
        trackColor={{ false: theme.colors.neutralLightGrey, true: theme.colors.neutralLightGrey }}
        thumbColor={isOpen ? theme.colors.primary : theme.colors.neutralWhite}
        ios_backgroundColor={theme.colors.neutralLightGrey}
        onValueChange={onToggle}
        value={isOpen}
      />
    </View>
  );
};

const stylesheet = createStyleSheet({
  box: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
});
