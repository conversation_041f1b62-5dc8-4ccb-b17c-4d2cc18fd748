import { ThemedText } from '@/components/ThemedText';
import { useQueryClient } from '@tanstack/react-query';
import { router } from 'expo-router';
import { Switch, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  isStaging: boolean;
  onToggleEnv: (isStaging: boolean) => void;
};

export const EnvTarget = ({ isStaging, onToggleEnv }: Props) => {
  const { styles, theme } = useStyles(stylesheet);
  const queryClient = useQueryClient();

  const handleToggleEnv = (value: boolean) => {
    onToggleEnv(value);
    router.dismissAll();
    queryClient.removeQueries();
  };

  return (
    <View style={styles.box}>
      <ThemedText type='defaultMedium'>Environment STAGING</ThemedText>

      <Switch
        trackColor={{ false: theme.colors.neutralLightGrey, true: theme.colors.neutralLightGrey }}
        thumbColor={isStaging ? theme.colors.primary : theme.colors.neutralWhite}
        ios_backgroundColor={theme.colors.neutralLightGrey}
        onValueChange={handleToggleEnv}
        value={isStaging}
      />
    </View>
  );
};

const stylesheet = createStyleSheet({
  box: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
});
