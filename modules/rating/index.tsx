import { IPodcastRate, useGetPodcastRateListQuery } from '@/apis/podcast';
import { Header } from '@/components/ui/Header';
import queryKeys from '@/utils/queryKeys';
import { useQueryClient } from '@tanstack/react-query';
import { useLocalSearchParams } from 'expo-router';
import { Pressable, View } from 'react-native';
import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles';
import { RateReviewHeader } from './components/RateReviewHeader';
import { RateReviewItem } from './components/RateReviewItem';
import { useCallback } from 'react';
import { FlashListAnimate } from '@/components/FlashListAnimate';
import { ListRenderItem } from '@shopify/flash-list';

const rt = UnistylesRuntime;
const RatingScreen = () => {
  const { styles } = useStyles(stylesheet);

  const queryClient = useQueryClient();

  const localParams = useLocalSearchParams();
  const podcastId = localParams['podcastId'] as string;

  const {
    data: podcastRate,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  } = useGetPodcastRateListQuery({
    podcastId,
    limit: 10,
    page: 1,
  });

  const allRates = podcastRate?.pages.flatMap((page) => page.data) || [];

  const handleRefetch = async () => {
    await Promise.all([
      queryClient.invalidateQueries({ queryKey: queryKeys.rates.stat() }),
      queryClient.invalidateQueries({ queryKey: queryKeys.podcasts.item() }),
      queryClient.invalidateQueries({ queryKey: queryKeys.rates.list() }),
    ]);
  };

  const renderItem = useCallback<ListRenderItem<IPodcastRate>>(
    ({ item }) => <RateReviewItem rateReviewData={item} />,
    []
  );

  const keyExtractor = useCallback((item: IPodcastRate) => item.userId.toString(), []);

  const onEndReached = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const ItemSeparatorComponent = useCallback(
    () => <Pressable pointerEvents='box-none' style={styles.separator} />,
    [styles.separator]
  );

  return (
    <View style={[styles.container, { paddingTop: rt.insets.top, paddingBottom: rt.insets.bottom }]}>
      <View style={styles.contentStyle}>
        <Header isBack hideHeader />
      </View>

      <FlashListAnimate
        data={allRates}
        showsVerticalScrollIndicator={false}
        keyExtractor={keyExtractor}
        ListHeaderComponent={<RateReviewHeader podcastId={podcastId} />}
        onRefresh={handleRefetch}
        onEndReachedThreshold={0.5}
        onEndReached={onEndReached}
        renderItem={renderItem}
        ItemSeparatorComponent={ItemSeparatorComponent}
        contentContainerStyle={styles.contentStyle}
      />
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  separator: {
    height: 1,
    backgroundColor: theme.colors.neutralGrey,
    marginVertical: 20,
  },
  contentStyle: {
    paddingHorizontal: 24,
  },
}));

export default RatingScreen;
