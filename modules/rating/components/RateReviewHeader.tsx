import {
  useAddPodcastRateMutation,
  useDeletePodcastRateMutation,
  useGetPodcastByIdQuery,
  useGetPodcastRateByPodcastIdQuery,
  useGetPodcastRateStatsQuery,
} from '@/apis/podcast';
import { RateStar } from '@/components/RateStar';
import { ThemedText } from '@/components/ThemedText';
import BarGraph from '@/components/BarGraph';
import DeleteRateModal from '@/modules/show-detail/components/DeleteRateModal';
import RateButton from '@/modules/show-detail/components/RateButton';
import RatingModal from '@/modules/show-detail/components/RatingModal';
import { ratingDistribution } from '@/utils/const';
import { formatCompactNumber } from '@/utils/func';
import { toastError, toastSuccess } from '@/utils/toast';
import { Skeleton } from 'moti/skeleton';
import { useCallback, useMemo, useState } from 'react';
import { Pressable, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';

type Props = {
  podcastId: string;
};

export const RateReviewHeader = ({ podcastId }: Props) => {
  const { styles, theme } = useStyles(stylesheet);

  const { onCheckAccountRestricted } = useCheckRestrictAccount();

  const { data: podcast, isPending: isPendingPodcast } = useGetPodcastByIdQuery(
    { podcastId },
    { enabled: !!podcastId }
  );

  const { data: podcastRateStats, isPending: isLoadingStats } = useGetPodcastRateStatsQuery(podcastId, {
    enabled: !!podcastId,
    refetchOnMount: true,
  });
  const { data: podcastUserRate } = useGetPodcastRateByPodcastIdQuery(
    { podcastId },
    { enabled: !!podcastId, refetchOnMount: true }
  );
  const [focusingRate, setFocusingRate] = useState<string>();

  const formattedDistribution = ratingDistribution.map((item) => {
    const distributionItem = podcastRateStats?.distribution.find((dist) => dist.rateValue.toString() === item.label);

    return {
      ...item,
      value: distributionItem ? +distributionItem.count : item.value,
    };
  });

  const focusingRateCount = formattedDistribution.find((item) => item.label === focusingRate)?.value;

  const { mutateAsync: addRate, isPending: isPendingAddRate } = useAddPodcastRateMutation();
  const { mutateAsync: deleteRate, isPending: isPendingDeleteRate } = useDeletePodcastRateMutation();

  const [showRateModal, setShowRateModal] = useState(false);
  const [showDeleteRateModal, setShowDeleteRateModal] = useState(false);

  const handleRatePress = useCallback(() => {
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    setShowRateModal(true);
  }, [onCheckAccountRestricted]);

  const handleRateSubmit = async (value: number) => {
    try {
      const isRestricted = onCheckAccountRestricted();
      if (isRestricted) return;

      await addRate({ podcastId: Number(podcastId), rate: value });
      toastSuccess({ description: 'Rated show successfully' });
      setShowRateModal(false);
    } catch (error) {
      toastError(error);
    } finally {
      setShowRateModal(false);
    }
  };

  const handleDeleteRate = async () => {
    try {
      const isRestricted = onCheckAccountRestricted();
      if (isRestricted) return;

      if (!podcastUserRate) {
        toastError('Delete rate failed! Podcast has not been rated yet!');
        return;
      }
      await deleteRate({ id: podcastUserRate?.podcastId });
      toastSuccess({ description: 'Deleted rate successfully' });
    } catch (error) {
      toastError(error);
    } finally {
      setShowDeleteRateModal(false);
    }
  };

  const onDeleteRatePressed = () => {
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    setShowDeleteRateModal(true);
  };

  const ratingCountText = useMemo(() => {
    if (focusingRate) {
      if ((focusingRateCount || 0) <= 1) {
        return `${formatCompactNumber(Number(focusingRateCount || 0))} Rating`;
      }

      return `${formatCompactNumber(Number(focusingRateCount || 0))} Ratings`;
    } else {
      if ((podcast?.rateCount || 0) <= 1) {
        return `Based on ${formatCompactNumber(Number(podcast?.rateCount || 0))} Rating`;
      }

      return `Based on ${formatCompactNumber(Number(podcast?.rateCount || 0))} Ratings`;
    }
  }, [podcast?.rateCount, focusingRate, focusingRateCount]);

  return (
    <Pressable>
      {showDeleteRateModal && (
        <DeleteRateModal
          isDeleting={isPendingDeleteRate}
          isVisible={showDeleteRateModal}
          onClose={() => setShowDeleteRateModal(false)}
          onDeleteConfirm={handleDeleteRate}
        />
      )}

      {showRateModal && (
        <RatingModal
          isVisible={showRateModal}
          defaultRate={podcastUserRate?.rate}
          onClose={() => setShowRateModal(false)}
          onRateSubmit={handleRateSubmit}
          isAddingRate={isPendingAddRate}
          enjoyName={podcast?.title || ''}
          image={podcast?.imageUrl}
        />
      )}

      {isPendingPodcast ? (
        <View style={[styles.headerTitle, { gap: 1 }]}>
          <Skeleton width={'100%'} height={32} />
          <Skeleton width={'40%'} height={32} />
        </View>
      ) : (
        <ThemedText style={styles.headerTitle}>{`Rating The ${podcast?.title}`}</ThemedText>
      )}

      {/* Rate Button */}
      <RateButton
        style={{ width: '100%' }}
        isRated={podcast?.hasRated || false}
        rating={podcastUserRate?.rate}
        onPress={handleRatePress}
        onDeleteRate={onDeleteRatePressed}
        onEditRate={handleRatePress}
        containerStyle={{ width: '100%' }}
        size='w-full'
      />

      {/* Rating Summary */}
      <View style={styles.ratingCard}>
        {isPendingPodcast ? (
          <Skeleton width={'100%'} height={32} />
        ) : (
          <View style={styles.ratingHeader}>
            <ThemedText style={styles.ratingValue}>
              {Number(focusingRate || podcast?.avgRate || '0.0').toFixed(1)}
            </ThemedText>

            <RateStar rating={Number(focusingRate) || Number(podcast?.avgRate || 0)} size={28} />
          </View>
        )}
        {isPendingPodcast ? (
          <View style={styles.ratingsCount}>
            <Skeleton width={'100%'} height={18} />
          </View>
        ) : (
          <ThemedText style={styles.ratingsCount}>{ratingCountText}</ThemedText>
        )}

        {/* Rating Chart */}
        <View style={styles.chartContainer}>
          {isLoadingStats ? (
            <Skeleton width={'100%'} height={104} />
          ) : (
            <BarGraph onChange={setFocusingRate} data={formattedDistribution} />
          )}
        </View>
      </View>

      <View style={{ marginTop: 28 }}>
        <ThemedText style={styles.sectionTitle}>Review</ThemedText>
      </View>
    </Pressable>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  headerTitle: {
    fontSize: 24,
    lineHeight: 32,
    ...theme.fw600,
    color: theme.colors.neutralWhite,
    marginTop: 16,
    marginBottom: 28,
  },
  rateButton: {},
  ratingCard: {
    marginTop: 28,
    paddingHorizontal: 20,
    paddingVertical: 24,
    backgroundColor: 'rgba(31, 30, 30, 1)',
    borderRadius: 12,
    minHeight: 215,
  },
  ratingHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingValue: {
    fontSize: 24,
    ...theme.fw700,
    marginRight: 16,
    lineHeight: 29,
    color: theme.colors.neutralWhite,
  },
  ratingsCount: {
    marginTop: 8,
    color: theme.colors.neutralWhite,
  },
  chartContainer: {
    marginTop: 8,
  },
  reviewsSection: {
    marginTop: 24,
    marginHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 24,
    ...theme.fw700,
    marginBottom: 16,
    color: theme.colors.neutralWhite,
  },
}));
