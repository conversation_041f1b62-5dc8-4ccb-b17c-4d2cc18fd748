import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { Button } from '@/components/ui/Button';
import { router } from 'expo-router';
import { Image, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

export const ResetPasswordSuccess = () => {
  const { styles } = useStyles(stylesheet);

  const handleDirect = () => {
    router.replace('/sign-in');
  };

  return (
    <View style={styles.container}>
      <Image source={require('@/assets/images/key.png')} style={styles.key} />

      <ThemedText type='titleSemiBold'>Reset was Successful!</ThemedText>

      <Spacer height={16} />

      <ThemedText type='small' style={styles.hintText}>
        Let's sign in to Rabid's again using your new password.
      </ThemedText>

      <Spacer height={56} />

      <View style={styles.directBox}>
        <Button onPress={handleDirect} style={styles.directButton}>
          Go to Sign In
        </Button>
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  key: {
    width: 280,
    height: 280,
    resizeMode: 'contain',
  },
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    paddingHorizontal: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  hintText: {
    maxWidth: 214,
    textAlign: 'center',
    color: theme.colors.neutralLightGrey,
  },
  directBox: {
    paddingHorizontal: 31,
    flexDirection: 'row',
  },
  directButton: {
    flex: 1,
  },
}));
