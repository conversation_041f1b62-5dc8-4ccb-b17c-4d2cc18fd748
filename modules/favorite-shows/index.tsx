import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { router } from 'expo-router';
import { useState, useCallback, useEffect } from 'react';
import { Header } from '@/components/ui/Header';
import * as Haptics from 'expo-haptics';
import { TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import DeleteShowModal from './components/DeleteShowModal';
import { ShowItem } from './components/ShowItem';
import {
  useGetFavoritePodcastsInfiniteQuery,
  useRemoveFavoritePodcastMutation,
  useUpdateFavoritePodcastOrderMutation,
} from '@/apis/favorite';
import { useGetProfileQuery } from '@/apis/auth/queries';
import { useLocalSearchParams } from 'expo-router';
import { InfiniteData, useQueryClient } from '@tanstack/react-query';
import { IFavoritePodcast, IGetFavoritePodcastsResponse } from '@/apis/favorite/types';
import { toastError, toastSuccess } from '@/utils/toast';
import { checkIsOwner } from '@/utils/func';
import { getUserIdForFavorites } from '@/utils/func';
import { Show } from '@/components/Show';
import { IconLoading } from '@/components/IconLoading';
import { Empty } from '@/components/Empty';
import Animated from 'react-native-reanimated';

import ReorderableList, { ReorderableListReorderEvent, reorderItems } from 'react-native-reorderable-list';
import { ListRenderItemInfo } from 'react-native';
import queryKeys from '@/utils/queryKeys';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';

export const DraggableFavoriteShows = () => {
  const { styles } = useStyles(stylesheet);
  const [shows, setShows] = useState<IFavoritePodcast[]>([]);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showToDelete, setShowToDelete] = useState<{ id: number; title: string } | null>(null);
  const [isPendingDelete, setIsPendingDelete] = useState(false);
  const { userId: routerUserId } = useLocalSearchParams<{ userId?: string }>();
  const { data: userProfile } = useGetProfileQuery();
  const queryClient = useQueryClient();
  const { onCheckAccountRestricted } = useCheckRestrictAccount();

  const targetUserId = getUserIdForFavorites(routerUserId, userProfile?.id);
  const isOwner = checkIsOwner(userProfile?.id, targetUserId);

  const {
    data: favoritePodcastsData,
    isFetching,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useGetFavoritePodcastsInfiniteQuery(
    {
      userId: Number(targetUserId),
      limit: 20,
    },
    {
      enabled: !!targetUserId,
    } as any
  );

  const removeFavoriteMutation = useRemoveFavoritePodcastMutation({
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.favorites.podcastsInfinite() });
      setIsPendingDelete(false);
      setShowDeleteModal(false);
      setShowToDelete(null);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      toastSuccess({
        description: 'Removed show from favorites successfully',
      });
    },
    onError: (error) => {
      setShowDeleteModal(false);
      toastError(error);
      setIsPendingDelete(false);
    },
  });

  const updateOrderMutation = useUpdateFavoritePodcastOrderMutation({
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.favorites.podcastsInfinite() });
    },
    onError: (error) => {
      toastError(error);
      const infiniteData = favoritePodcastsData as unknown as InfiniteData<IGetFavoritePodcastsResponse>;
      const allShows = infiniteData?.pages?.flatMap((page) => page.data) || [];
      setShows(allShows);
    },
  });

  useEffect(() => {
    const infiniteData = favoritePodcastsData as unknown as InfiniteData<IGetFavoritePodcastsResponse>;
    const allShows = infiniteData?.pages?.flatMap((page) => page.data) || [];
    setShows(allShows);
  }, [favoritePodcastsData]);

  const handleAddShows = useCallback(async () => {
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    router.push({
      pathname: '/(app)/discover-search',
      params: { searchAction: 'add-shows' },
    });
  }, [onCheckAccountRestricted]);

  const handleRemoveShow = useCallback((showId: number, showTitle: string) => {
    setShowToDelete({ id: showId, title: showTitle });
    setShowDeleteModal(true);
  }, []);

  const handleConfirmDelete = useCallback(async () => {
    if (!showToDelete) return;

    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    setIsPendingDelete(true);

    removeFavoriteMutation.mutate(showToDelete.id);
  }, [showToDelete, removeFavoriteMutation, onCheckAccountRestricted]);

  const handleCloseDeleteModal = useCallback(() => {
    setShowDeleteModal(false);
    setShowToDelete(null);
  }, []);

  const renderItem = useCallback(
    ({ item, index }: ListRenderItemInfo<IFavoritePodcast>) => {
      return <ShowItem item={item} index={index} onRemove={handleRemoveShow} isOwner={isOwner} />;
    },
    [handleRemoveShow, isOwner]
  );

  const keyExtractor = useCallback((item: IFavoritePodcast) => item.id.toString(), []);

  const handleReorder = ({ from, to }: ReorderableListReorderEvent) => {
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    updateOrderMutation.mutate({
      id: shows[from].id,
      index: to,
    });

    setShows((value) => reorderItems(value, from, to));
  };

  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  return (
    <View style={styles.container}>
      <Header isBack title='' leftStyle={styles.headerButton} />

      <Spacer height={24} />

      <View style={styles.header}>
        <View style={styles.leftSection}>
          <ThemedText style={styles.title}>Favorite Shows</ThemedText>
        </View>

        {isOwner && (
          <TouchableOpacity onPress={handleAddShows} style={styles.addButton}>
            <ThemedText style={styles.addButtonText}>Add Shows</ThemedText>
          </TouchableOpacity>
        )}
      </View>

      <Spacer height={40} />

      <Show when={isFetching}>
        <Animated.View>
          <IconLoading />
        </Animated.View>
      </Show>

      <Show when={!isFetching && shows.length === 0}>
        <Spacer height={73} />

        <Empty
          type='like'
          containerStyle={styles.emptyContainer}
          emptyText={`${isOwner ? 'You' : userProfile?.username} ${isOwner ? "don't" : "doesn't"} have any favorite shows yet.`}
        />
      </Show>

      <Show when={!isFetching && shows.length > 0}>
        <ReorderableList
          data={shows}
          renderItem={renderItem}
          keyExtractor={keyExtractor}
          onReorder={handleReorder}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContent}
          maxToRenderPerBatch={8}
          windowSize={8}
          initialNumToRender={6}
          updateCellsBatchingPeriod={50}
          getItemLayout={undefined}
          ItemSeparatorComponent={() => (
            <View style={styles.separator}>
              <View style={styles.separatorLine} />
            </View>
          )}
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
          ListFooterComponent={isFetchingNextPage ? <IconLoading /> : null}
        />

        <DeleteShowModal
          visible={showDeleteModal}
          onClose={handleCloseDeleteModal}
          onDelete={handleConfirmDelete}
          isPendingDelete={isPendingDelete}
          showTitle={showToDelete?.title}
        />
      </Show>
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    backgroundColor: theme.colors.background,
    flex: 1,
    paddingTop: rt.insets.top + 12,
    width: '100%',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    backgroundColor: theme.colors.background,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  backButton: {
    marginRight: 8,
    padding: 4,
  },
  title: {
    fontSize: 28,
    ...theme.fw500,
    lineHeight: 32,
    letterSpacing: 0,
    color: theme.colors.neutralWhite,
  },
  addButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 16,
    paddingVertical: 7,
    borderRadius: 9999,
  },
  addButtonText: {
    color: theme.colors.neutralBackground,
    fontSize: 12,
    ...theme.fw600,
    lineHeight: 24,
  },
  listContainer: {
    flex: 1,
  },
  listContent: {
    paddingBottom: 20,
  },
  separator: {
    paddingHorizontal: 24,
    paddingVertical: 24,
    backgroundColor: theme.colors.background,
  },
  separatorLine: {
    flex: 1,
    height: 1,
    backgroundColor: theme.colors.whiteOpacity10,
  },
  headerButton: {
    marginHorizontal: 24,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.neutralWhite,
  },
  emptyContainer: {},
  emptyText: {
    fontSize: 18,
    ...theme.fw500,
    color: theme.colors.neutralWhite,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: theme.colors.neutralWhite,
    opacity: 0.7,
    textAlign: 'center',
  },
  backButtonText: {
    color: theme.colors.neutralWhite,
  },
}));
