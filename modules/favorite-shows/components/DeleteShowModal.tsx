import BottomModal from '@/components/BottomModal';
import { ThemedText } from '@/components/ThemedText';
import { CustomButton } from '@/components/ui/CustomButton';
import { useCallback } from 'react';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type DeleteShowModalProps = {
  visible: boolean;
  onClose: () => void;
  onDelete: () => void;
  isPendingDelete?: boolean;
  showTitle?: string;
};

function DeleteShowModal({ visible, onClose, onDelete, isPendingDelete = false, showTitle }: DeleteShowModalProps) {
  const { styles } = useStyles(stylesheet);

  const handleDelete = useCallback(() => {
    onDelete();
  }, [onDelete]);

  const handleClose = useCallback(() => {
    onClose();
  }, [onClose]);

  return (
    <BottomModal isVisible={visible} onClose={onClose}>
      <View style={styles.container}>
        {showTitle ? (
          <ThemedText type='subtitleMedium' style={styles.title}>
            Are you sure you want to delete
            <ThemedText type='subtitleMedium' style={styles.showTitle}>
              {' '}
              {showTitle}{' '}
            </ThemedText>
            from your favorite shows?
          </ThemedText>
        ) : (
          <ThemedText type='subtitleMedium' style={styles.title}>
            Are you sure you want to delete this show from your favorites?
          </ThemedText>
        )}

        <CustomButton
          type='danger'
          onPress={handleDelete}
          style={{ marginBottom: 14 }}
          isLoading={isPendingDelete}
          textStyle={styles.confirmText}
        >
          Yes
        </CustomButton>

        <CustomButton type='outlined' onPress={handleClose} textStyle={styles.confirmText}>
          Cancel
        </CustomButton>
      </View>
    </BottomModal>
  );
}

const stylesheet = createStyleSheet((theme, rt) => ({
  confirmText: {
    ...theme.fw700,
  },
  container: {
    padding: 24,
  },
  showTitle: {
    color: theme.colors.neutralWhite,
    marginBottom: 28,
    ...theme.fw600,
    fontSize: 20,
    letterSpacing: 0,
  },
  title: {
    color: '#FFFFFFCC',
    fontSize: 20,
    marginBottom: 28,
    ...theme.fw500,
    letterSpacing: 0,
  },
}));

export default DeleteShowModal;
