import { Icons } from '@/assets/icons';
import { IFavoritePodcast } from '@/apis/favorite/types';
import DeleteOutline from '@/assets/icons/delete-outline';
import { ThemedText } from '@/components/ThemedText';
import * as Haptics from 'expo-haptics';
import React, { useCallback } from 'react';
import { Animated, TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { useReorderableDrag } from 'react-native-reorderable-list';
import { showDetailDirect } from '@/utils/router-prefetch';
import { useQueryClient } from '@tanstack/react-query';
import { ExpoImage } from '@/components/ui/Image';
import bigDecimal from 'js-big-decimal';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';

interface ShowItemProps {
  item: IFavoritePodcast;
  index?: number;
  onRemove: (id: number, title: string) => void;
  isOwner?: boolean;
}

export const ShowItem = React.memo(({ item, index, onRemove, isOwner }: ShowItemProps) => {
  const { styles, theme } = useStyles(itemStylesheet);
  const queryClient = useQueryClient();
  const { onCheckAccountRestricted } = useCheckRestrictAccount();

  const drag = useReorderableDrag();

  const handleRemove = useCallback(() => {
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    onRemove(item.id, item.title);
  }, [item.id, item.title, onRemove, onCheckAccountRestricted]);

  const handleDragStart = useCallback(() => {
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    drag();
  }, [drag, onCheckAccountRestricted]);

  const handleShowPress = useCallback(async () => {
    await showDetailDirect(queryClient, item?.podcastId?.toString());
  }, [item?.podcastId, queryClient]);

  const userRate = bigDecimal.round(item?.userRate, 1, bigDecimal.RoundingModes.HALF_UP);
  const avgRate = item.avgRate != null ? bigDecimal.round(item?.avgRate, 1, bigDecimal.RoundingModes.HALF_UP) : '0.0';

  return (
    <TouchableOpacity activeOpacity={0.7} onPress={handleShowPress}>
      <Animated.View style={[styles.container]}>
        <View style={styles.numberContainer}>
          <ThemedText style={styles.numberText}>{(index ?? 0) + 1}</ThemedText>
        </View>

        <ExpoImage source={{ uri: item.imageUrl }} style={styles.showImage} />

        <View style={styles.showInfoContainer}>
          <View style={styles.showInfo}>
            <ThemedText style={styles.showTitle} numberOfLines={1}>
              {item.title || ''}
            </ThemedText>

            <ThemedText style={styles.hostName} numberOfLines={1}>
              {item.authorName || ''}
            </ThemedText>

            <View style={styles.ratingContainer}>
              <Icons.StarRateFill color={theme.colors.stateWarning} width={16} height={16} />
              <ThemedText style={styles.ratingText}>{avgRate}</ThemedText>

              {item.userRate != null && (
                <>
                  <Icons.StarRateFill color={theme.colors.primary} width={16} height={16} />
                  <ThemedText style={styles.reviewCountText}>{userRate}</ThemedText>
                </>
              )}
            </View>
          </View>

          {isOwner && (
            <View style={styles.actionsContainer}>
              <TouchableOpacity
                onPressIn={handleDragStart}
                style={styles.dragHandle}
                hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
                activeOpacity={0.7}
              >
                <Icons.DotDrag color={theme.colors.neutralLightGrey} size={24} />
              </TouchableOpacity>

              <TouchableOpacity onPress={handleRemove} style={styles.actionButton} activeOpacity={0.7}>
                <DeleteOutline color={theme.colors.stateError} width={20} height={20} />
              </TouchableOpacity>
            </View>
          )}
        </View>
      </Animated.View>
    </TouchableOpacity>
  );
});

const itemStylesheet = createStyleSheet((theme) => ({
  container: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingHorizontal: 24,
    backgroundColor: theme.colors.background,
  },
  numberContainer: {
    width: 44,
    height: 44,
    alignItems: 'center',
    marginRight: 12,
    borderRadius: 1111,
    backgroundColor: theme.colors.neutralCard,
    padding: 10,
  },
  numberText: {
    fontSize: 14,
    ...theme.fw500,
    color: theme.colors.neutralWhite,
  },
  showImage: {
    width: 72,
    height: 72,
    borderRadius: 8,
    marginRight: 16,
  },
  showInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  showTitle: {
    fontSize: 16,
    ...theme.fw500,
    color: theme.colors.neutralWhite,
    marginBottom: 4,
    lineHeight: 24,
  },
  hostName: {
    fontSize: 14,
    color: theme.colors.neutralLightGrey,
    marginBottom: 4,
    lineHeight: 20,
    ...theme.fw500,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  ratingText: {
    fontSize: 12,
    color: theme.colors.neutralWhite,
    ...theme.fw500,
  },
  reviewCountText: {
    fontSize: 12,
    color: theme.colors.neutralLightGrey,
  },
  actionsContainer: {
    flexDirection: 'column',
    alignItems: 'flex-end',
  },
  actionButton: {
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  dragHandle: {
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    flex: 1,
  },
  showInfoContainer: {
    flex: 1,
    justifyContent: 'flex-start',
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
}));
