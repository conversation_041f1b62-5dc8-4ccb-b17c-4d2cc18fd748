import { UserLikesPost } from '@/apis/user';
import { Show } from '@/components/Show';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { Avatar } from '@/components/ui/Avatar';
import { ExpoImage } from '@/components/ui/Image';
import { UserProfileTouch } from '@/components/UserProfileTouch';
import { useIsYou } from '@/hooks/useIsYou';
import CommentImage from '@/modules/show-detail/components/CommentImage';
import { router } from 'expo-router';
import { memo, useMemo } from 'react';
import { TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  likedPostItem: Pick<
    UserLikesPost,
    | 'parentId'
    | 'commentId'
    | 'source'
    | 'authorId'
    | 'authorName'
    | 'authorAvatar'
    | 'parentTitle'
    | 'parentImageUrl'
    | 'title'
    | 'content'
    | 'images'
    | 'type'
  >;
};

export const LikedPostItem = memo(({ likedPostItem }: Props) => {
  const { styles, theme } = useStyles(stylesheet);

  const handleDirect = () => {
    if (likedPostItem.type === 'episode') {
      return router.push({
        pathname: '/(app)/episode/[episodeId]/review/[postId]',
        params: {
          episodeId: likedPostItem.parentId,
          postId: likedPostItem.commentId,
          source: likedPostItem.source,
        },
      });
    }

    router.push({
      pathname: '/(app)/podcast/[podcastId]/review/[postId]',
      params: {
        podcastId: likedPostItem.parentId,
        postId: likedPostItem.commentId,
        source: likedPostItem.source,
      },
    });
  };

  const isYou = useIsYou({
    userId: likedPostItem.authorId,
  });
  const usernameDisplay = useMemo(() => {
    if (isYou) return 'You';

    return likedPostItem?.authorName;
  }, [likedPostItem?.authorName, isYou]);

  return (
    <TouchableOpacity activeOpacity={0.7} onPress={handleDirect} style={styles.container}>
      <View style={[styles.infoBox, styles.rowHCenter]}>
        <UserProfileTouch userId={likedPostItem.authorId} userType={likedPostItem.source}>
          <Avatar image={likedPostItem.authorAvatar} />
        </UserProfileTouch>

        <View style={styles.fullFlex}>
          <ThemedText
            type='defaultSemiBold'
            style={[
              isYou
                ? {
                    color: theme.colors.primary,
                  }
                : null,
            ]}
          >
            {usernameDisplay}
          </ThemedText>

          <Show when={!!likedPostItem.parentTitle}>
            <Spacer height={6} />

            <ThemedText type='tiny' numberOfLines={2}>
              {likedPostItem.parentTitle}
            </ThemedText>
          </Show>
        </View>

        <ExpoImage source={{ uri: likedPostItem.parentImageUrl }} style={styles.postImage} />
      </View>

      <Spacer height={20} />

      <ThemedText type='defaultMedium'>{likedPostItem.title}</ThemedText>

      <Spacer height={8} />

      <ThemedText type='small'>{likedPostItem.content}</ThemedText>

      <Show when={likedPostItem.images.length > 0}>
        <>
          <Spacer height={20} />

          <CommentImage images={likedPostItem.images} />
        </>
      </Show>
    </TouchableOpacity>
  );
});

const stylesheet = createStyleSheet({
  container: {},
  infoBox: {
    gap: 24,
  },
  fullFlex: {
    flex: 1,
  },
  postImage: {
    width: 64,
    height: 64,
    borderRadius: 12,
  },
  rowHCenter: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});
