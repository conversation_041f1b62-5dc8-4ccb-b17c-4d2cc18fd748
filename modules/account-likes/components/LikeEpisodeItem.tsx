import { UserLikesEpisode } from '@/apis/user';
import { Show } from '@/components/Show';
import { ThemedText } from '@/components/ThemedText';
import { ExpoImage } from '@/components/ui/Image';
import { TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  item: UserLikesEpisode;
  listType: 'list' | 'grid';
  onPress: (item: UserLikesEpisode) => void;
  itemSize: number;
};

export const LikeEpisodeItem = ({ item, listType, itemSize, onPress }: Props) => {
  const { styles } = useStyles(stylesheet);

  if (listType === 'grid') {
    return (
      <TouchableOpacity onPress={() => onPress(item)} activeOpacity={0.7} style={styles.containerGrid}>
        <ExpoImage source={{ uri: item.imageUrl }} style={{ width: itemSize, height: itemSize, borderRadius: 8 }} />
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity onPress={() => onPress(item)} activeOpacity={0.7} style={styles.containerList}>
      <ExpoImage source={{ uri: item.imageUrl }} style={{ width: 64, height: 64, borderRadius: 8 }} />

      <View style={styles.fullFlex}>
        <ThemedText type='defaultMedium' numberOfLines={2}>
          {item.title}
        </ThemedText>

        <Show when={!!item?.podcastTitle}>
          <ThemedText type='defaultMedium' numberOfLines={1} style={styles.showText}>
            {`Episode - ${item?.podcastTitle ?? ''}`}
          </ThemedText>
        </Show>
      </View>
    </TouchableOpacity>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  containerGrid: {
    flexGrow: 1,
    flex: 1,
  },
  containerList: {
    flexGrow: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  fullFlex: {
    flex: 1,
  },
  showText: {
    color: theme.colors.neutralLightGrey,
  },
}));
