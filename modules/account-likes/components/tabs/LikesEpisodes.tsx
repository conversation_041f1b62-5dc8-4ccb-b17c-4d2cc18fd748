import { IUserProfileById, UserLikesEpisode } from '@/apis/user';
import { useGetInfiniteUserLikesEpisodesHistoryQuery } from '@/apis/user/queries';
import { Icons } from '@/assets/icons';
import { TabFlashList } from '@/components/collapsing-tabs/TabFlashList';
import { Empty } from '@/components/Empty';
import { IconLoading } from '@/components/IconLoading';
import { ThemedText } from '@/components/ThemedText';
import { useIsYou } from '@/hooks/useIsYou';
import { getItemSizeFlatList } from '@/utils/func';
import { episodeDetailDirect } from '@/utils/router-prefetch';
import { ListRenderItem } from '@shopify/flash-list';
import { useQueryClient } from '@tanstack/react-query';
import { memo, useCallback, useState } from 'react';
import { TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { LikeEpisodeItem } from '../LikeEpisodeItem';
import { Spacer } from '@/components/Spacer';
import queryKeys from '@/utils/queryKeys';

type Props = {
  userId: string;
  profile?: IUserProfileById;
};

const itemSize = getItemSizeFlatList(24, 3, 11);

export const LikesEpisodes = memo(({ userId, profile }: Props) => {
  const { styles } = useStyles(stylesheet);
  const queryClient = useQueryClient();
  const [listType, setListType] = useState<'list' | 'grid'>('grid');

  const isYou = useIsYou({
    userId: userId ?? '',
  });

  const {
    data: likesEpisodesData,
    isPending,
    isSuccess,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
  } = useGetInfiniteUserLikesEpisodesHistoryQuery({
    userId: userId ?? '',
    limit: 24,
  });

  const likesEpisodes = likesEpisodesData?.pages?.flatMap((page) => page.data) ?? [];

  const handleDirect = useCallback(
    (item: UserLikesEpisode) => {
      episodeDetailDirect(queryClient, item.episodeId.toString());
    },
    [queryClient]
  );

  const renderItem = useCallback<ListRenderItem<UserLikesEpisode>>(
    ({ item }) => <LikeEpisodeItem item={item} listType={listType} itemSize={itemSize} onPress={handleDirect} />,
    [handleDirect, listType]
  );

  const handleLoadMore = useCallback(async () => {
    if (hasNextPage && !isFetchingNextPage) {
      await fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const renderHeader = useCallback(
    () => (
      <View style={styles.headerList}>
        <ThemedText type='defaultSemiBold'>Recent</ThemedText>

        {listType === 'list' ? (
          <TouchableOpacity onPress={() => setListType('grid')} activeOpacity={0.7}>
            <Icons.GridIcon size={24} color='#fff' />
          </TouchableOpacity>
        ) : (
          <TouchableOpacity onPress={() => setListType('list')} activeOpacity={0.7}>
            <Icons.ListIcon size={24} color='#fff' />
          </TouchableOpacity>
        )}
      </View>
    ),
    [styles, listType]
  );

  const keyExtractor = useCallback((item: UserLikesEpisode) => item.id.toString(), []);

  const renderSkeleton = useCallback(() => <IconLoading />, []);

  const renderSeparator = useCallback(() => <Spacer height={listType === 'list' ? 24 : 28} />, [listType]);

  const handleRefetch = useCallback(async () => {
    await queryClient.resetQueries({ queryKey: queryKeys.userProfile.getUserLikesEpisodesHistoryInfinite() });
  }, [queryClient]);

  const isShowEmpty = isSuccess && likesEpisodes.length === 0;

  return (
    <TabFlashList
      key={listType}
      extraData={listType}
      bounces={false}
      refreshing={isPending}
      onRefresh={handleRefetch}
      numColumns={listType === 'list' ? 1 : 3}
      data={likesEpisodes}
      style={styles.container}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.contentContainer}
      scrollEventThrottle={16}
      keyExtractor={keyExtractor}
      renderItem={renderItem}
      ListHeaderComponent={renderHeader}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.4}
      estimatedItemSize={listType === 'list' ? 64 : itemSize}
      ItemSeparatorComponent={renderSeparator}
      ListFooterComponent={isFetchingNextPage ? renderSkeleton : null}
      ListEmptyComponent={
        isShowEmpty ? (
          <Empty
            type='like'
            emptyText={`${isYou ? 'You' : profile?.username} ${isYou ? "haven't" : "hasn't"} liked any episodes`}
          />
        ) : null
      }
    />
  );
});

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 24,
  },
  headerList: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 20,
  },
}));
