export const activitiesMock = [
  {
    id: 1,
    userId: 1,
    username: '<PERSON><PERSON><PERSON>',
    type: 'rated',
    rate: 4,
    parentName: 'Tech Talk Weekly',
    createdAt: '2024-01-20T10:00:00Z',
  },
  {
    id: 2,
    userId: 1,
    username: '<PERSON><PERSON><PERSON>',
    type: 'edited_rate',
    rate: 5,
    parentName: 'Science Today',
    createdAt: '2024-01-20T09:45:00Z',
  },
  {
    id: 3,
    userId: 1,
    username: '<PERSON><PERSON><PERSON>',
    type: 'liked',
    parentName: 'History Mysteries',
    createdAt: '2024-01-20T09:30:00Z',
  },
  {
    id: 4,
    userId: 1,
    username: '<PERSON><PERSON><PERSON>',
    type: 'unlike',
    parentName: 'True Crime Stories',
    createdAt: '2024-01-20T09:15:00Z',
  },
  {
    id: 5,
    userId: 1,
    username: '<PERSON><PERSON><PERSON>',
    type: 'added_to_watchlist',
    parentName: 'Daily News Digest',
    createdAt: '2024-01-20T09:00:00Z',
  },
  {
    id: 6,
    userId: 1,
    username: '<PERSON><PERSON><PERSON>',
    type: 'removed_from_watchlist',
    parentName: 'Comedy Hour',
    createdAt: '2024-01-20T08:45:00Z',
  },
  {
    id: 7,
    userId: 1,
    username: '<PERSON><PERSON>oe',
    type: 'marked_as_watched',
    parentName: 'Business Insights',
    createdAt: '2024-01-20T08:30:00Z',
  },
  {
    id: 8,
    userId: 1,
    username: 'JohnDoe',
    type: 'unmarked_as_watched',
    parentName: 'Health & Wellness',
    createdAt: '2024-01-20T08:15:00Z',
  },
  {
    id: 9,
    userId: 1,
    username: 'JohnDoe',
    type: 'commented',
    text: 'Great episode!',
    title: 'Episode 1',
    parentName: 'Tech Talk Weekly',
    createdAt: '2024-01-20T08:00:00Z',
  },
  {
    id: 10,
    userId: 1,
    username: 'JohnDoe',
    type: 'edited_comment',
    text: 'Amazing episode!',
    title: 'Episode 2',
    parentName: 'Science Today',
    createdAt: '2024-01-20T07:45:00Z',
  },
  {
    id: 11,
    userId: 1,
    username: 'JohnDoe',
    type: 'deleted_comment',
    text: 'Deleted comment',
    title: 'Episode 3',
    parentName: 'History Mysteries',
    createdAt: '2024-01-20T07:30:00Z',
  },
  {
    id: 12,
    userId: 1,
    username: 'JohnDoe',
    type: 'replied',
    text: 'Thanks for sharing!',
    poster: 'JohnDoe',
    title: 'Episode 1',
    parentName: 'True Crime Stories',
    createdAt: '2024-01-20T07:15:00Z',
  },
  {
    id: 13,
    userId: 1,
    username: 'JohnDoe',
    type: 'edited_reply',
    text: 'Thanks a lot for sharing!',
    poster: 'JaneDoe',
    title: 'Episode 2',
    parentName: 'Daily News Digest',
    createdAt: '2024-01-20T07:00:00Z',
  },
  {
    id: 14,
    userId: 1,
    username: 'JohnDoe',
    type: 'rated',
    rate: 3,
    parentName: 'Comedy Hour',
    createdAt: '2024-01-20T06:45:00Z',
  },
  {
    id: 15,
    userId: 1,
    username: 'JohnDoe',
    type: 'liked',
    parentName: 'Business Insights',
    createdAt: '2024-01-20T06:30:00Z',
  },
  {
    id: 16,
    userId: 1,
    username: 'JohnDoe',
    type: 'added_to_watchlist',
    parentName: 'Health & Wellness',
    createdAt: '2024-01-20T06:15:00Z',
  },
  {
    id: 17,
    userId: 1,
    username: 'JohnDoe',
    type: 'marked_as_watched',
    parentName: 'Tech Talk Weekly',
    createdAt: '2024-01-20T06:00:00Z',
  },
  {
    id: 18,
    userId: 1,
    username: 'JohnDoe',
    type: 'commented',
    text: 'Interesting podcast!',
    title: 'Episode 4',
    parentName: 'Science Today',
    createdAt: '2024-01-20T05:45:00Z',
  },
  {
    id: 19,
    userId: 1,
    username: 'JohnDoe',
    type: 'replied',
    text: 'Totally agree',
    poster: 'AliceSmith',
    title: 'Episode 4',
    parentName: 'History Mysteries',
    createdAt: '2024-01-20T05:30:00Z',
  },
  {
    id: 20,
    userId: 1,
    username: 'JohnDoe',
    type: 'edited_rate',
    rate: 2,
    parentName: 'True Crime Stories',
    createdAt: '2024-01-20T05:15:00Z',
  },
  {
    id: 21,
    userId: 1,
    type: 'followed',
    username: 'JohnDoe',
    createdAt: '2024-01-20T05:00:00Z',
  },
  {
    id: 22,
    userId: 1,
    type: 'unfollowed',
    username: 'JaneDoe',
    createdAt: '2024-01-20T04:45:00Z',
  },
  {
    id: 23,
    userId: 1,
    type: 'followed',
    username: 'AliceSmith',
    createdAt: '2024-01-20T04:30:00Z',
  },
  {
    id: 24,
    userId: 1,
    type: 'unfollowed',
    username: 'BobJohnson',
    createdAt: '2024-01-20T04:15:00Z',
  },
];

// add type
export type TActivityItem = (typeof activitiesMock)[number];
