import { ThemedText } from '@/components/ThemedText';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { useMemo } from 'react';
import { useIsYou } from '@/hooks/useIsYou';
import { ActivityTypeEnum } from '@/apis/activity';

type Props = {
  type: ActivityTypeEnum;
  name: string;
  username: string;
  userId: string;
};

export const LikeActivity = ({ type, name, userId, username }: Props) => {
  const { styles } = useStyles(stylesheet);

  const nameFormat = name?.trim();

  const isYou = useIsYou({
    userId: userId,
  });

  const title = useMemo(() => {
    if (type === ActivityTypeEnum.LIKE) return `${isYou ? 'You' : username} liked`;
    if (type === ActivityTypeEnum.UNLIKE) return `${isYou ? 'You' : username} unliked`;

    return '';
  }, [type, isYou, username]);

  return (
    <View style={styles.container}>
      <ThemedText type='smallNormal' numberOfLines={3}>
        {title}{' '}
        <ThemedText type='small' style={styles.title}>
          {nameFormat}
        </ThemedText>
      </ThemedText>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  title: {
    ...theme.fw600,
  },
}));
