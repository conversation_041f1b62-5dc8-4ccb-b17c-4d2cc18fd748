import { RateStar } from '@/components/RateStar';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { useMemo } from 'react';
import { Show } from '@/components/Show';
import { useIsYou } from '@/hooks/useIsYou';
import { ActivityTypeEnum } from '@/apis/activity';

type Props = {
  type: ActivityTypeEnum;
  rating?: number;
  name: string;
  username: string;
  userId: string;
};

export const RateActivity = ({ type, rating = 0, name, userId, username }: Props) => {
  const { styles } = useStyles(stylesheet);
  const isYou = useIsYou({
    userId: userId,
  });

  const nameFormat = name?.trim();

  const title = useMemo(() => {
    if (type === ActivityTypeEnum.RATE) return `${isYou ? 'You' : username} rated`;
    if (type === ActivityTypeEnum.EDIT_RATE) return `${isYou ? 'You' : username} edited the rating on`;
    if (type === ActivityTypeEnum.DELETE_RATE) return `${isYou ? 'You' : username} deleted the rating on`;

    return '';
  }, [type, isYou, username]);

  return (
    <View style={styles.container}>
      <ThemedText type='smallNormal' numberOfLines={3}>
        {title}{' '}
        <ThemedText type='small' style={styles.title}>
          {nameFormat}
        </ThemedText>
      </ThemedText>

      <Show when={type !== ActivityTypeEnum.DELETE_RATE}>
        <Spacer height={12} />

        <RateStar rating={rating} size={16} gap={4} />
      </Show>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  title: {
    ...theme.fw600,
  },
}));
