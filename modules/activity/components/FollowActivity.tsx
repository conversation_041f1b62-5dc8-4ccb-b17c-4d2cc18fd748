import { ThemedText } from '@/components/ThemedText';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { useMemo } from 'react';
import { useIsYou } from '@/hooks/useIsYou';
import { ActivityTypeEnum } from '@/apis/activity';

type Props = {
  type: ActivityTypeEnum;
  username: string; // This is the followed/unfollowed user's username
  userId: string; // This is the user who performed the action
  actionUserName: string; // Username of user who performed the action
};

export const FollowActivity = ({ type, username, userId, actionUserName }: Props) => {
  const { styles } = useStyles(stylesheet);
  const isYou = useIsYou({
    userId: userId,
  });

  const title = useMemo(() => {
    if (type === ActivityTypeEnum.FOLLOW) return `${isYou ? 'You' : actionUserName} followed ${username}`;
    if (type === ActivityTypeEnum.UNFOLLOW) return `${isYou ? 'You' : actionUserName} unfollowed ${username}`;

    return '';
  }, [type, username, actionUserName, isYou]);

  return (
    <View style={styles.container}>
      <ThemedText type='smallNormal' numberOfLines={3}>
        {title}
      </ThemedText>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  title: {
    ...theme.fw600,
  },
}));
