import { ThemedText } from '@/components/ThemedText';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { useMemo } from 'react';
import { useIsYou } from '@/hooks/useIsYou';
import { ActivityTypeEnum } from '@/apis/activity';

type Props = {
  type: ActivityTypeEnum;
  name: string;
  username: string;
  userId: string;
};

export const WatchlistActivity = ({ type, name, userId, username }: Props) => {
  const { styles } = useStyles(stylesheet);

  const nameFormat = name?.trim();

  const isYou = useIsYou({
    userId: userId,
  });

  const beginTitle = useMemo(() => {
    if (type === ActivityTypeEnum.ADD_WATCHLIST) return `${isYou ? 'You' : username} added`;
    if (type === ActivityTypeEnum.REMOVE_WATCHLIST) return `${isYou ? 'You' : username} removed`;

    return '';
  }, [type, username, isYou]);

  const endTitle = useMemo(() => {
    if (type === ActivityTypeEnum.ADD_WATCHLIST) return ` to ${isYou ? 'your' : 'their'} watchlist`;
    if (type === ActivityTypeEnum.REMOVE_WATCHLIST) return ` from ${isYou ? 'your' : 'their'} watchlist`;

    return '';
  }, [type, isYou]);

  return (
    <View style={styles.container}>
      <ThemedText type='smallNormal' numberOfLines={3}>
        {beginTitle}
        {endTitle}{' '}
        <ThemedText type='small' style={styles.title}>
          {nameFormat}
        </ThemedText>
      </ThemedText>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  title: {
    ...theme.fw600,
  },
}));
