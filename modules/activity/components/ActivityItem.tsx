import { Avatar } from '@/components/ui/Avatar';
import { useCallback } from 'react';
import { TouchableOpacity } from 'react-native';
import { RateActivity } from './RateActivity';
import { ThemedText } from '@/components/ThemedText';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { LikeActivity } from './LikeActivity';
import { WatchlistActivity } from './WatchlistActivity';
import { WatchedActivity } from './WatchedActivity';
import { format } from 'date-fns';
import { CommentActivity } from './CommentActivity';
import { ReplyActivity } from './ReplyActivity';
import { TActivityItem } from '../mocks';
import { FollowActivity } from './FollowActivity';
import {
  IActivityLog,
  ActivityTypeEnum,
  TargetTypeEnum,
  RateMetadata,
  LikeMetadata,
  WatchlistMetadata,
  WatchedMetadata,
  CommentMetadata,
  ReplyMetadata,
  FollowMetadata,
} from '@/apis/activity';
import { UserProfileTouch } from '@/components/UserProfileTouch';
import { router } from 'expo-router';
import { formatIntervalToNowDuration } from '@/utils/func';
import { episodeDetailDirect, showDetailDirect } from '@/utils/router-prefetch';
import { useQueryClient } from '@tanstack/react-query';

export type ActivityType =
  | 'rated'
  | 'edited_rate'
  | 'deleted_rate'
  | 'liked'
  | 'unlike'
  | 'added_to_watchlist'
  | 'removed_from_watchlist'
  | 'marked_as_watched'
  | 'unmarked_as_watched'
  | 'commented'
  | 'edited_comment'
  | 'deleted_comment'
  | 'replied'
  | 'edited_reply'
  | 'deleted_reply'
  | 'followed'
  | 'unfollowed';

// Transform API data to component format
const transformActivityData = (apiActivity: IActivityLog): TActivityItem => {
  // Map server enum values to component expected values
  const activityTypeMap: Record<ActivityTypeEnum, string> = {
    [ActivityTypeEnum.RATE]: 'rated',
    [ActivityTypeEnum.EDIT_RATE]: 'edited_rate',
    [ActivityTypeEnum.DELETE_RATE]: 'deleted_rate',
    [ActivityTypeEnum.LIKE]: 'liked',
    [ActivityTypeEnum.UNLIKE]: 'unlike',
    [ActivityTypeEnum.ADD_WATCHLIST]: 'added_to_watchlist',
    [ActivityTypeEnum.REMOVE_WATCHLIST]: 'removed_from_watchlist',
    [ActivityTypeEnum.MARK_WATCHED]: 'marked_as_watched',
    [ActivityTypeEnum.UNMARK_WATCHED]: 'unmarked_as_watched',
    [ActivityTypeEnum.COMMENT]: 'commented',
    [ActivityTypeEnum.EDIT_COMMENT]: 'edited_comment',
    [ActivityTypeEnum.DELETE_COMMENT]: 'deleted_comment',
    [ActivityTypeEnum.REPLY]: 'replied',
    [ActivityTypeEnum.EDIT_REPLY]: 'edited_reply',
    [ActivityTypeEnum.DELETE_REPLY]: 'deleted_reply',
    [ActivityTypeEnum.FOLLOW]: 'followed',
    [ActivityTypeEnum.UNFOLLOW]: 'unfollowed',
  };

  const transformedType = activityTypeMap[apiActivity.activityType];

  // Extract data based on activity type and metadata structure
  let rate: number | undefined;
  let parentName = '';
  let text: string | undefined;
  let title: string | undefined;
  let poster: string | undefined;

  switch (apiActivity.activityType) {
    case ActivityTypeEnum.RATE:
    case ActivityTypeEnum.EDIT_RATE:
    case ActivityTypeEnum.DELETE_RATE: {
      const rateMetadata = apiActivity.metadata as RateMetadata;
      rate = rateMetadata?.rating;
      parentName = rateMetadata?.title || '';
      break;
    }

    case ActivityTypeEnum.LIKE:
    case ActivityTypeEnum.UNLIKE: {
      const likeMetadata = apiActivity.metadata as LikeMetadata;
      parentName = likeMetadata?.title || '';
      break;
    }

    case ActivityTypeEnum.ADD_WATCHLIST:
    case ActivityTypeEnum.REMOVE_WATCHLIST: {
      const watchlistMetadata = apiActivity.metadata as WatchlistMetadata;
      parentName = watchlistMetadata?.title || '';
      break;
    }

    case ActivityTypeEnum.MARK_WATCHED:
    case ActivityTypeEnum.UNMARK_WATCHED: {
      const watchedMetadata = apiActivity.metadata as WatchedMetadata;
      parentName = watchedMetadata?.title || '';
      break;
    }

    case ActivityTypeEnum.COMMENT:
    case ActivityTypeEnum.EDIT_COMMENT: {
      const commentMetadata = apiActivity.metadata as CommentMetadata;
      parentName = commentMetadata?.title || '';
      title = commentMetadata?.commentTitle || '';
      break;
    }

    case ActivityTypeEnum.DELETE_COMMENT: {
      const commentMetadata = apiActivity.metadata as CommentMetadata;
      parentName = commentMetadata?.title || '';
      break;
    }

    case ActivityTypeEnum.REPLY:
    case ActivityTypeEnum.EDIT_REPLY: {
      const replyMetadata = apiActivity.metadata as ReplyMetadata;
      parentName = replyMetadata?.title || '';
      text = replyMetadata?.content || '';
      poster = replyMetadata?.user?.username || '';
      break;
    }

    case ActivityTypeEnum.DELETE_REPLY: {
      const replyMetadata = apiActivity.metadata as ReplyMetadata;
      parentName = replyMetadata?.title || '';
      poster = replyMetadata?.user?.username || '';
      break;
    }

    case ActivityTypeEnum.FOLLOW:
    case ActivityTypeEnum.UNFOLLOW: {
      const followMetadata = apiActivity.metadata as FollowMetadata;
      // For follow activities, we use the followed user's username
      parentName = followMetadata?.username || '';
      // targetId is the followed/unfollowed user's ID
      break;
    }
  }

  return {
    id: apiActivity.id,
    userId: apiActivity.userId,
    username: apiActivity.username || '',
    type: transformedType,
    createdAt: apiActivity.createdAt,
    rate,
    parentName,
    text,
    title,
    poster,
    avatar: apiActivity.avatar,
  } as TActivityItem;
};

type Props = {
  activityItem: IActivityLog;
};

export const ActivityItem = ({ activityItem }: Props) => {
  const { styles } = useStyles(stylesheet);
  const queryClient = useQueryClient();

  const handleActivityPress = useCallback(() => {
    const { activityType, targetType, targetId, metadata } = activityItem;

    switch (activityType) {
      case ActivityTypeEnum.RATE:
      case ActivityTypeEnum.EDIT_RATE: {
        if (targetType === TargetTypeEnum.PODCAST) {
          router.push(`/(app)/podcast/${targetId}/rating`);
        } else if (targetType === TargetTypeEnum.EPISODE) {
          router.push(`/(app)/episode/${targetId}/rating`);
        }
        break;
      }

      case ActivityTypeEnum.DELETE_RATE:
      case ActivityTypeEnum.LIKE:
      case ActivityTypeEnum.UNLIKE:
      case ActivityTypeEnum.ADD_WATCHLIST:
      case ActivityTypeEnum.REMOVE_WATCHLIST:
      case ActivityTypeEnum.MARK_WATCHED:
      case ActivityTypeEnum.UNMARK_WATCHED: {
        if (targetType === TargetTypeEnum.PODCAST) {
          showDetailDirect(queryClient, targetId?.toString());
        } else if (targetType === TargetTypeEnum.EPISODE) {
          episodeDetailDirect(queryClient, targetId?.toString());
        }
        break;
      }

      case ActivityTypeEnum.COMMENT:
      case ActivityTypeEnum.EDIT_COMMENT: {
        const commentMetadata = metadata as CommentMetadata;
        if (commentMetadata?.type === 'podcast') {
          router.push({
            pathname: '/(app)/podcast/[podcastId]/review/[postId]',
            params: {
              podcastId: commentMetadata.podcastId.toString(),
              postId: targetId.toString(),
              source: commentMetadata.source || 'local',
            },
          });
        } else if (commentMetadata?.type === 'episode') {
          router.push({
            pathname: '/(app)/episode/[episodeId]/review/[postId]',
            params: {
              episodeId: commentMetadata.episodeId.toString(),
              postId: targetId?.toString(),
              source: commentMetadata.source || 'local',
            },
          });
        }
        break;
      }

      case ActivityTypeEnum.DELETE_COMMENT: {
        const commentMetadata = metadata as CommentMetadata;
        if (commentMetadata?.type === 'podcast') {
          showDetailDirect(queryClient, commentMetadata?.podcastId?.toString());
        } else if (commentMetadata?.type === 'episode') {
          episodeDetailDirect(queryClient, commentMetadata?.episodeId?.toString());
        }
        break;
      }
      case ActivityTypeEnum.REPLY:
      case ActivityTypeEnum.EDIT_REPLY: {
        const replyMetadata = metadata as ReplyMetadata;
        const metaType = replyMetadata?.type;
        if (metaType === 'podcast') {
          router.push({
            pathname: '/(app)/podcast/[podcastId]/review/[postId]',
            params: {
              podcastId: replyMetadata?.podcastId?.toString() ?? '',
              postId: targetId?.toString(),
              source: replyMetadata.source || 'local',
            },
          });
        } else if (metaType === 'episode') {
          router.push({
            pathname: '/(app)/episode/[episodeId]/review/[postId]',
            params: {
              episodeId: replyMetadata?.episodeId?.toString() ?? '',
              postId: targetId?.toString(),
              source: replyMetadata.source || 'local',
            },
          });
        }
        break;
      }

      case ActivityTypeEnum.DELETE_REPLY: {
        const replyMetadata = metadata as ReplyMetadata;
        const metaType = replyMetadata?.type;
        if (metaType === 'podcast') {
          router.push({
            pathname: '/(app)/podcast/[podcastId]/review/[postId]',
            params: {
              podcastId: replyMetadata?.podcastId?.toString() ?? '',
              postId: targetId?.toString(),
              source: replyMetadata.source || 'local',
            },
          });
        } else if (metaType === 'episode') {
          router.push({
            pathname: '/(app)/episode/[episodeId]/review/[postId]',
            params: {
              episodeId: replyMetadata?.episodeId?.toString() ?? '',
              postId: targetId?.toString(),
              source: replyMetadata.source || 'local',
            },
          });
        }
        break;
      }

      case ActivityTypeEnum.FOLLOW:
      case ActivityTypeEnum.UNFOLLOW: {
        router.push({
          pathname: '/(app)/[userId]',
          params: { userId: targetId?.toString() },
        });
        break;
      }
    }
  }, [activityItem, queryClient]);

  const getActivityContent = useCallback(() => {
    const { activityType } = activityItem;
    const transformedItem = transformActivityData(activityItem);

    switch (activityType) {
      case ActivityTypeEnum.RATE:
      case ActivityTypeEnum.EDIT_RATE:
      case ActivityTypeEnum.DELETE_RATE:
        return (
          <RateActivity
            type={activityType}
            rating={transformedItem.rate}
            name={transformedItem.parentName ?? ''}
            userId={transformedItem.userId?.toString() ?? ''}
            username={transformedItem.username}
          />
        );

      case ActivityTypeEnum.LIKE:
      case ActivityTypeEnum.UNLIKE:
        return (
          <LikeActivity
            type={activityType}
            name={transformedItem.parentName ?? ''}
            userId={transformedItem.userId?.toString() ?? ''}
            username={transformedItem.username}
          />
        );

      case ActivityTypeEnum.ADD_WATCHLIST:
      case ActivityTypeEnum.REMOVE_WATCHLIST:
        return (
          <WatchlistActivity
            type={activityType}
            name={transformedItem.parentName ?? ''}
            userId={transformedItem.userId?.toString() ?? ''}
            username={transformedItem.username}
          />
        );

      case ActivityTypeEnum.MARK_WATCHED:
      case ActivityTypeEnum.UNMARK_WATCHED:
        return (
          <WatchedActivity
            type={activityType}
            time={format(new Date(transformedItem.createdAt), 'dd MMM yyyy')}
            name={transformedItem.parentName ?? ''}
            userId={transformedItem.userId?.toString() ?? ''}
            username={transformedItem.username}
          />
        );

      case ActivityTypeEnum.COMMENT:
      case ActivityTypeEnum.EDIT_COMMENT:
      case ActivityTypeEnum.DELETE_COMMENT:
        return (
          <CommentActivity
            type={activityType}
            text={transformedItem?.title ?? ''}
            name={transformedItem.parentName ?? ''}
            userId={transformedItem.userId?.toString() ?? ''}
            username={transformedItem.username}
          />
        );

      case ActivityTypeEnum.REPLY:
      case ActivityTypeEnum.EDIT_REPLY:
      case ActivityTypeEnum.DELETE_REPLY:
        return (
          <ReplyActivity
            type={activityType}
            text={transformedItem.text ?? ''}
            poster={transformedItem.poster ?? ''}
            name={transformedItem.parentName ?? ''}
            userId={transformedItem.userId?.toString() ?? ''}
            username={transformedItem.username}
          />
        );

      case ActivityTypeEnum.FOLLOW:
      case ActivityTypeEnum.UNFOLLOW:
        return (
          <FollowActivity
            type={activityType}
            username={transformedItem?.parentName ?? ''} // followed/unfollowed user's username
            userId={transformedItem.userId?.toString() ?? ''} // user who performed the action
            actionUserName={transformedItem?.username ?? ''} // username of user who performed action
          />
        );

      default:
        return null;
    }
  }, [activityItem]);

  return (
    <TouchableOpacity style={styles.container} onPress={handleActivityPress} activeOpacity={0.7}>
      <UserProfileTouch userId={activityItem.userId} userType='local'>
        <Avatar image={activityItem.avatar} />
      </UserProfileTouch>

      {getActivityContent()}

      <ThemedText style={styles.time}>{formatIntervalToNowDuration(activityItem.createdAt)}</ThemedText>
    </TouchableOpacity>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    backgroundColor: theme.colors.neutralBackground,
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 16,
  },
  time: {
    color: theme.colors.neutralLightGrey,
    fontSize: 12,
    lineHeight: 18,
    ...theme.fw500,
    marginLeft: 'auto',
    alignSelf: 'flex-start',
    backgroundColor: 'transparent',
  },
}));
