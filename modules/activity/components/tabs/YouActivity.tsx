import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { useCallback, useEffect, useMemo } from 'react';
import { ActivityItem } from '../ActivityItem';
import { useGetInfiniteMyActivitiesQuery, useGetInfiniteFollowingActivitiesQuery, IActivityLog } from '@/apis/activity';
import { IconLoading } from '@/components/IconLoading';
import { Empty } from '@/components/Empty';
import { useIsFocused } from '@react-navigation/native';
import { TabFlashList } from '@/components/collapsing-tabs/TabFlashList';
import { Spacer } from '@/components/Spacer';
import { ListRenderItem } from '@shopify/flash-list';
import { useQueryClient } from '@tanstack/react-query';
import queryKeys from '@/utils/queryKeys';
import { BOTTOM_TAB_HEIGHT } from '@/utils/const';

type ActivityType = 'my' | 'following';

type Props = {
  type?: ActivityType;
};

export const YouActivity = ({ type = 'my' }: Props) => {
  const { styles } = useStyles(stylesheet);
  const isFocused = useIsFocused();
  // Use appropriate query based on type
  const queryClient = useQueryClient();
  const myActivitiesQuery = useGetInfiniteMyActivitiesQuery({ limit: 20 }, { enabled: type === 'my' });
  const followingActivitiesQuery = useGetInfiniteFollowingActivitiesQuery(
    { limit: 20 },
    { enabled: type === 'following' }
  );

  const query = type === 'my' ? myActivitiesQuery : followingActivitiesQuery;

  // Flatten pages data
  const activities = useMemo(() => {
    return query.data?.pages.flatMap((page) => page.data) ?? [];
  }, [query.data]);

  const renderItem = useCallback<ListRenderItem<IActivityLog>>(({ item }) => <ActivityItem activityItem={item} />, []);

  const keyExtractor = useCallback((item: IActivityLog) => item.id.toString(), []);

  const handleLoadMore = useCallback(async () => {
    if (query.hasNextPage && !query.isFetchingNextPage) {
      await query.fetchNextPage();
    }
  }, [query]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    if (isFocused) {
      query.refetch();
    }
  }, [isFocused]);

  const handleRefetchList = async () => {
    await queryClient.resetQueries({ queryKey: queryKeys.activities.followingActivitiesInfinite() });
  };

  const renderGap = useCallback(() => <Spacer height={24} />, []);

  const isShowEmpty = query.isSuccess && activities.length === 0;

  return (
    <TabFlashList
      bounces={false}
      data={activities}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.5}
      refreshing={query.isPending}
      onRefresh={handleRefetchList}
      ListHeaderComponent={renderGap}
      contentContainerStyle={styles.contentContainer}
      style={styles.container}
      showsVerticalScrollIndicator={false}
      ListFooterComponent={query.isFetchingNextPage ? <IconLoading /> : null}
      ItemSeparatorComponent={renderGap}
      ListEmptyComponent={isShowEmpty ? <Empty type='post' emptyText='No activity found' /> : null}
    />
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    backgroundColor: theme.colors.neutralBackground,
    paddingTop: 24,
  },
  contentContainer: {
    // gap: 24,
    paddingHorizontal: 24,
    paddingBottom: BOTTOM_TAB_HEIGHT,
  },
  columnWrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 11,
  },
  skeletonContainer: {
    gap: 11,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
}));
