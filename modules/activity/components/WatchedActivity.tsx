import { ThemedText } from '@/components/ThemedText';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { useMemo } from 'react';
import { useIsYou } from '@/hooks/useIsYou';
import { ActivityTypeEnum } from '@/apis/activity';

type Props = {
  type: ActivityTypeEnum;
  time: string;
  name: string;
  username: string;
  userId: string;
};

export const WatchedActivity = ({ type, time, name, userId, username }: Props) => {
  const { styles } = useStyles(stylesheet);

  const nameFormat = name?.trim();

  const isYou = useIsYou({
    userId: userId,
  });

  const beginTitle = useMemo(() => {
    if (type === ActivityTypeEnum.MARK_WATCHED) return `${isYou ? 'You' : username} watched on ${time}`;
    if (type === ActivityTypeEnum.UNMARK_WATCHED) return `${isYou ? 'You' : username} unmark as watched`;

    return '';
  }, [type, isYou, username, time]);

  return (
    <View style={styles.container}>
      <ThemedText type='smallNormal' numberOfLines={3}>
        {beginTitle}{' '}
        <ThemedText type='small' style={styles.title}>
          {nameFormat}
        </ThemedText>
      </ThemedText>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  title: {
    ...theme.fw600,
  },
}));
