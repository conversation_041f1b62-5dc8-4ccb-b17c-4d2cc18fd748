import { ThemedText } from '@/components/ThemedText';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { useMemo } from 'react';
import { useIsYou } from '@/hooks/useIsYou';
import { ActivityTypeEnum } from '@/apis/activity';

type Props = {
  type: ActivityTypeEnum;
  text: string;
  poster: string;
  name: string;
  username: string;
  userId: string;
};

export const ReplyActivity = ({ type, text, poster, name, userId, username }: Props) => {
  const { styles } = useStyles(stylesheet);

  const nameFormat = name?.trim();

  const isYou = useIsYou({
    userId: userId,
  });

  const title = useMemo(() => {
    if (type === ActivityTypeEnum.REPLY) return `${isYou ? 'You' : username} posted reply to ${poster}'s comment on`;
    if (type === ActivityTypeEnum.EDIT_REPLY)
      return `${isYou ? 'You' : username} edited reply to ${poster}'s comment on`;
    if (type === ActivityTypeEnum.DELETE_REPLY)
      return `${isYou ? 'You' : username} deleted reply to ${poster}'s comment on`;

    return '';
  }, [type, poster, isYou, username]);

  return (
    <View style={styles.container}>
      <ThemedText type='smallNormal' numberOfLines={3}>
        {title}{' '}
        <ThemedText type='small' style={styles.title}>
          {nameFormat}
          {text ? `: ${text}` : ''}
        </ThemedText>
      </ThemedText>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  title: {
    ...theme.fw600,
  },
}));
