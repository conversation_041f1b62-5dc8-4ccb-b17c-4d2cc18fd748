import { Header } from '@/components/ui/Header';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { YouActivity } from './components/tabs/YouActivity';
import { View } from 'react-native';
import { TabContainer } from '@/components/collapsing-tabs';
import { TabWrapper } from '@/components/collapsing-tabs/TabWrapper';

export default function Activity() {
  const { styles } = useStyles(stylesheet);

  return (
    <View style={styles.container}>
      <Header title='Activity' />

      <TabContainer containerStyle={styles.tabContainer} headerContainerStyle={styles.headerContainer}>
        <TabWrapper tabName='You' label={'Your activity'}>
          <YouActivity type='my' />
        </TabWrapper>

        <TabWrapper tabName='Friends' label={'Friends activity'}>
          <YouActivity type='following' />
        </TabWrapper>
      </TabContainer>
    </View>
  );
}

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    paddingTop: rt.insets.top,
    backgroundColor: theme.colors.neutralBackground,
    flex: 1,
  },
  tabContainer: {
    flex: 1,
    overflow: 'hidden',
  },
  headerContainer: {
    backgroundColor: theme.colors.neutralBackground,
  },
  logoutTextStyle: {
    color: theme.colors.neutralBackground,
  },
}));
