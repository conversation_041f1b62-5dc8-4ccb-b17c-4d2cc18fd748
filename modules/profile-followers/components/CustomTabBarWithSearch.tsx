import { TabBar } from '@/components/collapsing-tabs/TabBar';
import { TabBarProps } from '@/components/collapsing-tabs/TabNavigator';
import { SearchInput } from '@/components/SearchInput';
import { Spacer } from '@/components/Spacer';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

interface CustomTabBarWithSearchProps extends TabBarProps {
  searchQuery: string;
  onSearchChange: (text: string) => void;
  [key: string]: any; // Để nhận các props khác từ TabBar
}

export const CustomTabBarWithSearch = ({
  searchQuery,
  onSearchChange,
  ...tabBarProps
}: CustomTabBarWithSearchProps) => {
  const { styles } = useStyles(stylesheet);

  return (
    <View style={styles.container}>
      <TabBar {...tabBarProps} />

      <Spacer height={24} />

      <View style={styles.searchContainer}>
        <SearchInput placeholder='Search Person' value={searchQuery} onChangeText={onSearchChange} />
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    backgroundColor: theme.colors.background,
  },
  searchContainer: {
    paddingHorizontal: 24,
    paddingBottom: 24,
    height: 72,
  },
}));
