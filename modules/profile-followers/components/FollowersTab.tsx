import { useCallback, useMemo, useState, useEffect } from 'react';
import { ListRenderItem } from '@shopify/flash-list';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

import { UserListItem } from '@/components/ui/UserListItem';
import { Spacer } from '@/components/Spacer';
import { IconLoading } from '@/components/IconLoading';
import { FlashListAnimate } from '@/components/FlashListAnimate';
import { useGetFollowersInfiniteQuery, type IFollow } from '@/apis/follow';
import { router } from 'expo-router';
import { useGetProfileQuery } from '@/apis/auth/queries';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';
import { v4 as uuid } from 'uuid';
import { Show } from '@/components/Show';
import { Empty } from '@/components/Empty';
const SEARCH_HEIGHT = 104;

interface FollowersTabProps {
  onFollowToggle: (userId: string, isFollowed: boolean) => void;
  searchQuery: string;
  targetUserId?: string;
  isOwner: boolean;
  refetchFollowers: () => void;
  displayUserName: string;
  totalFollowers: number;
}

export const FollowersTab = ({
  onFollowToggle,
  searchQuery,
  targetUserId,
  isOwner,
  refetchFollowers,
  displayUserName,
  totalFollowers,
}: FollowersTabProps) => {
  const { styles } = useStyles(stylesheet);
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState(searchQuery);
  const { data: userProfile } = useGetProfileQuery();
  const { onCheckAccountRestricted } = useCheckRestrictAccount();
  const [listKey, setListKey] = useState(uuid());

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  const {
    data: followersData,
    isLoading,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch,
    error,
  } = useGetFollowersInfiniteQuery({
    targetUserId: Number(targetUserId),
    limit: 20,
    search: debouncedSearchQuery,
  });

  const allFollowers = useMemo(() => {
    const infiniteData = followersData as any;
    if (!infiniteData?.pages) return [];

    return infiniteData.pages.flatMap((page: any) => page.data || []);
  }, [followersData]);

  const handleFollowToggle = useCallback(
    (userId: string, isFollowed: boolean) => {
      onFollowToggle(userId, isFollowed);
    },
    [onFollowToggle]
  );

  const renderUserItem = useCallback<ListRenderItem<IFollow>>(
    ({ item }) => {
      return (
        <UserListItem
          user={item}
          onUserPress={() => {
            const isRestricted = onCheckAccountRestricted();
            if (isRestricted) return;

            router.push({
              pathname: '/(app)/[userId]',
              params: {
                userId: item?.id?.toString(),
              },
            });
          }}
          onFollowToggle={handleFollowToggle}
          showFollowButton={item.id !== userProfile?.id}
        />
      );
    },
    [handleFollowToggle, onCheckAccountRestricted, userProfile?.id]
  );

  const keyExtractor = useCallback((item: IFollow) => item.id?.toString() || '', []);

  const renderSeparator = useCallback(() => <Spacer height={24} />, []);

  const onEndReached = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const handleRefresh = useCallback(async () => {
    await Promise.all([refetchFollowers(), refetch()]);
  }, [refetch, refetchFollowers]);

  return (
    <FlashListAnimate
      data={allFollowers}
      renderItem={renderUserItem}
      keyExtractor={keyExtractor}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.listContainer}
      ItemSeparatorComponent={renderSeparator}
      onEndReached={onEndReached}
      onEndReachedThreshold={0.5}
      onRefresh={handleRefresh}
      ListFooterComponent={isFetchingNextPage ? <IconLoading /> : null}
      flashListKey={listKey}
      ListEmptyComponent={
        <Show when={totalFollowers === 0}>
          <Empty
            type='follow'
            emptyText={`${isOwner ? 'You' : displayUserName} ${isOwner ? `haven't` : `hasn't`} got any followers yet.`}
          />
        </Show>
      }
    />
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  listContainer: {
    paddingHorizontal: 24,
    paddingBottom: rt.insets.bottom + 24,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
}));
