import { ICategoryResponse } from '@/apis/category';
import { useGetCategoriesInfiniteQuery } from '@/apis/category/queries';
import { SearchInput } from '@/components/SearchInput';
import { Spacer } from '@/components/Spacer';
import { Header } from '@/components/ui/Header';
import { getItemSizeFlatList } from '@/utils/func';
import { useDebouncedValue } from '@mantine/hooks';
import { useCallback, useEffect, useState } from 'react';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { CategoryItem } from './components/CategoryItem';
import { useQueryClient } from '@tanstack/react-query';
import { IconLoading } from '@/components/IconLoading';
import queryKeys from '@/utils/queryKeys';
import { FlashListAnimate } from '@/components/FlashListAnimate';
import { ListRenderItem } from '@shopify/flash-list';

const itemSize = getItemSizeFlatList(24, 2, 20);

export const CategoriesAll = () => {
  const { styles } = useStyles(stylesheet);

  const [searchValue, setSearchValue] = useState('');

  const [searchDebounced] = useDebouncedValue(searchValue, 200);
  const queryClient = useQueryClient();

  const { data, hasNextPage, isFetchingNextPage, fetchNextPage } = useGetCategoriesInfiniteQuery({
    limit: 40,
    page: 1,
    search: searchDebounced,
    isTrending: true,
  });
  const pages = data?.pages ?? [];
  const allCategories = pages?.flatMap((page) => page.data) ?? [];

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    return () => {
      queryClient.removeQueries({ queryKey: queryKeys.categories.categoriesInfinite() });
    };
  }, [queryClient, searchDebounced]);

  const renderItem = useCallback<ListRenderItem<ICategoryResponse>>(
    ({ item }) => (
      <CategoryItem
        categoryId={item.id}
        title={item.name}
        totalPodcasts={item.totalPodcasts}
        itemSize={itemSize}
        podcastImage={item?.imageUrl}
      />
    ),
    []
  );

  const onEndReached = useCallback(async () => {
    if (hasNextPage && !isFetchingNextPage) {
      await fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const keyExtractor = useCallback((item: ICategoryResponse) => item.id.toString(), []);

  const renderSpacer = useCallback(() => <Spacer height={20} />, []);

  return (
    <View style={styles.container}>
      <View style={styles.containerPadding}>
        <Header title='All Categories' isBack />
      </View>

      <View style={styles.containerPadding}>
        <Spacer height={32} />

        <SearchInput placeholder='Search Categories' value={searchValue} onChangeText={setSearchValue} />

        <Spacer height={32} />
      </View>

      <FlashListAnimate
        showsVerticalScrollIndicator={false}
        numColumns={2}
        data={allCategories}
        keyboardShouldPersistTaps='handled'
        keyExtractor={keyExtractor}
        contentContainerStyle={styles.contentContainer}
        onEndReachedThreshold={0.5}
        onEndReached={onEndReached}
        renderItem={renderItem}
        ListFooterComponent={isFetchingNextPage ? <IconLoading /> : null}
        ListFooterComponentStyle={styles.skeletonContainer}
        scrollEventThrottle={16}
        bounces={false}
        ItemSeparatorComponent={renderSpacer}
        estimatedItemSize={109}
      />
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.neutralBackground,
    gap: 32,
    paddingTop: rt.insets.top,
    paddingBottom: rt.insets.bottom,
  },
  containerPadding: {
    paddingHorizontal: 24,
  },
  contentContainer: {
    paddingBottom: 20,
    paddingHorizontal: 24,
  },
  columnWrapper: {
    gap: 20,
    justifyContent: 'space-between',
  },
  skeletonContainer: {
    flexDirection: 'row',
    gap: 20,
  },
}));
