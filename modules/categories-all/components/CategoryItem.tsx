import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { ExpoImage } from '@/components/ui/Image';
import { formatCompactNumber } from '@/utils/func';
import { router } from 'expo-router';
import { memo } from 'react';
import { TouchableOpacity } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  categoryId: string;
  title: string;
  totalPodcasts: number;
  itemSize: number;
  podcastImage?: string;
};

export const CategoryItem = memo(
  ({ categoryId, title, totalPodcasts, itemSize, podcastImage }: Props) => {
    const { styles } = useStyles(stylesheet);

    const handleDirect = () => {
      router.push({
        pathname: '/(app)/category/[id]',
        params: {
          id: categoryId,
          name: title,
        },
      });
    };

    return (
      <TouchableOpacity activeOpacity={0.7} style={[styles.container, { width: itemSize }]} onPress={handleDirect}>
        <ThemedText type='tinyMedium' style={styles.title} numberOfLines={2}>
          {title}
        </ThemedText>

        <Spacer height={7} />

        <ThemedText
          style={styles.totalShow}
          type='tinyMedium'
        >{`${formatCompactNumber(totalPodcasts)} ${totalPodcasts > 1 ? 'Shows' : 'Show'}`}</ThemedText>

        <ExpoImage source={{ uri: podcastImage }} style={styles.image} />
      </TouchableOpacity>
    );
  },
  (prev, next) => prev.categoryId === next.categoryId
);

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    height: 109,
    padding: 12,
    flexDirection: 'column',
    justifyContent: 'center',
    backgroundColor: theme.colors.neutralCard,
    borderRadius: 16,
    overflow: 'hidden',
  },
  totalShow: {
    opacity: 0.56,
  },
  image: {
    position: 'absolute',
    right: -45,
    width: 90,
    height: 90,
    objectFit: 'cover',
    borderRadius: 16,
  },
  title: {
    maxWidth: rt.screen.width / 2 - 95,
  },
}));
