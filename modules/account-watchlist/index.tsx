import { Header } from '@/components/ui/Header';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { useLocalSearchParams } from 'expo-router';
import { useIsYou } from '@/hooks/useIsYou';
import { useGetUserProfileByIdQuery } from '@/apis/user/queries';
import { WatchlistAll } from './components/WatchlistAll';
import { TabContainer } from '@/components/collapsing-tabs';
import { TabWrapper } from '@/components/collapsing-tabs/TabWrapper';

export const AccountWatchlist = () => {
  const { styles } = useStyles(stylesheet);
  const { userId } = useLocalSearchParams<{ userId: string }>();

  const { data: userProfileById, isPending: isPendingUserProfile } = useGetUserProfileByIdQuery(userId ?? '', {
    enabled: !!userId,
  });

  const isYou = useIsYou({
    userId: userProfileById?.id?.toString() ?? '',
  });

  return (
    <View style={styles.container}>
      <View style={styles.containerPadding}>
        <Header
          title={isPendingUserProfile ? '' : isYou ? 'My Watchlist' : `${userProfileById?.username} Watchlist`}
          isBack
        />
      </View>

      <TabContainer containerStyle={styles.tabContainer}>
        <TabWrapper tabName='All' label={'All'}>
          <WatchlistAll userId={userId} tab={'All'} profile={userProfileById} />
        </TabWrapper>

        <TabWrapper tabName='Shows' label={'Shows'}>
          <WatchlistAll userId={userId} tab={'Shows'} profile={userProfileById} />
        </TabWrapper>

        <TabWrapper tabName='Episodes' label={'Episodes'}>
          <WatchlistAll userId={userId} tab={'Episodes'} profile={userProfileById} />
        </TabWrapper>
      </TabContainer>
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.neutralBackground,
    paddingTop: rt.insets.top,
    paddingBottom: rt.insets.bottom,
  },
  containerPadding: {
    paddingHorizontal: 24,
  },
  tabContainer: {
    flex: 1,
    overflow: 'hidden',
  },
  headerContainer: {
    backgroundColor: 'transparent',
  },
}));
