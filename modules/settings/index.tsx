import { Icons } from '@/assets/icons';
import { HeaderStyle } from '@/components/HeaderStyle';
import { ThemedText } from '@/components/ThemedText';
import { useDevMode } from '@/hooks/useDevMode';
import { useHeaderStyleAnimated } from '@/hooks/useHeaderStyleAnimated';
import { useUserStore } from '@/store/user';
import { useQueryClient } from '@tanstack/react-query';
import Constants from 'expo-constants';
import { router } from 'expo-router';
import { Image } from 'react-native';
import { TouchableOpacity, View } from 'react-native';
import Animated from 'react-native-reanimated';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

const version = Constants.expoConfig?.version;

type Props = {};

export const Settings = (props: Props) => {
  const { onScroll, scrollY, headerHeight, onHeaderLayout } = useHeaderStyleAnimated();

  const { styles, theme } = useStyles(stylesheet);
  const signOut = useUserStore.use.signOut();
  // const setIsFirstTime = useCommonStore.use.setIsFirstTime();
  const queryClient = useQueryClient();

  const { onToggle } = useDevMode();

  const handleSignOut = () => {
    router.dismissAll();
    queryClient.removeQueries();
    signOut();
    // setIsFirstTime(true);
  };

  const settingsData = [
    {
      title: 'General',
      items: [
        {
          id: 'account',
          icon: Icons.PeopleEdit,
          title: 'Account Settings',
          description: 'Manage your personal details and sign in preferences',
          onPress: () => {
            router.push({
              pathname: '/(app)/settings/account',
              params: {
                title: 'Account Settings',
              },
            });
          },
        },
        {
          id: 'subscription',
          icon: ({ size, color }: { size: number; color: string }) => (
            <Image source={require('@/assets/images/premium_plan.png')} style={{ width: size, height: size }} />
          ),
          title: 'Subscription Plan',
          description: 'Manage your Subscription Plan',
          onPress: () => {
            router.push({
              pathname: '/(app)/subscription-plan',
            });
          },
        },
        {
          id: 'referral',
          icon: Icons.Gift,
          title: 'Your Referral',
          description: 'Ask your friends to join Rabid',
          onPress: () => {
            router.push({
              pathname: '/(app)/settings/your-referral',
              params: {
                title: 'Your Referral',
              },
            });
          },
        },
      ],
    },
    {
      title: 'Support',
      items: [
        {
          id: 'help',
          icon: Icons.CircleQuestionmark,
          title: 'Help & Support',
          description: 'Get assistance or report an issue',
          onPress: () => {
            router.push({
              pathname: '/(app)/settings/help-support',
              params: {
                title: 'Help & Support',
              },
            });
          },
        },
        {
          id: 'terms',
          icon: Icons.Notebook,
          title: 'Term & Conditions',
          description: 'Review Term & Conditions documents',
          onPress: () => {
            router.push({
              pathname: '/(app)/settings/terms',
              params: {
                title: 'Term & Conditions',
              },
            });
          },
        },
        {
          id: 'privacy',
          icon: Icons.Notebook,
          title: 'Privacy Policy',
          description: 'Review Privacy Policy documents',
          onPress: () => {
            router.push({
              pathname: '/(app)/settings/privacy',
              params: {
                title: 'Privacy Policy',
              },
            });
          },
        },
      ],
    },
  ];

  const renderSettingItem = ({ icon: IconComponent, title, description, onPress }: any) => (
    <TouchableOpacity style={styles.settingItem} onPress={onPress} activeOpacity={0.7}>
      <View style={styles.iconContainer}>
        <IconComponent size={32} color={theme.colors.neutralWhite} />
      </View>

      <View style={styles.settingContent}>
        <ThemedText type='defaultMedium' style={styles.settingTitle}>
          {title}
        </ThemedText>
        <ThemedText type='tinyMedium' style={styles.settingDescription}>
          {description}
        </ThemedText>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <HeaderStyle title='Settings' scrollY={scrollY} onHeaderLayout={onHeaderLayout} />

      <Animated.ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        onScroll={onScroll}
        contentContainerStyle={{ paddingTop: headerHeight + 40 }}
      >
        {settingsData.map((section, sectionIndex) => (
          <View key={section.title} style={styles.section}>
            <ThemedText type='subtitleSemiBold' style={styles.sectionTitle}>
              {section.title}
            </ThemedText>

            <View style={styles.sectionContent}>
              {section.items.map((item, itemIndex) => (
                <View key={item.id}>
                  {renderSettingItem(item)}
                  {itemIndex < section.items.length - 1 && <View style={styles.itemSeparator} />}
                </View>
              ))}
            </View>
          </View>
        ))}

        <TouchableOpacity activeOpacity={0.7} onPress={onToggle}>
          <View style={styles.versionContainer}>
            <ThemedText type='tinyMedium' style={{ color: '#fff' }}>{`Version: ${version}`}</ThemedText>
          </View>
        </TouchableOpacity>
      </Animated.ScrollView>

      <View style={styles.signOutContainer}>
        <TouchableOpacity activeOpacity={0.7} onPress={handleSignOut} style={styles.signOutButton}>
          <ThemedText type='defaultBold' style={styles.signOutText}>
            Sign Out
          </ThemedText>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.neutralBackground,
    paddingBottom: rt.insets.bottom,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 24,
    // paddingTop: 40,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    color: theme.colors.neutralWhite,
  },
  sectionContent: {
    backgroundColor: 'transparent',
    alignContent: 'flex-start',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 24,
  },
  iconContainer: {
    width: 56,
    height: 56,
    borderRadius: 16,
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: theme.colors.whiteOpacity4,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 24,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    color: theme.colors.neutralWhite,
    marginBottom: 4,
  },
  settingDescription: {
    color: theme.colors.whiteOpacity56,
  },
  itemSeparator: {
    height: 1,
    backgroundColor: theme.colors.whiteOpacity10,
  },
  signOutContainer: {
    paddingHorizontal: 24,
    paddingVertical: 28,
  },
  signOutButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    height: 48,
    backgroundColor: theme.colors.stateErrorOpacity20,
    borderRadius: 9999,
    gap: 16,
    marginHorizontal: 35,
  },
  signOutText: {
    color: theme.colors.stateError,
    zIndex: 1,
  },
  versionContainer: {
    paddingVertical: 5,
    justifyContent: 'center',
    alignItems: 'center',
  },
}));
