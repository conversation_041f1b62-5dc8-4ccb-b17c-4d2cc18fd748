import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { HeaderStyle } from '@/components/HeaderStyle';
import { WebView } from 'react-native-webview';
import { IconLoading } from '@/components/IconLoading';
import { useHeaderStyleAnimated } from '@/hooks/useHeaderStyleAnimated';

const PrivacyScreen = () => {
  const { styles } = useStyles(stylesheet);
  const { headerHeight, onHeaderLayout } = useHeaderStyleAnimated();

  return (
    <View style={styles.container}>
      <HeaderStyle title='Privacy Policy' onHeaderLayout={onHeaderLayout} />

      <View style={[styles.webviewContainer]}>
        <WebView
          containerStyle={{ paddingTop: headerHeight + 24 }}
          source={{ uri: 'https://rabid.var-meta.com/static/privacy.html' }}
          style={styles.webview}
          renderLoading={() => <IconLoading />}
          showsVerticalScrollIndicator={false}
        />
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.neutralBackground,
  },
  webview: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  webviewContainer: {
    flex: 1,
    backgroundColor: 'transparent',
    paddingHorizontal: 24,
  },
}));

export default PrivacyScreen;
