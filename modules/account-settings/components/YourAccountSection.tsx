import { ThemedText } from '@/components/ThemedText';
import { Control, Controller, FieldErrors } from 'react-hook-form';
import { TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { AccountSettingsFormData } from '../schema';
import TextInput from '@/components/ui/TextInput';
import { router } from 'expo-router';
import { useGetProfileQuery } from '@/apis/auth/queries';
import { IconLoading } from '@/components/IconLoading';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';

type Props = {
  control: Control<AccountSettingsFormData>;
  errors: FieldErrors<AccountSettingsFormData>;
};

export const YourAccountSection = ({ control, errors }: Props) => {
  const { styles } = useStyles(stylesheet);
  const { data: profile, isLoading: isLoadingProfile } = useGetProfileQuery();
  const { onCheckAccountRestricted } = useCheckRestrictAccount();

  const handleEditAccount = () => {
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    router.push({
      pathname: '/edit-account',
      params: {
        email: profile?.email || '',
        phoneNumber: profile?.phoneNumber || '',
      },
    });
  };

  if (isLoadingProfile) {
    return (
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <ThemedText type='defaultSemiBold' style={styles.sectionTitle}>
            Your Account
          </ThemedText>
        </View>
        <View style={styles.sectionContent}>
          <IconLoading />
        </View>
      </View>
    );
  }

  return (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <ThemedText type='defaultSemiBold' style={styles.sectionTitle}>
          Your Account
        </ThemedText>
        <TouchableOpacity style={styles.actionButton} onPress={handleEditAccount}>
          <ThemedText type='tinySemiBold' style={styles.actionButtonText}>
            Edit Account
          </ThemedText>
        </TouchableOpacity>
      </View>

      <View style={styles.sectionContent}>
        <View style={styles.inputContainer}>
          <Controller
            control={control}
            name='email'
            render={({ field: { onChange, onBlur } }) => {
              const email = profile?.email;

              if (!email) {
                return (
                  <View style={styles.infoContainer}>
                    <ThemedText type='defaultSemiBold'>Email</ThemedText>
                    <View>
                      <ThemedText type='default' style={styles.infoText}>
                        You haven't added an email yet.
                      </ThemedText>
                      <TouchableOpacity onPress={handleEditAccount}>
                        <ThemedText type='defaultMedium' style={styles.addText}>
                          Add your email
                        </ThemedText>
                      </TouchableOpacity>
                    </View>
                  </View>
                );
              }

              return (
                <TextInput
                  label='Email'
                  placeholder='Enter your email'
                  onChangeText={(value) => onChange(value.replace(/\s/g, ''))}
                  onBlur={onBlur}
                  value={email}
                  keyboardType='email-address'
                  autoCapitalize='none'
                  error={errors.email?.message}
                  editable={false}
                  inputStyle={styles.inputStyle}
                />
              );
            }}
          />
        </View>

        <View style={[styles.inputContainer]}>
          <Controller
            control={control}
            name='phoneNumber'
            render={({ field: { onChange, onBlur } }) => {
              const phoneNumber = profile?.phoneNumber;

              if (!phoneNumber) {
                return (
                  <View style={styles.infoContainer}>
                    <ThemedText type='defaultSemiBold'>Phone Number</ThemedText>
                    <View>
                      <ThemedText type='default' style={styles.infoText}>
                        You haven't added a phone number yet.
                      </ThemedText>
                      <TouchableOpacity onPress={handleEditAccount}>
                        <ThemedText type='defaultMedium' style={styles.addText}>
                          Add your phone number
                        </ThemedText>
                      </TouchableOpacity>
                    </View>
                  </View>
                );
              }

              return (
                <TextInput
                  label='Phone Number'
                  placeholder='Add your phone number'
                  onChangeText={(value) => onChange(value.replace(/\s/g, ''))}
                  onBlur={onBlur}
                  value={phoneNumber}
                  keyboardType='phone-pad'
                  error={errors.phoneNumber?.message}
                  editable={false}
                  inputStyle={styles.inputStyle}
                />
              );
            }}
          />
        </View>
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  section: {
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.whiteOpacity10,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  sectionTitle: {
    color: theme.colors.neutralWhite,
    fontSize: 20,
  },
  actionButton: {
    backgroundColor: theme.colors.primaryOpacity10,
    paddingHorizontal: 16,
    paddingVertical: 7,
    borderRadius: 9999,
  },
  actionButtonText: {
    color: theme.colors.primary,
  },
  sectionContent: {},
  inputContainer: {
    paddingBottom: 20,
  },
  inputLabel: {
    color: theme.colors.neutralWhite,
    marginBottom: 16,
    fontSize: 16,
  },
  textInput: {
    backgroundColor: theme.colors.neutralCard,
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    minHeight: 48,
    color: theme.colors.neutralWhite,
    fontSize: 16,
  },
  infoContainer: {
    gap: 14,
  },
  infoText: {},
  addText: {
    color: theme.colors.primary,
  },
  inputStyle: {
    opacity: 1,
  },
}));
