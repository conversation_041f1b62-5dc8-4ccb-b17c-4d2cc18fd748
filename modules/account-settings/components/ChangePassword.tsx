import { Header } from '@/components/ui/Header';
import { ThemedText } from '@/components/ThemedText';
import TextInput from '@/components/ui/TextInput';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { router, useLocalSearchParams } from 'expo-router';
import { useChangePasswordMutation } from '@/apis/auth';
import { toastError } from '@/utils/toast';
import { Button } from '@/components/ui/Button';
import { ZOD_ERRORS } from '@/config/zodError';
import { REGEX } from '@/constants/regex';
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';

const changePasswordSchema = z
  .object({
    currentPassword: z
      .string()
      .min(8, ZOD_ERRORS.passwordLength)
      .max(64, ZOD_ERRORS.passwordLength)
      .regex(REGEX.PASSWORD, ZOD_ERRORS.passwordRegex),
    newPassword: z
      .string()
      .min(8, ZOD_ERRORS.passwordLength)
      .max(64, ZOD_ERRORS.passwordLength)
      .regex(REGEX.PASSWORD, ZOD_ERRORS.passwordRegex),
    confirmPassword: z
      .string()
      .min(8, ZOD_ERRORS.passwordLength)
      .max(64, ZOD_ERRORS.passwordLength)
      .regex(REGEX.PASSWORD, ZOD_ERRORS.passwordRegex),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

type ChangePasswordFormData = z.infer<typeof changePasswordSchema>;

export const ChangePassword = () => {
  const { styles } = useStyles(stylesheet);
  const localParams = useLocalSearchParams();
  const email = localParams?.email as string;
  const { onCheckAccountRestricted } = useCheckRestrictAccount();

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<ChangePasswordFormData>({
    resolver: zodResolver(changePasswordSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
  });

  const { mutate: changePassword, isPending: isChangingPassword } = useChangePasswordMutation({
    onSuccess: async (data, variables) => {
      router.push({
        pathname: '/(app)/verify-code',
        params: {
          identifierType: JSON.stringify([email]),
          isChangePassword: 'true',
          oldPassword: variables.oldPassword,
          newPassword: variables.newPassword,
        },
      });
    },
    onError: (error) => {
      toastError(error);
    },
  });

  const onSubmit = (data: ChangePasswordFormData) => {
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    changePassword({
      oldPassword: data.currentPassword,
      newPassword: data.newPassword,
    });
  };

  return (
    <View style={styles.container}>
      <Header isBack />

      <KeyboardAwareScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.formSection}>
          <ThemedText type='titleSemiBold' style={styles.title}>
            Change Your Password
          </ThemedText>

          <View style={styles.formContent}>
            <View style={styles.formContainer}>
              <View style={styles.inputFieldsContainer}>
                <View style={styles.inputContainer}>
                  <Controller
                    control={control}
                    name='currentPassword'
                    render={({ field: { onChange, onBlur, value } }) => (
                      <TextInput
                        label='Old Password'
                        placeholder='Enter Your Password'
                        onChangeText={onChange}
                        onBlur={onBlur}
                        value={value}
                        isPassword
                        error={errors.currentPassword?.message}
                      />
                    )}
                  />
                </View>

                <View style={styles.inputContainer}>
                  <Controller
                    control={control}
                    name='newPassword'
                    render={({ field: { onChange, onBlur, value } }) => (
                      <TextInput
                        label='Create a New Password'
                        placeholder='Enter Your Password'
                        onChangeText={onChange}
                        onBlur={onBlur}
                        value={value}
                        isPassword
                        error={errors.newPassword?.message}
                      />
                    )}
                  />
                </View>

                <View style={styles.inputContainer}>
                  <Controller
                    control={control}
                    name='confirmPassword'
                    render={({ field: { onChange, onBlur, value } }) => (
                      <TextInput
                        label='Confirm Your New Password'
                        placeholder='Re-enter Your Password'
                        onChangeText={onChange}
                        onBlur={onBlur}
                        value={value}
                        isPassword
                        error={errors.confirmPassword?.message}
                      />
                    )}
                  />
                </View>
              </View>
            </View>

            <Button style={[styles.submitButton]} onPress={handleSubmit(onSubmit)} isLoading={isChangingPassword}>
              <ThemedText type='defaultBold' style={styles.submitButtonText}>
                Continue
              </ThemedText>
            </Button>
          </View>
        </View>
      </KeyboardAwareScrollView>
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    paddingTop: rt.insets.top + 16,
    paddingHorizontal: 24,
  },
  content: {
    flex: 1,
    paddingTop: 56,
    paddingBottom: 40,
  },
  formSection: {
    gap: 32,
  },
  title: {
    color: theme.colors.neutralWhite,
    fontSize: 28,
    height: 32,
  },
  formContent: {
    gap: 60,
  },
  formContainer: {
    gap: 32,
  },
  inputFieldsContainer: {
    gap: 32,
  },
  inputContainer: {
    gap: 16,
  },
  submitButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 9999,
    alignItems: 'center',
    justifyContent: 'center',
  },
  submitButtonText: {
    color: theme.colors.neutralBackground,
    fontSize: 16,
  },
  submitButtonDisabled: {
    backgroundColor: theme.colors.primaryOpacity10,
  },
}));
