import { ThemedText } from '@/components/ThemedText';
import { Control, Controller, FieldErrors } from 'react-hook-form';
import { TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { AccountSettingsFormData } from '../schema';
import TextInput from '@/components/ui/TextInput';
import { router } from 'expo-router';
import { useGetProfileQuery } from '@/apis/auth/queries';
import { Show } from '@/components/Show';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';

type Props = {
  control: Control<AccountSettingsFormData>;
  errors: FieldErrors<AccountSettingsFormData>;
};

export const PasswordSection = ({ control, errors }: Props) => {
  const { styles } = useStyles(stylesheet);
  const { data: profile, isLoading: isLoadingProfile } = useGetProfileQuery();
  const isHasPassword = profile?.hasPassword || false;

  const { onCheckAccountRestricted } = useCheckRestrictAccount();

  const handleChangePassword = () => {
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    if (isHasPassword) {
      router.push({
        pathname: '/(app)/change-password',
        params: {
          email: profile?.email || '',
        },
      });
    } else {
      router.push({
        pathname: '/(app)/create-password',
        params: {
          identifier: profile?.email || '',
        },
      });
    }
  };

  return (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <ThemedText type='defaultSemiBold' style={styles.sectionTitle}>
          Password
        </ThemedText>
        <TouchableOpacity style={styles.actionButton} onPress={handleChangePassword}>
          <ThemedText type='tinySemiBold' style={styles.actionButtonText}>
            {isHasPassword ? 'Change Password' : 'Add Password'}
          </ThemedText>
        </TouchableOpacity>
      </View>
      <View style={styles.sectionContent}>
        <ThemedText type='smallNormal' style={[styles.infoText, { marginTop: 12 }]}>
          This password applies to both email and phone number sign-in
        </ThemedText>
      </View>
      <Show when={isHasPassword}>
        <View style={styles.sectionContent}>
          <View style={styles.inputContainer}>
            <Controller
              control={control}
              name='password'
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  label='Password'
                  placeholder='Enter your password'
                  onChangeText={onChange}
                  onBlur={onBlur}
                  value='password'
                  secureTextEntry
                  error={errors.password?.message}
                  editable={false}
                  inputStyle={styles.inputStyle}
                />
              )}
            />
          </View>
        </View>
      </Show>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  section: {
    marginBottom: 60,
    marginTop: 32,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  sectionTitle: {
    color: theme.colors.neutralWhite,
    fontSize: 20,
  },
  actionButton: {
    backgroundColor: theme.colors.primaryOpacity10,
    paddingHorizontal: 16,
    paddingVertical: 7,
    borderRadius: 9999,
  },
  actionButtonText: {
    color: theme.colors.primary,
  },
  sectionContent: {},
  inputContainer: {
    paddingVertical: 20,
  },
  inputLabel: {
    color: theme.colors.neutralWhite,
    marginBottom: 16,
  },
  textInput: {
    backgroundColor: theme.colors.neutralCard,
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    minHeight: 48,
    color: theme.colors.neutralWhite,
    fontSize: 16,
  },
  infoText: {
    lineHeight: 24,
  },
  inputStyle: {
    fontSize: 16,
    lineHeight: 24,
    letterSpacing: 0,
    fontWeight: '400',
    opacity: 1,
  },
}));
