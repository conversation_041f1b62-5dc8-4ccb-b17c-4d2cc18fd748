import { HeaderStyle } from '@/components/HeaderStyle';
import { zodResolver } from '@hookform/resolvers/zod';
import { KeyboardAvoidingView, Platform, View } from 'react-native';
import { useForm } from 'react-hook-form';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { PasswordSection } from './components/PasswordSection';
import { SignInMethodsSection } from './components/SignInMethodsSection';
import { YourAccountSection } from './components/YourAccountSection';
import { AccountSettingsFormData, accountSettingsSchema } from './schema';
import { useHeaderStyleAnimated } from '@/hooks/useHeaderStyleAnimated';
import Animated from 'react-native-reanimated';

type Props = {};

export const AccountSettings = (props: Props) => {
  const { styles } = useStyles(stylesheet);

  const { onScroll, scrollY, headerHeight, onHeaderLayout } = useHeaderStyleAnimated();

  const {
    control,
    formState: { errors },
  } = useForm<AccountSettingsFormData>({
    resolver: zodResolver(accountSettingsSchema),
    defaultValues: {
      email: '',
      phoneNumber: '',
      password: '',
    },
  });

  return (
    <View style={styles.container}>
      <HeaderStyle title='Account Settings' scrollY={scrollY} onHeaderLayout={onHeaderLayout} />

      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
      >
        <Animated.ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps='handled'
          contentContainerStyle={[styles.scrollContent, { paddingTop: headerHeight + 24 }]}
          onScroll={onScroll}
        >
          <SignInMethodsSection />
          <YourAccountSection control={control} errors={errors} />
          <PasswordSection control={control} errors={errors} />
        </Animated.ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.neutralBackground,
    paddingBottom: rt.insets.bottom,
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    minHeight: rt.screen.height,
  },
  scrollContent: {
    paddingHorizontal: 24,
    paddingTop: 32,
    paddingBottom: 32,
  },
}));
