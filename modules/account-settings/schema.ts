import { ZOD_ERRORS } from '@/config/zodError';
import { REGEX } from '@/constants/regex';
import { z } from 'zod';

export const accountSettingsSchema = z.object({
  email: z.string().email(ZOD_ERRORS.invalidEmail).max(254, ZOD_ERRORS.emailLength),
  phoneNumber: z
    .string()
    .trim()
    .optional()
    .refine(
      (val) => {
        if (!val || val === '') return true; // Allow empty phone number

        const hasOnlyValidChars = REGEX.PHONE_CHARS_ONLY.test(val);
        if (!hasOnlyValidChars) {
          return false;
        }

        const looksLikePhone = val.startsWith('+') && REGEX.DIGIT_ONLY.test(val.slice(1));
        const looksLikePhoneWithoutCountryCode = REGEX.DIGIT_ONLY.test(val);

        if (looksLikePhone) {
          const isValidPhone = REGEX.PHONE.test(val);
          // ignore character '+'
          if (val.length < 10 || val.length > 16) {
            return false;
          }
          if (!isValidPhone) {
            return false;
          }
          return true;
        } else if (looksLikePhoneWithoutCountryCode) {
          const isValidPhone = REGEX.PHONE_NO_COUNTRY_CODE.test(val);
          if (!isValidPhone) return false;
          return true;
        }

        return false;
      },
      (val) => {
        if (!val || val === '') return { message: '' };

        const hasOnlyValidChars = REGEX.PHONE_CHARS_ONLY.test(val);
        if (!hasOnlyValidChars) {
          return { message: ZOD_ERRORS.invalidPhoneNumber };
        }

        const looksLikePhone = val.startsWith('+') && REGEX.DIGIT_ONLY.test(val.slice(1));
        const looksLikePhoneWithoutCountryCode = REGEX.DIGIT_ONLY.test(val);

        if (looksLikePhone) {
          const isValidPhone = REGEX.PHONE.test(val);
          // ignore character '+'
          if (val.length < 10 || val.length > 16) {
            return { message: ZOD_ERRORS.phoneNumberLength };
          }
          if (!isValidPhone) {
            return { message: ZOD_ERRORS.invalidPhoneNumber };
          }
        } else if (looksLikePhoneWithoutCountryCode) {
          const isValidPhone = REGEX.PHONE_NO_COUNTRY_CODE.test(val);
          if (!isValidPhone) return { message: ZOD_ERRORS.phoneNumberNoCodeLength };
        }
        return { message: ZOD_ERRORS.invalidPhoneNumber };
      }
    ),
  password: z.string().optional(),
});

export type AccountSettingsFormData = z.infer<typeof accountSettingsSchema>;
