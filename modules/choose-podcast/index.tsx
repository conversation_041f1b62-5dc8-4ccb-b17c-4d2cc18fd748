import { useGetProfileQuery } from '@/apis/auth/queries';
import { IPodcast, useAddFavoritePodcastBulkMutation, useGetPodcastsInfiniteQuery } from '@/apis/podcast';
import { planPaymentMutation } from '@/apis/user';
import { FlashListAnimate } from '@/components/FlashListAnimate';
import { SearchInput } from '@/components/SearchInput';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { Button } from '@/components/ui/Button';
import { Header } from '@/components/ui/Header';
import { ExpoImage } from '@/components/ui/Image';
import { getItemSizeFlatList } from '@/utils/func';
import queryKeys from '@/utils/queryKeys';
import { toastError } from '@/utils/toast';
import { useQueryClient } from '@tanstack/react-query';
import { router } from 'expo-router';
import { Skeleton } from 'moti/skeleton';
import { useCallback, useState } from 'react';
import { Image, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type ItemType = IPodcast & { empty?: boolean };

const NUM_COLUMNS = 3;
const itemSize = getItemSizeFlatList(16, NUM_COLUMNS, 12);

export default function ChoosePodcast() {
  const { styles, theme } = useStyles(stylesheet);
  const [selectedPodcasts, setSelectedPodcasts] = useState<string[]>([]);
  const [searchValue, setSearchValue] = useState<string>('');

  const queryClient = useQueryClient();
  const { mutateAsync: chooseInterest, isPending: isPendingChooseInterest } = planPaymentMutation();
  const { mutateAsync: addFavoritePodcasts, isPending: isPendingAddFavoritePodcasts } =
    useAddFavoritePodcastBulkMutation();

  const { data: userProfile, isRefetching: isLoadingProfileQuery } = useGetProfileQuery();
  const {
    data: podcasts,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
    isFetching,
    refetch,
  } = useGetPodcastsInfiniteQuery({
    search: searchValue,
    limit: 20,
    page: 1,
    hasValidImage: true,
  });

  const allPodcast = podcasts?.pages?.map((page) => page.data)?.flat() ?? [];

  const togglePodcastSelection = useCallback(
    (podcastId: string) => {
      if (selectedPodcasts.includes(podcastId)) {
        setSelectedPodcasts(selectedPodcasts.filter((id) => id !== podcastId));
      } else {
        setSelectedPodcasts([...selectedPodcasts, podcastId]);
      }
    },
    [selectedPodcasts]
  );

  const refetchProfile = async () => {
    await Promise.all([
      queryClient.resetQueries({ queryKey: queryKeys.auth.profile() }),
      queryClient.refetchQueries({ queryKey: queryKeys.userProfile.byUserId(userProfile?.id || '') }),
    ]);
  };

  const handleContinue = async () => {
    try {
      await addFavoritePodcasts({
        podcastIds: selectedPodcasts.map((id) => Number(id)),
      });

      await chooseInterest({
        status: 'complete',
      });

      await refetchProfile();

      // Navigate to the next screen with selected podcasts
      router.replace({
        pathname: '/(app)/(tabs)',
      });
    } catch (error) {
      toastError(error);
    }
  };

  const handleSkip = async () => {
    try {
      await chooseInterest({
        status: 'complete',
      });

      await refetchProfile();

      router.replace({
        pathname: '/(app)/(tabs)',
      });
    } catch (error) {
      toastError(error);
    }
  };

  const handleBack = async () => {
    try {
      chooseInterest({
        status: 'choose_interest',
      });

      router.replace({
        pathname: '/(app)/(tabs)',
      });
    } catch (error) {
      toastError(error);
    }
  };

  const handleRefetch = async () => {
    await refetch();
  };

  const renderPodcastItem = useCallback(
    ({ item }: { item: ItemType }) => {
      const isSelected = selectedPodcasts.includes(item.id);

      return (
        <TouchableOpacity
          style={[styles.podcastItem, { width: itemSize }]}
          onPress={() => togglePodcastSelection(item.id)}
        >
          <ExpoImage source={{ uri: item.imageUrl }} style={styles.podcastImage} />

          {isSelected && (
            <View style={styles.checkIconContainer}>
              <Image
                source={require('@/assets/images/check_image_large.png')}
                style={{
                  width: 48,
                  height: 48,
                  tintColor: theme.colors.neutralWhite,
                  borderRadius: 12,
                }}
              />
            </View>
          )}
        </TouchableOpacity>
      );
    },
    [selectedPodcasts, styles, theme, togglePodcastSelection]
  );

  const renderSpacer = useCallback(() => <Spacer height={12} />, []);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Header
          isBack={true}
          onBackPress={handleBack}
          title='Favorite Podcast'
          rightAction={
            <TouchableOpacity onPress={handleSkip}>
              <Text style={styles.skipText}>Skip</Text>
            </TouchableOpacity>
          }
        />

        <FlashListAnimate
          showsVerticalScrollIndicator={false}
          data={allPodcast}
          ListHeaderComponent={<HeaderScreen searchValue={searchValue} setSearchValue={setSearchValue} />}
          renderItem={renderPodcastItem}
          keyExtractor={(item) => item.id}
          numColumns={3}
          ItemSeparatorComponent={renderSpacer}
          onEndReached={() => {
            if (hasNextPage && !isFetchingNextPage) {
              fetchNextPage();
            }
          }}
          style={{ flex: 1 }}
          onEndReachedThreshold={0.5}
          ListFooterComponentStyle={styles.skeletonContainer}
          ListFooterComponent={
            isFetching
              ? () => (
                  <>
                    {[...Array(12)].map((_, index) => (
                      <View style={{ flex: 1, minWidth: '30%' }} key={index}>
                        <Skeleton key={index} width={'100%'} height={120} radius={16} />
                      </View>
                    ))}
                  </>
                )
              : null
          }
          estimatedItemSize={itemSize}
        />
      </View>

      <View style={styles.bottomContainer}>
        <Button
          isLoading={isPendingChooseInterest || isLoadingProfileQuery || isPendingAddFavoritePodcasts}
          onPress={handleContinue}
          disabled={selectedPodcasts.length === 0}
        >
          Continue
        </Button>
      </View>
    </SafeAreaView>
  );
}

const HeaderScreen = ({
  searchValue,
  setSearchValue,
}: { searchValue: string; setSearchValue: (value: string) => void }) => {
  const { styles } = useStyles(stylesheet);

  return (
    <View>
      <ThemedText style={styles.subtitle}>
        Now, select your favorite podcast.{'\n'}
        Feel free to choose more than one!
      </ThemedText>

      <View style={styles.searchBox}>
        <SearchInput
          placeholder='Search Podcast shows or Episodes'
          value={searchValue}
          onChangeText={setSearchValue}
          autoFocus
        />
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 12,
  },
  subtitle: {
    color: theme.colors.neutralWhite,
    fontSize: 16,
    marginTop: 28,
    marginBottom: 32,
    lineHeight: 25.6,
    opacity: 0.64,
  },
  searchBox: {
    marginBottom: 32,
  },
  searchIcon: {
    width: 20,
    height: 20,
    marginRight: 8,
  },
  searchPlaceholder: {
    color: '#8E8E93',
    fontSize: 16,
  },
  podcastGrid: {
    gap: 12,
  },
  podcastGridColumn: {
    gap: 12,
    rowGap: 12,
  },
  podcastItem: {
    // flex: 1,
    aspectRatio: 1,
    borderRadius: 12,
  },
  podcastImage: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
  },
  checkIconContainer: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  bottomContainer: {
    position: 'static',
    bottom: 0,
    left: 0,
    right: 0,
    paddingTop: 44,
    paddingHorizontal: 59,
    paddingBottom: 40,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  skipText: {
    color: theme.colors.primary,
    fontSize: 16,
    fontFamily: 'Inter',
  },
  skeletonContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    width: '100%',
    marginBottom: 20,
    columnGap: 12,
    rowGap: 12,
  },
}));
