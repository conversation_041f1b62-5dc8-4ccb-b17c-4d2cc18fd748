import { Skeleton } from 'moti/skeleton';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

export const CommentSkeleton = () => {
  const { styles } = useStyles(stylesheet);

  return (
    <View style={styles.container}>
      <Skeleton width={40} height={40} radius={999} />

      <View style={styles.commentBox}>
        <Skeleton width={100} height={16} />

        <Skeleton width={100} height={20} />
      </View>

      <Skeleton width={20} height={16} />
    </View>
  );
};

const stylesheet = createStyleSheet(() => ({
  container: {
    flexDirection: 'row',
    gap: 16,
  },
  commentBox: {
    flex: 1,
    gap: 4,
  },
}));
