import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { Skeleton } from 'moti/skeleton';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

export const PostHeaderSkeleton = () => {
  const { styles } = useStyles(stylesheet);

  return (
    <View>
      <View style={styles.info}>
        <View style={styles.podcastInfo}>
          <View style={styles.posterInfoBox}>
            <Skeleton width={48} height={48} radius={999} />

            <Skeleton width={80} height={20} />
          </View>

          <Skeleton width={150} height={24} />

          <View>
            <Skeleton width={100} height={16} />
          </View>

          <Skeleton width={120} height={20} />
        </View>

        <Skeleton width={120} height={120} radius={5.12} />
      </View>

      <Spacer height={24} />

      <View style={styles.reviewBox}>
        <Skeleton width={200} height={24} />

        <Skeleton width={'100%'} height={50} />

        <View style={styles.reviewTimeBox}>
          <Skeleton width={80} height={20} />
          <Skeleton width={100} height={20} />
        </View>
      </View>

      <Spacer height={24} />

      <View style={styles.posterInfoBox}>
        <Skeleton width={70} height={24} />
        <Skeleton width={70} height={24} />
      </View>

      <Spacer height={32} />

      <View style={styles.bottomLine} />

      <Spacer height={24} />

      <ThemedText type='defaultMedium'>Newest Reply</ThemedText>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    height: 32,
  },
  avatarPoster: {
    width: 48,
    height: 48,
    borderRadius: 999,
  },
  posterInfoBox: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  podcastImage: {
    width: 120,
    height: 120,
    borderRadius: 5.12,
  },
  info: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },
  podcastInfo: {
    gap: 12,
    alignItems: 'flex-start',
  },
  podcastTitle: {},
  time: {
    opacity: 0.56,
  },
  reviewTimeBox: {
    flexDirection: 'row',
    gap: 20,
  },
  reviewTimeText: {
    color: theme.colors.neutralLightGrey,
  },
  reviewBox: {
    gap: 16,
  },
  bottomLine: {
    width: '100%',
    height: 1,
    backgroundColor: theme.colors.neutralGrey,
  },
}));
