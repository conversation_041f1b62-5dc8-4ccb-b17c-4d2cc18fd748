import { ThemedText } from '@/components/ThemedText';
import { Button } from '@/components/ui/Button';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  onPress: () => void;
  isLoading: boolean;
};

export const SaveButton = ({ onPress, isLoading }: Props) => {
  const { styles } = useStyles(stylesheet);

  return (
    <View style={styles.container}>
      <Button type='default' onPress={onPress} isLoading={isLoading} disabled={isLoading} style={styles.button}>
        <ThemedText type='defaultSemiBold' style={styles.buttonText}>
          Save
        </ThemedText>
      </Button>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    padding: 24,
    paddingBottom: 40,
    backgroundColor: theme.colors.background,
  },
  button: {
    width: '100%',
    height: 48,
    borderRadius: 9999,
    marginTop: 40,
  },
  buttonText: {
    color: theme.colors.background,
  },
}));
