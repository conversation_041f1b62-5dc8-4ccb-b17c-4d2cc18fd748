import { Icons } from '@/assets/icons';
import { toastError } from '@/utils/toast';
import * as ImagePicker from 'expo-image-picker';
import { ImagePickerAsset } from 'expo-image-picker';
import { useState } from 'react';
import { Alert, Image, TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

const defaultAvatar = require('@/assets/images/default_avatar.png');

type Props = {
  imageUri: string;
  onImageChange: (asset: ImagePickerAsset) => void;
};

export const ProfileImageSection = ({ imageUri, onImageChange }: Props) => {
  const { theme, styles } = useStyles(stylesheet);
  const [isUploadingImage, setIsUploadingImage] = useState(false);

  const openCamera = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();

    if (status !== 'granted') {
      Alert.alert('Permission denied', 'We need camera permissions to take a photo');
      return;
    }

    setIsUploadingImage(true);
    try {
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.5,
      });

      if (!result.canceled && result.assets[0]) {
        onImageChange(result.assets[0]);
      }
    } catch (error) {
      toastError(error);
    } finally {
      setIsUploadingImage(false);
    }
  };

  const openGallery = async () => {
    setIsUploadingImage(true);
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images',
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.5,
      });

      if (!result.canceled && result.assets[0]) {
        onImageChange(result.assets[0]);
      }
    } catch (error) {
      const hasErrorCode = (err: unknown): err is { code: string; message?: string } => {
        return typeof err === 'object' && err !== null && 'code' in err;
      };

      if (hasErrorCode(error)) {
        if (error.code === 'ERR_FAILED_TO_READ_IMAGE') {
          toastError('Image is invalid. Please try again');
        }
      }
    } finally {
      setIsUploadingImage(false);
    }
  };

  const pickImage = async () => {
    Alert.alert('Change Profile Picture', 'Choose an option', [
      {
        text: 'Take Photo',
        onPress: openCamera,
      },
      {
        text: 'Choose from Gallery',
        onPress: openGallery,
      },
      {
        text: 'Cancel',
        style: 'cancel',
      },
    ]);
  };

  return (
    <View style={styles.container}>
      <View style={styles.imageContainer}>
        <Image source={imageUri ? { uri: imageUri } : defaultAvatar} style={styles.profileImage} />
        <TouchableOpacity style={styles.editButton} onPress={pickImage} disabled={isUploadingImage}>
          <Icons.EditV2 size={16} color={theme.colors.background} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    alignItems: 'center',
    paddingTop: 56,
  },
  imageContainer: {
    position: 'relative',
  },
  profileImage: {
    width: 112,
    height: 112,
    borderRadius: 9999,
    backgroundColor: theme.colors.background,
  },
  editButton: {
    position: 'absolute',
    bottom: 13,
    right: -6,
    width: 30,
    height: 30,
    borderRadius: 999,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: theme.colors.background,
  },
}));
