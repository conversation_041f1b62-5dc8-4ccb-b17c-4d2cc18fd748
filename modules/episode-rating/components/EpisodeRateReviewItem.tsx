import { IEpisodeRate } from '@/apis/episode';
import { RateStar } from '@/components/RateStar';
import { Show } from '@/components/Show';
import { Spacer } from '@/components/Spacer';
import Tag from '@/components/Tag';
import { ThemedText } from '@/components/ThemedText';
import { UserProfileText } from '@/components/UserProfileText';
import { UserProfileTouch } from '@/components/UserProfileTouch';
import { Avatar } from '@/components/ui/Avatar';
import { useIsYou } from '@/hooks/useIsYou';
import { formatIntervalToNowDuration } from '@/utils/func';
import { useMemo } from 'react';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  rateReviewData: IEpisodeRate;
};

export const EpisodeRateReviewItem = ({ rateReviewData }: Props) => {
  const { styles, theme } = useStyles(stylesheet);

  const isYou = useIsYou({
    userId: rateReviewData.userId,
    userSource: rateReviewData.source,
  });

  const usernameDisplay = useMemo(() => {
    if (isYou) return 'You';

    return rateReviewData?.username;
  }, [rateReviewData?.username, isYou]);

  return (
    <View style={styles.reviewItem}>
      <UserProfileTouch userId={rateReviewData.userId} userType={rateReviewData.source}>
        <Avatar image={rateReviewData.avatar} imageStyle={styles.avatar} />
      </UserProfileTouch>

      <View style={styles.reviewContent}>
        <UserProfileText userId={rateReviewData.userId} userType={rateReviewData.source}>
          <ThemedText type='defaultMedium' style={[styles.reviewerName, isYou && styles.textPrimary]}>
            {usernameDisplay}
          </ThemedText>
        </UserProfileText>

        <Spacer height={8} />

        <View style={styles.ratingBox}>
          <RateStar rating={rateReviewData.rate || 0} size={16} gap={12} />

          <Show when={!!rateReviewData.tag}>
            <Tag tagName={rateReviewData.tag!} size={20} />
          </Show>
        </View>
      </View>

      <ThemedText style={styles.reviewDate}>{formatIntervalToNowDuration(rateReviewData.createdAt)}</ThemedText>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  reviewItem: {
    flexDirection: 'row',
    marginBottom: 20,
    paddingBottom: 20,
    borderBottomWidth: 1,
    width: '100%',
    borderBottomColor: theme.colors.neutralGrey,
  },
  avatar: {
    width: 56,
    height: 56,
    borderRadius: 999,
    backgroundColor: theme.colors.neutralGrey,
  },
  reviewContent: {
    flex: 1,
    marginLeft: 12,
  },
  reviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  reviewerName: {
    color: theme.colors.neutralWhite,
  },
  reviewDate: {
    color: theme.colors.whiteOpacity56,
  },
  textPrimary: {
    color: theme.colors.primary,
  },
  ratingBox: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
}));
