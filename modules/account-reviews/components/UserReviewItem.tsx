import { UserRateReview } from '@/apis/user';
import { RateStar } from '@/components/RateStar';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { ExpoImage } from '@/components/ui/Image';
import { episodeDetailDirect, showDetailDirect } from '@/utils/router-prefetch';
import { useQueryClient } from '@tanstack/react-query';
import { TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  userRateReview: UserRateReview;
};

export const UserReviewItem = ({ userRateReview }: Props) => {
  const { styles } = useStyles(stylesheet);
  const queryClient = useQueryClient();

  const handleDirect = async () => {
    if (userRateReview.type === 'episode') {
      return episodeDetailDirect(queryClient, userRateReview.mediaId.toString());
    }

    await showDetailDirect(queryClient, userRateReview.mediaId.toString());
  };

  return (
    <TouchableOpacity style={styles.container} activeOpacity={0.7} onPress={handleDirect}>
      <ExpoImage source={{ uri: userRateReview.mediaImageUrl }} style={styles.avatar} />

      <View style={styles.fullFlex}>
        <ThemedText type='small' numberOfLines={userRateReview.type === 'episode' ? 2 : 1}>
          {userRateReview.mediaName}
        </ThemedText>

        <Spacer height={8} />

        <View style={styles.flexStart}>
          <RateStar rating={userRateReview.rate} size={20} gap={4} />
        </View>
      </View>
    </TouchableOpacity>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  avatar: {
    width: 64,
    height: 64,
    borderRadius: 7.11,
  },
  fullFlex: {
    flex: 1,
  },
  flexStart: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
  },
}));
