import { IUserProfileById, UserRateReview } from '@/apis/user';
import { useGetInfiniteUserRatesReviewQuery } from '@/apis/user/queries';
import { IconLoading } from '@/components/IconLoading';
import { getItemSizeFlatList } from '@/utils/func';
import { memo, useCallback } from 'react';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { TReviewTab } from '../..';
import { UserReviewItem } from '../UserReviewItem';
import { Empty } from '@/components/Empty';
import { useIsYou } from '@/hooks/useIsYou';
import { TabFlashList } from '@/components/collapsing-tabs/TabFlashList';
import { ListRenderItem } from '@shopify/flash-list';
import { Spacer } from '@/components/Spacer';
import { useQueryClient } from '@tanstack/react-query';
import queryKeys from '@/utils/queryKeys';

const itemSize = getItemSizeFlatList(24, 3, 11);

type Props = {
  userId: string;
  tab: TReviewTab;
  profile?: IUserProfileById;
};

export const UserReviewAll = memo(({ userId, tab, profile }: Props) => {
  const { styles } = useStyles(stylesheet);

  const isYou = useIsYou({
    userId: userId ?? '',
  });
  const queryClient = useQueryClient();

  const currentType = tab !== 'All' ? (tab === 'Show' ? 'podcast' : 'episode') : undefined;

  const {
    data: userRatesReviewData,
    isFetching,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
    isSuccess,
  } = useGetInfiniteUserRatesReviewQuery({
    userId: userId ?? '',
    limit: 24,
    type: currentType,
  });

  const userRatesReview = userRatesReviewData?.pages?.flatMap((page) => page.data) ?? [];

  const renderItem = useCallback<ListRenderItem<UserRateReview>>(
    ({ item }) => <UserReviewItem userRateReview={item} />,
    []
  );

  const handleLoadMore = useCallback(async () => {
    if (hasNextPage && !isFetchingNextPage) {
      await fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const keyExtractor = useCallback((item: UserRateReview) => `${item.id}_${item.type}`, []);

  const renderSkeleton = useCallback(() => <IconLoading />, []);

  const renderSpacer = useCallback(() => <Spacer height={24} />, []);

  const handleRefetch = useCallback(async () => {
    await queryClient.resetQueries({ queryKey: queryKeys.userProfile.getUserRatesReviewInfinite() });
  }, [queryClient]);

  const isShowEmpty = isSuccess && userRatesReview.length === 0;

  return (
    <TabFlashList
      onRefresh={handleRefetch}
      refreshing={isFetching}
      bounces={false}
      data={userRatesReview}
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      showsVerticalScrollIndicator={false}
      scrollEventThrottle={16}
      keyExtractor={keyExtractor}
      renderItem={renderItem}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.4}
      ListHeaderComponent={<Spacer height={24} />}
      ItemSeparatorComponent={renderSpacer}
      ListFooterComponent={isFetchingNextPage ? renderSkeleton : null}
      ListEmptyComponent={
        isShowEmpty ? (
          <Empty
            type='star'
            emptyText={`${isYou ? 'You' : profile?.username} ${isYou ? "haven't" : "hasn't"} ${tab === 'All' ? 'rated any shows or episodes' : tab === 'Show' ? 'rated any shows' : 'rated any episodes'}`}
          />
        ) : null
      }
    />
  );
});

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  columnWrapper: {
    gap: 11,
  },
}));
