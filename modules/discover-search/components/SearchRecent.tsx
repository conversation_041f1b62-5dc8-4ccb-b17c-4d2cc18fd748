import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { useGetRecentSearches, IRecentSearchItem } from '@/apis/recent-search';
import { useDeleteRecentSearch } from '@/apis/recent-search/mutations';
import { FlatList, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { SearchAction } from '..';
import { PodcastSearchItem } from './PodcastSearchItem';
import { IPodcastSearchInfinite } from '@/apis/podcast';
import { IconLoading } from '@/components/IconLoading';
import Animated from 'react-native-reanimated';

type Props = {
  searchAction: SearchAction;
};

interface IRecentSearchItemWithMetadata extends IPodcastSearchInfinite {
  recentSearchId: string;
}

const convertToClientFormat = (serverItem: IRecentSearchItem): IRecentSearchItemWithMetadata => {
  const itemType = serverItem.type || serverItem.subTitle || '';

  return {
    id: serverItem.targetId, // Use targetId as the podcast/episode id for navigation
    title: serverItem.title,
    description: '',
    imageUrl: serverItem.imageUrl,
    authorEmail: null,
    authorName: null,
    ratingCount: 0,
    ratingAverage: 0,
    numberOfEpisodes: 0,
    reviewCount: 0,
    avgRate: null,
    commentCount: '0',
    type: (itemType === 'Show' ? 'podcast' : 'episode') as 'podcast' | 'episode',
    podcastTitle: itemType?.startsWith('Episode - ') ? itemType.replace('Episode - ', '') : undefined,
    recentSearchId: serverItem.id,
  };
};

export const SearchRecent = ({ searchAction }: Props) => {
  const { styles } = useStyles(stylesheet);

  // Fetch recent searches from server
  const recentSearchParams = {
    limit: 10,
    sort: 'DESC' as const,
    target: (searchAction === 'add-episodes' || searchAction === 'add-shows' ? 'favorite' : 'discover') as
      | 'discover'
      | 'favorite',
    type:
      searchAction === 'discover' || searchAction === 'create-post'
        ? undefined
        : searchAction === 'add-shows'
          ? ('podcast' as const)
          : ('episode' as const),
  };

  const { data: recentSearchResponse, isLoading } = useGetRecentSearches(recentSearchParams);

  const { mutate: deleteRecentSearch } = useDeleteRecentSearch();

  const handleRemove = (recentSearchId: string) => {
    deleteRecentSearch({ id: Number(recentSearchId) });
  };

  // Convert server data and filter based on search action
  const recentSearches = recentSearchResponse?.data || [];
  const convertedSearches = recentSearches.map(convertToClientFormat);

  if (isLoading) {
    return (
      <View style={styles.container}>
        <View style={styles.paddingH}>
          <ThemedText type='subtitleMedium'>Recent Searches</ThemedText>
          <Spacer height={16} />
          <Animated.View>
            <IconLoading />
          </Animated.View>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.paddingH}>
        <ThemedText type='subtitleMedium'>Recent Searches</ThemedText>

        <Spacer height={16} />
      </View>

      <FlatList
        showsVerticalScrollIndicator={false}
        contentContainerStyle={[styles.listContainer, styles.paddingH]}
        data={convertedSearches}
        renderItem={({ item }) => (
          <PodcastSearchItem
            searchItem={item}
            searchAction={searchAction}
            removeAble
            onRemove={() => handleRemove(item.recentSearchId)}
          />
        )}
        keyExtractor={(item) => item.recentSearchId}
        onEndReachedThreshold={0.5}
      />
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
  },
  paddingH: {
    paddingHorizontal: 24,
  },
  listContainer: {
    gap: 24,
    paddingBottom: rt.insets.bottom,
  },
}));
