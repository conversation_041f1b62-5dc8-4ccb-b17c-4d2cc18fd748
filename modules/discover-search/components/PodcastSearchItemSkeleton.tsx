import { Spacer } from '@/components/Spacer';
import { Skeleton } from 'moti/skeleton';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

export const PodcastSearchItemSkeleton = () => {
  const { styles } = useStyles(stylesheet);

  return (
    <View style={styles.container}>
      <Skeleton width={64} height={64} radius={8} />

      <Spacer width={16} />

      <View style={styles.info}>
        <Skeleton width={'50%'} height={20} />

        <Spacer height={4} />

        <Skeleton width={'70%'} height={20} />
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  info: {
    flex: 1,
  },
}));
