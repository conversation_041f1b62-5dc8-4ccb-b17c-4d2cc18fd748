import { IPodcastSearchInfinite } from '@/apis/podcast';
import { Icons } from '@/assets/icons';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { ExpoImage } from '@/components/ui/Image';
import { useAddRecentSearch } from '@/apis/recent-search';
import { router } from 'expo-router';
import { TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import * as Haptics from 'expo-haptics';
import { SearchAction } from '..';
import { useAddFavoriteEpisodeMutation, useAddFavoritePodcastMutation } from '@/apis/favorite';
import { toastError } from '@/utils/toast';
import { episodeDetailDirect, showDetailDirect } from '@/utils/router-prefetch';
import { useQueryClient } from '@tanstack/react-query';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';

type Props = {
  removeAble?: boolean;
  onRemove?: () => void;
  searchItem: IPodcastSearchInfinite;
  searchAction: SearchAction;
};

export const PodcastSearchItem = ({ removeAble = false, searchItem, searchAction, onRemove }: Props) => {
  const { styles, theme } = useStyles(stylesheet);
  const queryClient = useQueryClient();
  const { onCheckAccountRestricted } = useCheckRestrictAccount();

  const { addRecentSearch } = useAddRecentSearch();
  const { mutateAsync: addEpisodeToFavorites, isPending: isAddingEpisode } = useAddFavoriteEpisodeMutation();
  const { mutateAsync: addPodcastToFavorites, isPending: isAddingPodcast } = useAddFavoritePodcastMutation();
  const { title, imageUrl, id, type, podcastTitle } = searchItem;

  const handleDirect = async () => {
    if (searchAction === 'discover') {
      addRecentSearch(type, Number(id), 'discover', {
        title,
        imageUrl,
      });

      if (searchItem.type === 'podcast') {
        return showDetailDirect(queryClient, id.toString());
      }

      if (searchItem.type === 'episode') {
        return episodeDetailDirect(queryClient, id.toString());
      }
    }

    if (searchAction === 'add-shows' && type === 'podcast') {
      try {
        addRecentSearch(type, Number(id), 'favorite', {
          title,
          imageUrl,
        });

        const isRestricted = onCheckAccountRestricted();
        if (isRestricted) return;

        await addPodcastToFavorites({ podcastId: Number(id) });

        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

        if (router.canDismiss()) {
          router.dismissTo({
            pathname: '/(app)/favorite-shows',
          });
        } else {
          router.replace('/(app)/favorite-shows');
        }
      } catch (error) {
        toastError(error);
      }

      return;
    }

    if (searchAction === 'add-episodes' && type === 'episode') {
      try {
        addRecentSearch(type, Number(id), 'favorite', {
          title,
          imageUrl,
        });

        const isRestricted = onCheckAccountRestricted();
        if (isRestricted) return;

        await addEpisodeToFavorites({ episodeId: Number(id) });

        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

        if (router.canDismiss()) {
          router.dismissTo({
            pathname: '/(app)/favorite-episodes',
          });
        } else {
          router.replace('/(app)/favorite-episodes');
        }
      } catch (error) {
        toastError(error);
      }

      return;
    }

    if (searchItem.type === 'podcast') {
      return router.push({
        pathname: '/(app)/podcast/[podcastId]/add-post',
        params: {
          podcastId: id,
        },
      });
    }

    if (searchItem.type === 'episode') {
      return router.push({
        pathname: '/(app)/episode/[episodeId]/add-post',
        params: {
          episodeId: id,
        },
      });
    }
  };

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={handleDirect}
      disabled={isAddingPodcast || isAddingEpisode}
      activeOpacity={isAddingPodcast || isAddingEpisode ? 0.5 : 0.7}
    >
      <ExpoImage source={{ uri: imageUrl }} style={styles.image} />

      <View style={styles.info}>
        <ThemedText type='defaultMedium' numberOfLines={1}>
          {title}
        </ThemedText>

        <Spacer height={4} />

        <ThemedText numberOfLines={1} style={styles.description}>
          {type === 'podcast' ? 'Show' : `Episode - ${podcastTitle}`}
        </ThemedText>
      </View>

      {removeAble && (
        <TouchableOpacity onPress={onRemove}>
          <Icons.Close size={24} color={theme.colors.neutralWhite} />
        </TouchableOpacity>
      )}
    </TouchableOpacity>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  image: {
    width: 64,
    height: 64,
    objectFit: 'cover',
    borderRadius: 8,
  },
  info: {
    flex: 1,
  },
  description: {
    color: theme.colors.neutralLightGrey,
  },
}));
