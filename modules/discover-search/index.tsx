import { IPodcastSearchInfinite, useGetPodcastsSearchInfiniteQuery } from '@/apis/podcast';
import { SearchInput } from '@/components/SearchInput';
import { Show } from '@/components/Show';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { useDebouncedValue } from '@mantine/hooks';
import { router, useLocalSearchParams } from 'expo-router';
import { useCallback, useEffect, useState } from 'react';
import { TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { PodcastSearchItem } from './components/PodcastSearchItem';
import { PodcastSearchItemSkeleton } from './components/PodcastSearchItemSkeleton';
import { SearchRecent } from './components/SearchRecent';
import { useQueryClient } from '@tanstack/react-query';
import queryKeys from '@/utils/queryKeys';
import { FlashList, ListRenderItem } from '@shopify/flash-list';

export type SearchAction = 'discover' | 'create-post' | 'add-shows' | 'add-episodes';

export const DiscoverSearch = () => {
  const { searchAction = 'discover' } = useLocalSearchParams<{ searchAction: SearchAction }>();

  const { styles } = useStyles(stylesheet);

  const [searchValue, setSearchValue] = useState('');
  const [isEndReachedCalled, setIsEndReachedCalled] = useState(true);

  const [searchDebounced] = useDebouncedValue(searchValue, 200);
  const queryClient = useQueryClient();

  const {
    data,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
    hasPreviousPage,
    fetchPreviousPage,
    isFetchingPreviousPage,
    isPending,
  } = useGetPodcastsSearchInfiniteQuery({
    limit: 40,
    page: 1,
    search: searchDebounced,
    type: searchAction === 'add-shows' ? 'podcast' : searchAction === 'add-episodes' ? 'episode' : undefined,
  });

  const pages = data?.pages ?? [];
  const podcasts = pages?.map((page) => page.data).flat() ?? [];

  const handleCancel = () => {
    router.dismiss();
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    return () => {
      queryClient.removeQueries({ queryKey: queryKeys.categories.categoriesInfinite() });
    };
  }, [queryClient, searchDebounced]);

  const renderItem = useCallback<ListRenderItem<IPodcastSearchInfinite>>(
    ({ item }) => <PodcastSearchItem searchItem={item} searchAction={searchAction} />,
    [searchAction]
  );

  const keyExtractor = useCallback((item: IPodcastSearchInfinite) => `${item.id}-${item.type}`, []);

  const handleStartReached = useCallback(async () => {
    if (!isFetchingPreviousPage && hasPreviousPage) {
      await fetchPreviousPage();
      setIsEndReachedCalled(true);
    }
  }, [isFetchingPreviousPage, hasPreviousPage, fetchPreviousPage]);

  const onEndReached = useCallback(async () => {
    if (hasNextPage && !isFetchingNextPage && !isEndReachedCalled) {
      await fetchNextPage();
      setIsEndReachedCalled(true);
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage, isEndReachedCalled]);

  const renderSkeleton = useCallback(() => {
    return (
      <>
        {Array(3)
          .fill(0)
          .map((_, index) => (
            <PodcastSearchItemSkeleton key={index} />
          ))}
      </>
    );
  }, []);

  const handleMomentumScrollBegin = useCallback(() => {
    setIsEndReachedCalled(false);
  }, []);

  const renderSpacer = useCallback(() => <Spacer height={24} />, []);

  const placeholder =
    searchAction === 'add-shows'
      ? 'Search Show'
      : searchAction === 'add-episodes'
        ? 'Search Episode'
        : 'Search Show or Episode';

  return (
    <View style={styles.container}>
      <View style={[styles.box, styles.searchBox]}>
        <SearchInput placeholder={placeholder} value={searchValue} onChangeText={setSearchValue} />

        <Spacer width={16} />

        <TouchableOpacity onPress={handleCancel}>
          <ThemedText style={styles.cancelText}>Cancel</ThemedText>
        </TouchableOpacity>
      </View>

      <View style={[styles.searchContent]}>
        <Spacer height={24} />

        <Show when={!searchValue}>
          <SearchRecent searchAction={searchAction} />
        </Show>

        <Show when={!!searchValue}>
          <FlashList
            bounces={false}
            data={podcasts}
            showsVerticalScrollIndicator={false}
            style={{ flex: 1 }}
            keyboardShouldPersistTaps='handled'
            scrollEventThrottle={16}
            onStartReachedThreshold={0.4}
            onStartReached={handleStartReached}
            onEndReachedThreshold={0.4}
            onEndReached={onEndReached}
            contentContainerStyle={styles.listContainer}
            renderItem={renderItem}
            keyExtractor={keyExtractor}
            ListFooterComponent={isPending ? renderSkeleton : null}
            onMomentumScrollBegin={handleMomentumScrollBegin}
            ListFooterComponentStyle={styles.skeletonContainer}
            ItemSeparatorComponent={renderSpacer}
            estimatedItemSize={64}
          />
        </Show>
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.neutralBackground,
    paddingBottom: rt.insets.bottom,
    paddingTop: rt.insets.top,
  },
  box: {
    paddingHorizontal: 24,
  },
  searchBox: {
    backgroundColor: theme.colors.neutralCard,
    paddingVertical: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  cancelText: {
    color: theme.colors.neutralLightGrey,
  },
  searchContent: {
    backgroundColor: theme.colors.neutralBackground,
    flex: 1,
  },
  listContainer: {
    // gap: 24,
    paddingHorizontal: 24,
  },
  skeletonContainer: {
    gap: 24,
  },
  full: {
    flex: 1,
  },
}));
