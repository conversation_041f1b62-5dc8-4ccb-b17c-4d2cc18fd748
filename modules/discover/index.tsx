import { IEpisode, useGetNewestEpisodesQuery } from '@/apis/podcast';
import { FlatListAnimate, FlatListAnimateProps } from '@/components/FlatListAnimate';
import { useCallback } from 'react';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { EpisodeItem } from './components/EpisodeItem';
import { EpisodeItemSkeleton } from './components/EpisodeItemSkeleton';
import { HeaderDiscoverList } from './components/HeaderDiscoverList';

export const Discover = () => {
  const { styles } = useStyles(stylesheet);

  const { data: newestEpisodesData, isPending: isLoadingNewestEpisodes } = useGetNewestEpisodesQuery({
    limit: 5,
    page: 1,
    isNewest: true,
  });

  const newestEpisodes = newestEpisodesData?.data ?? [];

  const keyExtractor = useCallback((item: any) => item.id.toString(), []);
  const renderItem = useCallback<FlatListAnimateProps<IEpisode>['renderItem']>(
    ({ item }) => <EpisodeItem episodeItem={item} />,
    []
  );
  const renderSkeleton = useCallback(() => {
    if (!isLoadingNewestEpisodes) return null;

    return (
      <>
        {Array(3)
          .fill(0)
          .map((_, index) => (
            <EpisodeItemSkeleton key={index} />
          ))}
      </>
    );
  }, [isLoadingNewestEpisodes]);

  return (
    <View style={[styles.container]}>
      <FlatListAnimate
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={false}
        data={newestEpisodes}
        ListHeaderComponent={<HeaderDiscoverList />}
        keyExtractor={keyExtractor}
        contentContainerStyle={styles.scrollView}
        renderItem={renderItem}
        ListFooterComponent={renderSkeleton}
        ListFooterComponentStyle={[styles.skeletonContainer, styles.box]}
      />
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  box: {
    paddingHorizontal: 24,
  },
  container: {
    backgroundColor: theme.colors.neutralBackground,
    flex: 1,
    paddingTop: rt.insets.top,
  },
  viewAllText: {
    color: theme.colors.neutralLightGrey,
  },
  scrollView: {
    paddingTop: 16,
    paddingBottom: 20,
    gap: 16,
    flexGrow: 1,
  },
  skeletonContainer: {
    gap: 16,
  },
}));
