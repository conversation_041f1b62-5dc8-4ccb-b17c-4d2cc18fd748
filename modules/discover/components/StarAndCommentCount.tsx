import { Icons } from '@/assets/icons';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { formatCompactNumber } from '@/utils/func';
import bigDecimal from 'js-big-decimal';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  star: number;
  rateCount: number;
};

export const StarAndCommentCount = ({ star = 0, rateCount = 0 }: Props) => {
  const { styles } = useStyles(stylesheet);

  const avgRate = bigDecimal.round(star, 1, bigDecimal.RoundingModes.HALF_UP);

  return (
    <View style={styles.starAndCommentsBox}>
      <Icons.Star size={16} color={'#FFB200'} />

      <Spacer width={8} />

      <ThemedText type='tinyMedium'>{avgRate}</ThemedText>

      <Spacer width={4} />

      <View style={styles.dot} />

      <Spacer width={4} />

      <ThemedText type='tinyMedium' style={styles.commentsCount}>
        {formatCompactNumber(rateCount)}
      </ThemedText>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  starAndCommentsBox: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dot: {
    width: 4,
    height: 4,
    borderRadius: 999,
    backgroundColor: theme.colors.neutralWhite,
  },
  commentsCount: {
    color: theme.colors.neutralLightGrey,
  },
}));
