import { IPodcast, useGetPodcastsListQuery } from '@/apis/podcast';
import { Spacer } from '@/components/Spacer';
import { FlatList, ListRenderItem, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { TitleBox } from './TitleBox';
import { TopShowItem } from './TopShowItem';
import { TopShowItemSkeleton } from './TopShowItemSkeleton';
import { memo, useCallback, useEffect, useState } from 'react';

export const TopShows = memo(() => {
  const { styles } = useStyles(stylesheet);
  const [key, setKey] = useState(0);
  const {
    data: topShowsData,
    isPending: isLoadingTopShows,
    isFetching: isFetchingTopShows,
  } = useGetPodcastsListQuery({
    limit: 5,
    page: 1,
    isTrending: true,
  });
  const topShows = topShowsData?.data ?? [];

  const renderItem = useCallback<ListRenderItem<IPodcast>>(
    ({ item }) => (
      <TopShowItem
        imageUrl={item.imageUrl}
        name={item.title}
        authorName={item.authorName}
        star={Number(item.avgRate ?? 0)}
        rateCount={Number(item.rateCount ?? 0)}
        itemSize={200}
        podcastId={Number(item.id)}
      />
    ),
    []
  );

  const keyExtractor = useCallback((item: IPodcast) => item.id.toString(), []);

  const renderSkeleton = useCallback(() => {
    return (
      <>
        {Array(5)
          .fill(0)
          .map((_, index) => (
            <TopShowItemSkeleton key={index} itemSize={200} />
          ))}
      </>
    );
  }, []);

  useEffect(() => {
    if (!isFetchingTopShows) {
      setKey((prev) => prev + 1);
    }
  }, [isFetchingTopShows]);

  return (
    <View>
      <View style={styles.container}>
        <TitleBox title='Top Shows' />
      </View>

      <Spacer height={16} />

      <FlatList
        key={key}
        data={topShows}
        horizontal
        showsHorizontalScrollIndicator={false}
        ListHeaderComponentStyle={styles.skeletonContainer}
        ListHeaderComponent={isLoadingTopShows ? renderSkeleton : null}
        contentContainerStyle={styles.container}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
      />
    </View>
  );
});

const stylesheet = createStyleSheet((theme) => ({
  container: {
    paddingHorizontal: 24,
    gap: 8,
  },
  skeletonContainer: {
    flexDirection: 'row',
    gap: 8,
  },
}));
