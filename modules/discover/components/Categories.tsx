import { useGetCategoriesQuery } from '@/apis/category/queries';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { router } from 'expo-router';
import { FlatList, ListRenderItem, TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { CategoryItem } from './CategoryItem';
import { CategoryItemSkeleton } from './CategoryItemSkeleton';
import { TitleBox } from './TitleBox';
import { memo, useCallback, useEffect, useState } from 'react';
import { ICategoryResponse } from '@/apis/category';

export const Categories = memo(() => {
  const { styles } = useStyles(stylesheet);
  const [key, setKey] = useState(0);
  const {
    data: categoriesData,
    isPending: isLoadingCategories,
    isFetching: isFetchingCategories,
  } = useGetCategoriesQuery({
    limit: 5,
    page: 1,
    isTrending: true,
  });
  const categories = categoriesData?.data ?? [];

  const handleDirect = () => {
    router.push('/(app)/categories-all');
  };

  const renderItem = useCallback<ListRenderItem<ICategoryResponse>>(
    ({ item }) => <CategoryItem name={item.name} id={Number(item.id)} />,
    []
  );

  const keyExtractor = useCallback((item: ICategoryResponse) => item.id.toString(), []);

  const renderSkeleton = useCallback(() => {
    return (
      <>
        {Array(5)
          .fill(0)
          .map((_, index) => (
            <CategoryItemSkeleton key={index} />
          ))}
      </>
    );
  }, []);

  useEffect(() => {
    if (!isFetchingCategories) {
      setKey((prev) => prev + 1);
    }
  }, [isFetchingCategories]);

  return (
    <View>
      <View style={styles.container}>
        <TitleBox
          title='Top Categories'
          rightComponent={
            <TouchableOpacity onPress={handleDirect}>
              <ThemedText type='small' style={styles.viewAllText}>
                View All
              </ThemedText>
            </TouchableOpacity>
          }
        />
      </View>

      <Spacer height={16} />

      <FlatList
        key={key}
        data={categories}
        horizontal
        scrollEventThrottle={16}
        contentContainerStyle={styles.container}
        showsHorizontalScrollIndicator={false}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        initialNumToRender={5}
        ListHeaderComponent={isLoadingCategories ? renderSkeleton : undefined}
        ListHeaderComponentStyle={styles.skeletonContainer}
      />
    </View>
  );
});

const stylesheet = createStyleSheet((theme) => ({
  container: {
    paddingHorizontal: 24,
    gap: 8,
  },
  viewAllText: {
    color: theme.colors.neutralLightGrey,
  },
  skeletonContainer: {
    flexDirection: 'row',
    gap: 8,
  },
}));
