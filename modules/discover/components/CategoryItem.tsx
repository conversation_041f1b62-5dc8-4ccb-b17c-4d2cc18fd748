import { ThemedText } from '@/components/ThemedText';
import { router } from 'expo-router';
import { TouchableOpacity } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  name: string;
  id: number;
};

export const CategoryItem = ({ name, id }: Props) => {
  const { styles } = useStyles(stylesheet);

  const handleDirect = () => {
    router.push({
      pathname: '/(app)/category/[id]',
      params: {
        id,
        name,
      },
    });
  };

  return (
    <TouchableOpacity onPress={handleDirect} style={styles.itemBox}>
      <ThemedText type='small' style={styles.itemText}>
        {name}
      </ThemedText>
    </TouchableOpacity>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  itemBox: {
    borderWidth: 1,
    borderRadius: 999,
    borderColor: theme.colors.whiteOpacity24,
    paddingHorizontal: 16,
    paddingVertical: 10,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  itemText: {
    color: theme.colors.neutralWhite,
  },
  itemImage: {
    width: 16,
    height: 16,
    objectFit: 'contain',
  },
}));
