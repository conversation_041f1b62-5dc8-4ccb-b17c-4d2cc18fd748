import { Spacer } from '@/components/Spacer';
import { Skeleton } from 'moti/skeleton';
import { View } from 'react-native';

type Props = {
  itemSize: number;
};

export const TopShowItemSkeleton = ({ itemSize }: Props) => {
  return (
    <View>
      <Skeleton width={itemSize} height={itemSize} radius={4} />

      <Spacer height={14} />

      <Skeleton width={150} height={24} />

      <Spacer height={4} />

      <Skeleton width={120} height={20} />

      <Spacer height={4} />

      <Skeleton width={100} height={20} />
    </View>
  );
};
