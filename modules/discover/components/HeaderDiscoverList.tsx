import { SearchInput } from '@/components/SearchInput';
import { Spacer } from '@/components/Spacer';
import { router } from 'expo-router';
import { TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { Categories } from './Categories';
import { TitleBox } from './TitleBox';
import { TopShows } from './TopShows';
import { memo } from 'react';

export const HeaderDiscoverList = memo(() => {
  const { styles } = useStyles(stylesheet);

  return (
    <View>
      <TouchableOpacity onPress={() => router.push('/(app)/discover-search')}>
        <View style={styles.box} pointerEvents='none'>
          <SearchInput placeholder='Search Show or Episode' />
        </View>
      </TouchableOpacity>

      <Spacer height={32} />

      <Categories />

      <Spacer height={16} />

      <TopShows />

      <Spacer height={16} />

      <View style={styles.box}>
        <TitleBox title='Newest Episodes' />
      </View>
    </View>
  );
});

const stylesheet = createStyleSheet(() => ({
  box: {
    paddingHorizontal: 24,
  },
}));
