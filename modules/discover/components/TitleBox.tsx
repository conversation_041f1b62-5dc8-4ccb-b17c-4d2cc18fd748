import { ThemedText } from '@/components/ThemedText';
import { ReactNode } from 'react';
import { Pressable, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  title: string;
  rightComponent?: ReactNode;
};

export const TitleBox = ({ title, rightComponent }: Props) => {
  const { styles } = useStyles(stylesheet);

  return (
    <Pressable style={styles.titleBox}>
      <ThemedText type='subtitleMedium'>{title}</ThemedText>

      {rightComponent ? <View>{rightComponent}</View> : null}
    </Pressable>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  titleBox: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
}));
