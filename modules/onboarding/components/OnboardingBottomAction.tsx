import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { ONBOARDING_TOTAL_STEPS } from '../utils';
import { DotsProgress } from './DotsProgress';
import { OnboardingAction } from './OnboardingAction';

export type OnboardingBottomActionProps = {
  stepActive: number;
  onNext: () => void;
};

const onboardingTitle = [
  "Join YOUR Podcast's Community",
  "Track Your Podcast's Universe",
  'Share Your Opinion about Podcast',
  'Discover New Shows, Episodes, and Communities',
];

export const OnboardingBottomAction = ({ stepActive, onNext }: OnboardingBottomActionProps) => {
  const { bottom } = useSafeAreaInsets();
  const { theme, styles } = useStyles(stylesheet);

  return (
    <View style={[styles.card, { paddingBottom: bottom }]} pointerEvents='box-none'>
      <View style={styles.center}>
        <ThemedText pointerEvents='box-none' style={[styles.textCenter, styles.welcomeText]} type='small'>
          Welcome to Rabid
        </ThemedText>

        <Spacer height={24} />

        <ThemedText pointerEvents='box-none' style={[styles.textCenter, theme.fw600, theme.wFull]} type='title'>
          {onboardingTitle[stepActive]}
        </ThemedText>
      </View>

      <DotsProgress stepActive={stepActive} totalSteps={ONBOARDING_TOTAL_STEPS} />

      <OnboardingAction onNext={onNext} stepActive={stepActive} totalSteps={ONBOARDING_TOTAL_STEPS} />
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  card: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    width: '100%',
    borderBottomWidth: 0,
    borderTopLeftRadius: 40,
    borderTopRightRadius: 40,
    backgroundColor: theme.colors.neutralBackground,
    paddingVertical: 40,
    paddingHorizontal: 24,
    gap: 40,
  },
  center: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  },
  textCenter: {
    textAlign: 'center',
  },
  welcomeText: {
    opacity: 0.56,
    zIndex: 0,
  },
}));
