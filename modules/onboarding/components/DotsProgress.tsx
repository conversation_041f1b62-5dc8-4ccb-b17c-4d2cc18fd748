import { View } from 'react-native';
import Animated, { useAnimatedStyle, withTiming } from 'react-native-reanimated';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  stepActive: number;
  totalSteps: number;
};

export const DotsProgress = ({ stepActive, totalSteps }: Props) => {
  const { theme, styles } = useStyles(stylesheet);

  return (
    <View style={[styles.centerH, styles.dotBox]}>
      {Array.from({ length: totalSteps }).map((_, index) => {
        const animatedStyle = useAnimatedStyle(() => {
          return {
            width: index === stepActive ? withTiming(32, { duration: 300 }) : withTiming(8, { duration: 300 }),
            height: 8,
            borderRadius: 999,
            backgroundColor:
              index === stepActive
                ? withTiming(theme.colors.primary, { duration: 300 })
                : withTiming(theme.colors.whiteOpacity16, { duration: 300 }),
          };
        });

        return <Animated.View pointerEvents={'none'} key={index} style={animatedStyle} />;
      })}
    </View>
  );
};

const stylesheet = createStyleSheet({
  centerH: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dotBox: {
    gap: 8,
  },
});
