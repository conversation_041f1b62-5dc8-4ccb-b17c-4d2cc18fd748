import { Spacer } from '@/components/Spacer';
import { LinearGradient } from 'expo-linear-gradient';
import { Image, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles';

const WIDTH_SCREEN = UnistylesRuntime.screen.width;
const HEIGHT_SCREEN = UnistylesRuntime.screen.height;

export const OnboardingStep2 = () => {
  const { theme, styles } = useStyles(stylesheet);

  return (
    <LinearGradient
      colors={theme.colors.onboardingGradient2}
      start={{ x: 0, y: 0 }}
      end={{ x: 0, y: 0.5 }}
      style={[styles.container, { width: WIDTH_SCREEN, height: HEIGHT_SCREEN }]}
    >
      <SafeAreaView style={styles.safeView}>
        <View style={styles.logoView}>
          <Image source={require('@/assets/images/logo.png')} style={styles.logo} />
        </View>

        <Spacer height={9} />

        <View style={styles.mainImageBox}>
          <Image source={require('@/assets/images/onboarding_step_2.png')} style={styles.mainImage} />
        </View>
      </SafeAreaView>
    </LinearGradient>
  );
};

const stylesheet = createStyleSheet({
  logo: {
    height: 48,
    objectFit: 'contain',
  },
  container: {
    alignItems: 'center',
    paddingBottom: 100,
    width: '100%',
    height: '100%',
  },
  text: {
    color: 'white',
  },
  safeView: {
    flexDirection: 'column',
    flex: 1,
    width: '100%',
  },
  logoView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 4,
  },
  mainImageBox: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 59,
    paddingRight: 25.67,
  },
  mainImage: {
    width: '100%',
    height: 580,
    objectFit: 'cover',
  },
});
