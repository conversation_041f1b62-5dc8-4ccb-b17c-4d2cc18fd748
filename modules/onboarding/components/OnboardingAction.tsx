import { ThemedText } from '@/components/ThemedText';
import { Button } from '@/components/ui/Button';
import { router } from 'expo-router';
import { TouchableOpacity, View } from 'react-native';
import Animated, { useAnimatedStyle, withTiming } from 'react-native-reanimated';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { OnboardingBottomActionProps } from './OnboardingBottomAction';

type Props = {
  totalSteps: number;
} & Pick<OnboardingBottomActionProps, 'onNext' | 'stepActive'>;

export const OnboardingAction = ({ onNext, totalSteps, stepActive }: Props) => {
  const { styles } = useStyles(stylesheet);

  const skipAnimatedButtonStyle = useAnimatedStyle(() => {
    return {
      width: stepActive + 1 === totalSteps ? withTiming(0, { duration: 300 }) : withTiming(100, { duration: 300 }),
      display: stepActive + 1 === totalSteps ? 'none' : 'flex',
    };
  });

  const handleSkip = () => {
    router.replace('/sign-up');
  };

  const handleSignIn = () => {
    router.replace('/sign-in');
  };

  return (
    <View style={styles.actions}>
      <View style={styles.stepActions}>
        <Animated.View style={skipAnimatedButtonStyle}>
          <Button type='outline' onPress={handleSkip} style={styles.skipButton}>
            <ThemedText>Skip</ThemedText>
          </Button>
        </Animated.View>

        <Button type='default' onPress={onNext} style={styles.nextButton}>
          <ThemedText style={styles.nextText}>{totalSteps === stepActive + 1 ? 'Sign Up' : 'Next'}</ThemedText>
        </Button>
      </View>

      <View style={[styles.centerH, styles.signInBox]}>
        <ThemedText type='small'>Have an Account?</ThemedText>

        <TouchableOpacity activeOpacity={0.7} onPress={handleSignIn}>
          <ThemedText style={styles.signInText} type='small'>
            Sign In
          </ThemedText>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  actions: {
    gap: 24,
    paddingBottom: 32,
  },
  signInText: {
    color: theme.colors.primary,
    ...theme.fw500,
  },
  skipButton: {
    minWidth: 100,
  },
  nextButton: {
    flex: 1,
  },
  signInBox: {
    gap: 8,
  },
  centerH: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  nextText: {
    color: theme.colors.neutralBackground,
    ...theme.fw700,
  },
  stepActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 35,
    gap: 12,
  },
}));
