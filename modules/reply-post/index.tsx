import {
  useEditReplyCommentEpisodeMutation,
  useEditReplyCommentMutation,
  useReplyCommentEpisodeMutation,
  useReplyCommentMutation,
} from '@/apis/comment/mutations';
import { ICommentResponse } from '@/apis/comment/types';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { CustomButton } from '@/components/ui/CustomButton';
import { Header } from '@/components/ui/Header';
import { toastError } from '@/utils/toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { router, useLocalSearchParams } from 'expo-router';
import { FormProvider, useForm } from 'react-hook-form';
import { TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { PostInfo } from './components/PostInfo';
import { ReplyComment } from './components/ReplyComment';
import { ReplyPostFormData, replyPostSchema } from './schema';
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';

type Props = {};

export const ReplyPost = (props: Props) => {
  const { styles } = useStyles(stylesheet);

  const { postId, podcastId, episodeId, isEdit, initCommentContent, commentId, ...localParams } = useLocalSearchParams<{
    postId: string;
    podcastId: string;
    episodeId: string;
    postInfo: string;
    isEdit: string;
    commentId: string;
    initCommentContent: string;
  }>();

  // Safely parse postInfo with error handling
  let postInfo: ICommentResponse | null = null;
  try {
    if (localParams.postInfo && localParams.postInfo !== 'undefined') {
      postInfo = JSON.parse(localParams.postInfo) as ICommentResponse;
    }
  } catch (error) {
    console.error('Error parsing postInfo:', error);
  }

  // If postInfo is not available, we can't proceed
  if (!postInfo) {
    return (
      <View style={styles.container}>
        <Header title='Reply' />
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
          <ThemedText>Error: Post information not available</ThemedText>
          <ThemedText style={{ marginTop: 10, fontSize: 12, opacity: 0.7 }}>
            Debug: postInfo = {localParams.postInfo || 'undefined'}
          </ThemedText>
        </View>
      </View>
    );
  }

  const enableEdit = isEdit === 'true';

  const { onCheckAccountRestricted } = useCheckRestrictAccount();
  const { mutateAsync: editReplyComment } = useEditReplyCommentMutation();
  const { mutateAsync: editReplyCommentEpisode } = useEditReplyCommentEpisodeMutation();
  const { mutateAsync: replyComment } = useReplyCommentMutation();
  const { mutateAsync: replyCommentEpisode } = useReplyCommentEpisodeMutation();

  const methods = useForm<ReplyPostFormData>({
    resolver: zodResolver(replyPostSchema),
    defaultValues: {
      content: initCommentContent ?? '',
    },
  });
  const {
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const handleGoBack = () => {
    router.back();
  };

  const handleReplyComment = async (data: ReplyPostFormData) => {
    try {
      if (!postInfo) return;
      const isRestricted = onCheckAccountRestricted();
      if (isRestricted) return;

      if (isEdit) {
        if (episodeId) {
          await editReplyCommentEpisode({
            id: commentId,
            commentId: Number(postId),
            content: data.content?.trim(),
            source: postInfo.source,
          });
        } else {
          await editReplyComment({
            id: commentId,
            commentId: Number(postId),
            content: data.content?.trim(),
            source: postInfo.source,
          });
        }
      } else {
        if (episodeId) {
          await replyCommentEpisode({
            commentId: Number(postId),
            content: data.content?.trim(),
            source: postInfo.source,
          });
        } else {
          await replyComment({
            commentId: Number(postId),
            content: data.content?.trim(),
            source: postInfo.source,
          });
        }
      }

      handleGoBack();
    } catch (error) {
      toastError(error);
    }
  };

  return (
    <FormProvider {...methods}>
      <View style={styles.container}>
        <Header
          leftAction={
            <TouchableOpacity style={styles.cancelTouch} onPress={handleGoBack}>
              <ThemedText type='tiny' style={styles.cancelText}>
                Cancel
              </ThemedText>
            </TouchableOpacity>
          }
          rightAction={
            <CustomButton
              type='primary'
              textType='tinySemiBold'
              onPress={handleSubmit(handleReplyComment)}
              style={[styles.postTouch, isSubmitting && styles.editButtonDisabled]}
              disabled={isSubmitting}
              isLoading={isSubmitting}
            >
              {isSubmitting ? '' : enableEdit ? 'Edit Post' : 'Post'}
            </CustomButton>
          }
          title='Post Reply'
          titleStyle={styles.titleStyle}
        />

        <Spacer height={24} />

        <KeyboardAwareScrollView
          style={{ flex: 1 }}
          bounces={false}
          contentContainerStyle={styles.scrollBox}
          showsVerticalScrollIndicator={false}
          bottomOffset={50}
        >
          <PostInfo postInfo={postInfo} />

          <ReplyComment postInfo={postInfo} />
        </KeyboardAwareScrollView>
      </View>
    </FormProvider>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.neutralBackground,
    paddingHorizontal: 24,
    paddingTop: rt.insets.top,
    paddingBottom: rt.insets.bottom,
  },
  cancelText: {
    color: theme.colors.neutralLightGrey,
    flexWrap: 'nowrap',
  },
  cancelTouch: {},
  postTouch: {
    paddingHorizontal: 16,
    minHeight: 38,
  },
  titleStyle: {
    ...theme.fw500,
    fontSize: 16,
    lineHeight: 24,
  },
  scrollBox: {
    paddingBottom: 24,
    flexDirection: 'column',
    justifyContent: 'flex-start',
  },
  editButtonDisabled: {
    backgroundColor: theme.colors.neutralGrey,
  },
}));
