import { ICommentResponse } from '@/apis/comment/types';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { Avatar } from '@/components/ui/Avatar';
import { UserProfileText } from '@/components/UserProfileText';
import { UserProfileTouch } from '@/components/UserProfileTouch';
import { useIsYou } from '@/hooks/useIsYou';
import { useMemo } from 'react';
import { TextStyle, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  postInfo: Pick<ICommentResponse, 'title' | 'content' | 'user' | 'source'>;
  usernameStyle?: TextStyle;
};

export const PostInfo = ({ postInfo, usernameStyle }: Props) => {
  const { styles } = useStyles(stylesheet);

  const isPostOwner = useIsYou({
    userId: postInfo.user.id.toString(),
  });

  const usernameDisplay = useMemo(() => {
    if (isPostOwner) return 'You';

    return postInfo?.user?.username;
  }, [postInfo?.user?.username, isPostOwner]);

  return (
    <View style={styles.postContent}>
      <UserProfileTouch userId={postInfo.user.id} userType={postInfo.source}>
        <Avatar size={48} image={postInfo.user.avatar} />

        <View style={styles.line} />
      </UserProfileTouch>

      <Spacer width={24} />

      <View style={styles.postInfoBox}>
        <UserProfileText userId={postInfo.user.id} userType={postInfo.source}>
          <ThemedText type='small' style={[usernameStyle, isPostOwner && styles.textPrimary]}>
            {usernameDisplay}
          </ThemedText>
        </UserProfileText>

        <Spacer height={4} />

        <ThemedText type='defaultMedium'>{postInfo.title}</ThemedText>

        <Spacer height={4} />

        <ThemedText type='small'>{postInfo.content}</ThemedText>
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  postContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    minHeight: 100,
  },
  line: {
    width: 1,
    height: '100%',
    backgroundColor: theme.colors.neutralGrey,
    position: 'absolute',
    left: '50%',
    right: '50%',
    zIndex: -1,
  },
  postInfoBox: {
    flexDirection: 'column',
    justifyContent: 'flex-start',
    flex: 1,
    paddingBottom: 24,
  },
  textPrimary: {
    color: theme.colors.primary,
  },
}));
