import { useGetProfileQuery } from '@/apis/auth/queries';
import { ICommentResponse } from '@/apis/comment/types';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { Avatar } from '@/components/ui/Avatar';
import { TextareaInput } from '@/components/ui/TextareaInput';
import { useIsYou } from '@/hooks/useIsYou';
import { useMemo } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { ReplyPostFormData } from '../schema';

type Props = {
  postInfo: ICommentResponse;
};

export const ReplyComment = ({ postInfo }: Props) => {
  const { styles } = useStyles(stylesheet);
  const { data: userProfile } = useGetProfileQuery();
  const {
    formState: { errors, isSubmitting },
    control,
  } = useFormContext<ReplyPostFormData>();

  const isYou = useIsYou({
    userId: postInfo.user.id.toString(),
  });

  const usernameDisplay = useMemo(() => {
    if (isYou) return 'You';

    return postInfo?.user?.username;
  }, [postInfo?.user?.username, isYou]);

  return (
    <View style={styles.container}>
      <Avatar image={userProfile?.avatar} size={48} />

      <Spacer width={24} />

      <View style={styles.replyContainer}>
        <ThemedText type='small'>
          Replying to{' '}
          <ThemedText type='small' style={styles.replyUsername}>
            {usernameDisplay}
          </ThemedText>
        </ThemedText>

        <Spacer height={8} />

        <Controller
          control={control}
          name='content'
          render={({ field: { onChange, onBlur, value } }) => (
            <TextareaInput
              placeholder='Write your reply'
              value={value}
              onChangeText={onChange}
              onBlur={onBlur}
              editable={!isSubmitting}
              error={errors.content?.message}
              autoFocus
            />
          )}
        />
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  replyUsername: {
    color: theme.colors.primary,
  },
  replyContainer: {
    flex: 1,
  },
}));
