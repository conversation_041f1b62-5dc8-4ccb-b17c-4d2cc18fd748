import { router, useLocalSearchParams } from 'expo-router';
import { useRef, useState } from 'react';
import { Keyboard, KeyboardAvoidingView, TouchableOpacity, TouchableWithoutFeedback, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

import {
  useCreateCommentEpisodeMutation,
  useCreateCommentMutation,
  useUploadCommentImageMutation,
} from '@/apis/comment/mutations';
import { useGetEpisodeByIdQuery } from '@/apis/episode';
import { useGetPodcastByIdQuery } from '@/apis/podcast';
import { useUploadImageToS3 } from '@/apis/user';
import { ThemedText } from '@/components/ThemedText';
import { CustomButton } from '@/components/ui/CustomButton';
import { Header } from '@/components/ui/Header';
import { toastError, toastSuccess } from '@/utils/toast';
import PostForm, { PostFormData, PostFormRef } from '../../components/PostForm';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';

function AddPost() {
  const localParams = useLocalSearchParams<{ podcastId: string; episodeId: string }>();
  const podcastId = localParams?.podcastId;
  const episodeId = localParams?.episodeId;

  const isAddPostToPodcast = !!podcastId;
  const isAddPostToEpisode = !!episodeId;

  const formRef = useRef<PostFormRef>(null);
  const { styles } = useStyles(stylesheet);

  const { onCheckAccountRestricted } = useCheckRestrictAccount();

  const { data: podcast } = useGetPodcastByIdQuery({ podcastId }, { enabled: isAddPostToPodcast });
  const { data: episode } = useGetEpisodeByIdQuery(episodeId, { enabled: isAddPostToEpisode });

  const { mutateAsync: createComment, isPending: isPendingCreateComment } = useCreateCommentMutation();
  const { mutateAsync: createCommentEpisode, isPending: isPendingCreateCommentEpisode } =
    useCreateCommentEpisodeMutation();
  const { mutateAsync: requestUploadImage, isPending: isRequestingUploadImage } = useUploadCommentImageMutation();
  const { mutateAsync: uploadImageToS3, isPending: isUploadingToS3 } = useUploadImageToS3();

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (data: PostFormData) => {
    try {
      const isRestricted = onCheckAccountRestricted();
      if (isRestricted) return;

      let uploadedImageUrls: PromiseSettledResult<string>[] = [];

      if (data.images && data.images.length > 0) {
        const startTime = performance.now();
        const uploadUrls = await requestUploadImage({ count: data.images.length });
        const endTime = performance.now();

        try {
          // Upload all images in parallel and collect their public URLs
          uploadedImageUrls = await Promise.allSettled<string>(
            (data.images || []).map(async (image, index) => {
              const presignedUrl = uploadUrls[index]?.presigned;

              const publicUrl = uploadUrls[index]?.url;

              if (!presignedUrl || !publicUrl) {
                throw new Error('Invalid upload URL received');
              }

              // Upload the image to S3
              await uploadImageToS3({
                file: image,
                url: presignedUrl,
              });

              return publicUrl;
            })
          );
        } catch (uploadError) {
          console.error('Image upload failed:', uploadError);
        }
      }

      const images = uploadedImageUrls.filter((url) => url.status === 'fulfilled').map((url) => url.value);

      if (isAddPostToPodcast) {
        await createComment({
          podcastId,
          content: data.comment?.trim(),
          title: data.title?.trim(),
          images,
        });

        toastSuccess({ description: 'Posted successfully' });
        router.dismissTo({
          pathname: '/(app)/podcast/[podcastId]',
          params: { podcastId },
        });
      }

      if (isAddPostToEpisode) {
        await createCommentEpisode({
          episodeId,
          content: data.comment?.trim(),
          title: data.title?.trim(),
          images,
        });

        toastSuccess({ description: 'Posted successfully' });
        router.dismissTo({
          pathname: '/(app)/episode/[episodeId]',
          params: { episodeId },
        });
      }
    } catch (error) {
      toastError(error);
    }
  };

  const triggerSubmit = () => {
    formRef.current?.submit();
  };

  const isPosting =
    isRequestingUploadImage ||
    isPendingCreateComment ||
    isUploadingToS3 ||
    isPendingCreateCommentEpisode ||
    isSubmitting;

  return (
    <KeyboardAvoidingView behavior='padding' style={styles.content}>
      <TouchableWithoutFeedback onPress={() => Keyboard.dismiss()}>
        <View style={styles.container}>
          <Header
            title='Add Post'
            titleStyle={styles.titleStyle}
            leftAction={
              <TouchableOpacity onPress={() => router.back()}>
                <ThemedText type='small' style={styles.cancelText}>
                  Cancel
                </ThemedText>
              </TouchableOpacity>
            }
            rightAction={
              <CustomButton
                onPress={triggerSubmit}
                style={[styles.postButton, isPosting && styles.postButtonDisabled]}
                disabled={isPendingCreateComment}
                isLoading={isPosting}
                textType='tinySemiBold'
              >
                {isPosting ? '' : 'Post'}
              </CustomButton>
            }
          />

          <View style={styles.content}>
            <PostForm
              episode={episode}
              podcast={podcast}
              ref={formRef}
              onSubmit={handleSubmit}
              onFormSubmittingChange={setIsSubmitting}
            />
          </View>
        </View>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
}

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    paddingHorizontal: 16,
    paddingTop: rt.insets.top,
    paddingBottom: rt.insets.bottom,
  },
  content: {
    flex: 1,
  },
  cancelText: {
    color: theme.colors.neutralLightGrey,
  },
  postButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 16,
    paddingVertical: 7,
    minHeight: 38,
  },
  postButtonDisabled: {
    backgroundColor: theme.colors.neutralGrey,
  },
  postButtonText: {
    color: theme.colors.background,
  },
  titleStyle: {
    ...theme.fw500,
    fontSize: 16,
    lineHeight: 24,
  },
}));

export default AddPost;
