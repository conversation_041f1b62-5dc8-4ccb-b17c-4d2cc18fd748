import { ZOD_ERRORS } from '@/config/zodError';
import { REGEX } from '@/constants/regex';
import { z } from 'zod';

export const forgotPasswordSchema = z.object({
  emailOrPhone: z
    .string()
    .trim()
    .min(1, ZOD_ERRORS.required)
    .refine(
      (val) => {
        const looksLikePhone = val.startsWith('+') && REGEX.DIGIT_ONLY.test(val.slice(1));
        const looksLikePhoneWithoutCountryCode = REGEX.DIGIT_ONLY.test(val);

        if (looksLikePhone) {
          const isValidPhone = REGEX.PHONE.test(val);
          // ignore character '+'
          if (val.length < 10 || val.length > 16) {
            return false;
          }
          if (!isValidPhone) {
            return false;
          }
          return true;
        } else if (looksLikePhoneWithoutCountryCode) {
          const isValidPhone = REGEX.PHONE_NO_COUNTRY_CODE.test(val);

          if (!isValidPhone) return false;

          return true;
        } else {
          // Validate as email
          const isValidEmail = REGEX.EMAIL.test(val);
          if (!isValidEmail) {
            return false;
          }
          if (val.length > 254) {
            return false;
          }
          return true;
        }
      },
      (val) => {
        const looksLikePhone = val.startsWith('+') && REGEX.DIGIT_ONLY.test(val.slice(1));
        const looksLikePhoneWithoutCountryCode = REGEX.DIGIT_ONLY.test(val);

        if (looksLikePhone) {
          const isValidPhone = REGEX.PHONE.test(val);
          // ignore character '+'
          if (val.length < 10 || val.length > 16) {
            return { message: ZOD_ERRORS.phoneNumberLength };
          }
          if (!isValidPhone) {
            return { message: ZOD_ERRORS.invalidPhoneNumber };
          }
        } else if (looksLikePhoneWithoutCountryCode) {
          const isValidPhone = REGEX.PHONE_NO_COUNTRY_CODE.test(val);

          if (!isValidPhone) return { message: ZOD_ERRORS.phoneNumberNoCodeLength };
        } else {
          // Validate as email
          const isValidEmail = REGEX.EMAIL.test(val);
          if (!isValidEmail) {
            return { message: ZOD_ERRORS.invalidEmail };
          }
          if (val.length > 254) {
            return { message: ZOD_ERRORS.emailLength };
          }
        }
        return { message: '' };
      }
    ),
});

export type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;
