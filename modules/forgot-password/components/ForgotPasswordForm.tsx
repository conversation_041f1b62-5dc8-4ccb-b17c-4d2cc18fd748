import { useForgotPassMutation } from '@/apis/auth';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { Button } from '@/components/ui/Button';
import TextInput from '@/components/ui/TextInput';
import { DEFAULT_CODE_PENDING } from '@/constants/common';
import { REGEX } from '@/constants/regex';
import { toastError } from '@/utils/toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { router } from 'expo-router';
import { Controller, useForm } from 'react-hook-form';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { ForgotPasswordFormData, forgotPasswordSchema } from '../schema';
import { useGetRegionInfoQuery } from '@/apis/user/queries';
import { phoneTransform } from '@/utils/phoneUtils';

type Props = {};

export const ForgotPasswordForm = (props: Props) => {
  const { styles } = useStyles(stylesheet);
  const { data: regionInfo } = useGetRegionInfoQuery();

  const {
    control,
    handleSubmit,
    formState: { errors, isValid, isSubmitting },
  } = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      emailOrPhone: '',
    },
  });

  const { mutateAsync: forgotPasswordRequest, isPending: isRequestingVerification } = useForgotPassMutation();

  const onSubmit = async (data: ForgotPasswordFormData) => {
    try {
      const identifier = REGEX.DIGIT_ONLY.test(data.emailOrPhone)
        ? phoneTransform(data.emailOrPhone, regionInfo?.calling_code)
        : data.emailOrPhone;

      const { remainingTime } = await forgotPasswordRequest({ identifier });
      router.push({
        pathname: `/verify-code-forgot-password`,
        params: { emailOrPhone: identifier, remainingTime: Number(remainingTime || DEFAULT_CODE_PENDING) },
      });
    } catch (error) {
      toastError(error);
    }
  };

  return (
    <View>
      <Controller
        control={control}
        name='emailOrPhone'
        render={({ field: { onChange, onBlur, value } }) => (
          <TextInput
            label='Email or Phone Number'
            placeholder='Input your email or phone number'
            onChangeText={(text) => onChange(text.replace(/\s/g, ''))}
            onBlur={onBlur}
            value={value}
            autoCapitalize='none'
            error={errors.emailOrPhone?.message}
          />
        )}
      />

      <Spacer height={56} />

      <View style={styles.signInBtnBox}>
        <Button
          type='default'
          isLoading={isRequestingVerification}
          disabled={isSubmitting}
          onPress={handleSubmit(onSubmit)}
        >
          <ThemedText type='defaultBold' style={styles.sendCodeText}>
            Send Code
          </ThemedText>
        </Button>
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  sendCodeText: {
    color: theme.colors.neutralBackground,
  },
  signInBtnBox: {
    paddingHorizontal: 31,
  },
}));
