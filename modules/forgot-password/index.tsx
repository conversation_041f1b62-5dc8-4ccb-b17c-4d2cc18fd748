import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { Header } from '@/components/ui/Header';
import { ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { ForgotPasswordForm } from './components/ForgotPasswordForm';

export default function ForgotPassword() {
  const { styles } = useStyles(stylesheet);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.box} contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
        <Header isBack />

        <Spacer height={48} />

        <ThemedText style={styles.textCenter} type='title'>
          Forgot Password
        </ThemedText>

        <Spacer height={16} />

        <ThemedText style={[styles.textNotice, styles.textCenter]} type='small'>
          We will send an OTP code to change your password.
        </ThemedText>

        <ThemedText style={[styles.textNotice, styles.textCenter]} type='small'>
          Please check your email inbox and spam folder!
        </ThemedText>

        <Spacer height={48} />

        <ForgotPasswordForm />
      </ScrollView>
    </SafeAreaView>
  );
}

const stylesheet = createStyleSheet((theme) => ({
  container: {
    backgroundColor: theme.colors.neutralBackground,
    flex: 1,
  },
  box: {
    flex: 1,
  },
  textNotice: {
    color: theme.colors.neutralLightGrey,
    width: '100%',
  },
  textCenter: {
    textAlign: 'center',
  },
  scrollContent: {
    flexGrow: 1,
    paddingTop: 4,
    paddingBottom: 32,
    paddingHorizontal: 24,
  },
  footer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 'auto',
  },
  signUpText: {
    ...theme.fw500,
    color: theme.colors.primary,
  },
}));
