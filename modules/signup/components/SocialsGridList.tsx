import { SignInApple } from '@/modules/sign-in/components/SignInApple';
import { SignInFacebook } from '@/modules/sign-in/components/SignInFacebook';
import { SignInGoogle } from '@/modules/sign-in/components/SignInGoogle';
import { SignInSpotify } from '@/modules/sign-in/components/SignInSpotify';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

export const SocialsGridList = () => {
  const { styles } = useStyles(stylesheet);

  return (
    <View style={styles.socialButtonsContainer}>
      <View style={styles.socialButtonsRow}>
        <SignInSpotify isSignUp isOnlyIcon />

        <SignInGoogle isOnlyIcon />
      </View>
      <View style={styles.socialButtonsRow}>
        <SignInApple isOnlyIcon />

        <SignInFacebook isOnlyIcon />
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet(() => ({
  socialButtonsContainer: {
    marginBottom: 40,
  },
  socialButtonsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 14,
    gap: 14,
  },
}));
