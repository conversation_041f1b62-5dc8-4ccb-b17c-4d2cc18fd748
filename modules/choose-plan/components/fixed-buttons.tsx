import { useGetProfileQuery } from '@/apis/auth/queries';
import { planPaymentMutation, useGetSubscriptionStatusQuery } from '@/apis/user';
import { Spacer } from '@/components/Spacer';
import { CustomButton } from '@/components/ui/CustomButton';
import { useSubscriptionContext } from '@/contexts/subscription.context';
import { toastError } from '@/utils/toast';
import { router, useLocalSearchParams } from 'expo-router';
import { ImageBackground, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

const FixedButtons = () => {
  const { styles } = useStyles(stylesheet);
  const localParams = useLocalSearchParams();
  const isAccountUpdate = localParams?.isAccountUpdate as string;

  const { packages, customerInfo, isReady, isPurchasing, purchasePackage } = useSubscriptionContext();
  const { data: subscriptionStatus, isPending: isPendingSubscriptionStatus } = useGetSubscriptionStatusQuery();
  const { mutateAsync: planPaymentFree, isPending: isPendingPlanPaymentFree } = planPaymentMutation();
  const { refetch: refetchProfile, isRefetching: isRefetchingProfile } = useGetProfileQuery();

  const premium = customerInfo?.entitlements.active['premium'];
  const isCancelled =
    // subscriptionStatus?.subscription?.status === 'cancelled' ||
    premium && (!premium?.willRenew || !!premium?.unsubscribeDetectedAt);

  const handleJoinPatron = async () => {
    if (packages.length === 0) return;

    if (!premium || isCancelled) {
      purchasePackage(packages[0]);
    }
  };

  const handleMaybeLater = async () => {
    try {
      await planPaymentFree({
        status: 'choose_interest',
      });

      await refetchProfile();

      router.replace({
        pathname: '/(app)/(tabs)',
      });
    } catch (error) {
      toastError(error);
    }
  };

  return (
    <ImageBackground style={styles.container} source={require('@/assets/images/subscription_actions_blur.png')}>
      {/* <BlurView
        blurAmount={Platform.OS === 'ios' ? 60 : 30}
        blurType='dark'
        reducedTransparencyFallbackColor='rgba(0, 0, 0, 0.5)'
        style={styles.blurView}
      /> */}

      <View>
        <CustomButton
          disabled={!isReady || packages.length === 0 || isPendingSubscriptionStatus}
          isLoading={isPurchasing}
          style={styles.joinButton}
          onPress={handleJoinPatron}
          textType='defaultBold'
        >
          Join Founding Patron
        </CustomButton>

        {isAccountUpdate !== 'true' && (
          <>
            <Spacer height={16} />

            <CustomButton
              type='text'
              isLoading={isPendingPlanPaymentFree || isRefetchingProfile}
              onPress={handleMaybeLater}
              textType='defaultBold'
              textStyle={styles.maybeLaterText}
            >
              Maybe Later
            </CustomButton>
          </>
        )}
      </View>
    </ImageBackground>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    paddingTop: 32,
    paddingHorizontal: 59,
    flex: 1,
    paddingBottom: rt.insets.bottom + 20,
    overflow: 'hidden',
    zIndex: 0,
    backgroundColor: '#0E100FBF',
  },
  blurView: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
  },
  joinButton: {
    minHeight: 48,
  },
  maybeLaterText: {
    color: theme.colors.primary,
  },
}));

export default FixedButtons;
