import { ThemedText } from '@/components/ThemedText';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

const FreeCard = () => {
  const { styles } = useStyles(stylesheet);

  return (
    <View style={[styles.planCard, styles.planCardFree]}>
      <ThemedText style={styles.planTitle}>Founding Member</ThemedText>
      <ThemedText style={styles.freePrice}>Free</ThemedText>

      <ThemedText style={styles.planDescription}>Get full access to all features at no cost.</ThemedText>
      <ThemedText style={styles.planDescription}>
        No limitations, no trials—just everything you need, completely free.
      </ThemedText>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  headerText: {
    fontSize: 24,
    ...theme.fw600,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 32,
    marginTop: 28,
  },
  coinContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    marginVertical: 24,
  },
  coinImage: {
    width: 280,
    height: 298,
    zIndex: 1,
  },
  planCard: {
    backgroundColor: theme.colors.neutralCard,
    borderRadius: 16,
    padding: 24,
    marginBottom: 24,
  },
  planCardFree: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#FFFFFF1A',
  },
  planTitle: {
    fontSize: 20,
    lineHeight: 28,
    ...theme.fw500,
    marginBottom: 24,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    height: 32,
  },
  priceAmount: {
    fontSize: 28,
    ...theme.fw700,
    lineHeight: 32,
  },
  pricePeriod: {
    fontSize: 14,
    marginLeft: 4,
    opacity: 0.8,
  },
  freePrice: {
    fontSize: 28,
    ...theme.fw700,
    lineHeight: 32,
    marginBottom: 16,
  },
  planDescription: {
    fontSize: 14,
    lineHeight: 20,
    opacity: 0.9,
  },
  checkIcon: {
    width: 24,
    height: 24,
    marginRight: 16,
  },
  benefitText: {
    fontSize: 14,
    opacity: 0.9,
  },
}));

export default FreeCard;
