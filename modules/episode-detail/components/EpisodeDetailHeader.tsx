import {
  useDeleteEpisodeRate,
  useGetEpisodeByIdQuery,
  useGetEpisodeMarkAsWatchQuery,
  useGetEpisodeRateByIdQuery,
  useGetEpisodeWatchListQuery,
  useRateEpisode,
  useToggleLikeEpisode,
  useToggleWatched,
  useToggleWatchlist,
} from '@/apis/episode';
import { Icons } from '@/assets/icons';
import StarRateFill from '@/assets/icons/start-rate-fill';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { ExpoImage } from '@/components/ui/Image';
import { useExpandableText } from '@/hooks/useExpandableText';
import { durationTime, formatCompactNumber } from '@/utils/func';
import { toastError, toastSuccess } from '@/utils/toast';
import { Ionicons } from '@expo/vector-icons';
import { useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import { router } from 'expo-router';
import { memo, useCallback, useState } from 'react';
import { Pressable, TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import DeleteRateModal from '../../show-detail/components/DeleteRateModal';
import EpisodeMenuModal from '../../show-detail/components/EpisodeMenuModal';
import JoinCommunityButton from '../../show-detail/components/JoinCommunityButton';
import LikeShowButton from '../../show-detail/components/LikeShowButton';
import RateButton from '../../show-detail/components/RateButton';
import RatingModal from '../../show-detail/components/RatingModal';
import queryKeys from '@/utils/queryKeys';
import bigDecimal from 'js-big-decimal';
import { showDetailDirect } from '@/utils/router-prefetch';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';
import { Skeleton } from 'moti/skeleton';

type Props = {
  episodeId: string;
};

export const EpisodeDetailHeader = memo(({ episodeId }: Props) => {
  const { styles, theme } = useStyles(stylesheet);

  const [showDeleteRateModal, setShowDeleteRateModal] = useState(false);
  const [showRateModal, setShowRateModal] = useState(false);
  const [showMenuModal, setShowMenuModal] = useState(false);

  const { onCheckAccountRestricted } = useCheckRestrictAccount();

  const { isExpanded, showButton, toggleExpanded, onTextLayout, numberOfLines } = useExpandableText({
    maxLines: 3,
  });

  const queryClient = useQueryClient();

  const { data: episode, isPending: isPendingEpisode } = useGetEpisodeByIdQuery(episodeId, { enabled: !!episodeId });
  const { data: podcastRate, isPending: isPendingEpisodeRate } = useGetEpisodeRateByIdQuery(episodeId);
  const { data: watchList } = useGetEpisodeWatchListQuery(episodeId, { enabled: !!episodeId });
  const { data: markAsWatchInfo } = useGetEpisodeMarkAsWatchQuery(episodeId, { enabled: !!episodeId });

  const { mutateAsync: rateEpisode, isPending: isPendingAddRate } = useRateEpisode();
  const { mutateAsync: deleteRate, isPending: isPendingDeleteRate } = useDeleteEpisodeRate();
  const { mutateAsync: toggleWatchlist, isPending: isPendingToggleWatchlist } = useToggleWatchlist();
  const { mutateAsync: toggleWatched, isPending: isPendingToggleWatched } = useToggleWatched();
  const { mutateAsync: toggleLike } = useToggleLikeEpisode();

  const handleBackPress = () => {
    router.dismiss();
  };

  const handleMenuPress = useCallback(() => setShowMenuModal(true), []);

  const onDeleteRatePressed = () => {
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    setShowDeleteRateModal(true);
  };

  const handleRateSubmit = async (value: number) => {
    try {
      const isRestricted = onCheckAccountRestricted();
      if (isRestricted) return;

      await rateEpisode({
        episodeId,
        rate: value,
      });
      toastSuccess({ description: 'Rated episode successfully' });
      refetchDiscoverPage();
    } catch (error) {
      toastError(error);
    } finally {
      setShowRateModal(false);
    }
  };

  const handleAddToWatchlist = async () => {
    try {
      const isRestricted = onCheckAccountRestricted();
      if (isRestricted) return;

      const isInWatchlist = !!watchList;

      await toggleWatchlist({
        episodeId: episodeId,
        isInWatchlist: isInWatchlist,
      });

      toastSuccess({
        description: isInWatchlist ? 'Removed from watchlist successfully' : 'Added to watchlist successfully',
      });
    } catch (error) {
      toastError(error);
    } finally {
      setShowMenuModal(false);
    }
  };

  const handleDeleteRate = async () => {
    try {
      const isRestricted = onCheckAccountRestricted();
      if (isRestricted) return;

      if (!podcastRate) {
        toastError('Delete rate failed! Episode has not been rated yet!');
        return;
      }
      await deleteRate(episodeId);

      refetchDiscoverPage();
      toastSuccess({ description: 'Deleted rate successfully' });
    } catch (error) {
      toastError(error);
    } finally {
      setShowDeleteRateModal(false);
    }
  };

  const refetchDiscoverPage = () => {
    Promise.all([
      queryClient.invalidateQueries({
        queryKey: queryKeys.podcasts.podcasts({
          limit: 5,
          page: 1,
          isTrending: true,
        }),
      }),
      queryClient.invalidateQueries({ queryKey: queryKeys.podcasts.newestEpisodes() }),
    ]);
  };

  const handleMarkAsWatch = async () => {
    try {
      const isRestricted = onCheckAccountRestricted();
      if (isRestricted) return;

      const isWatched = !!markAsWatchInfo;

      await toggleWatched({
        episodeId,
        isWatched: isWatched,
      });

      toastSuccess({ description: isWatched ? 'Unmarked as watched successfully' : 'Marked as watched successfully' });
    } catch (error) {
      toastError(error);
    } finally {
      setShowMenuModal(false);
    }
  };

  const handleLike = async () => {
    if (!episode) return;

    try {
      const isRestricted = onCheckAccountRestricted();
      if (isRestricted) return;

      const isLiked = episode?.hasLiked;

      await toggleLike({
        episodeId: episode.id,
        isLiked: !isLiked,
      });
      toastSuccess({ description: isLiked ? 'Unliked episode successfully' : 'Liked episode successfully' });
    } catch (error) {
      toastError(error);
    }
  };

  const handleRatePress = useCallback(() => {
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    setShowRateModal(true);
  }, [onCheckAccountRestricted]);

  const handleDirectToPodcastDetail = async () => {
    if (!episode) return;

    await showDetailDirect(queryClient, episode?.podcastId?.toString());
  };

  const handleInteract = () => {
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    router.push({
      pathname: '/(app)/episode/[episodeId]/add-post',
      params: {
        episodeId,
      },
    });
  };

  const episodeImage = episode?.imageUrl;
  const rateCount = Number(episode?.rateCount || 0);
  const formattedRateCount = formatCompactNumber(rateCount);
  const episodePublishDate = episode?.releaseDate ? format(new Date(episode?.releaseDate), 'MMM dd, yyyy') : '';
  const episodeDuration = durationTime(Number(episode?.duration || 0));
  const avgRate = bigDecimal.round(episode?.avgRate, 1, bigDecimal.RoundingModes.HALF_UP);
  const userRate = bigDecimal.round(podcastRate?.rate, 1, bigDecimal.RoundingModes.HALF_UP);
  const isShowSkeleton = isPendingEpisode || isPendingEpisodeRate;

  return (
    <View style={styles.container}>
      <RatingModal
        isVisible={showRateModal}
        defaultRate={podcastRate?.rate}
        onClose={() => setShowRateModal(false)}
        onRateSubmit={handleRateSubmit}
        isAddingRate={isPendingAddRate}
        enjoyName={episode?.title || ''}
        image={episode?.imageUrl}
      />

      <EpisodeMenuModal
        isVisible={showMenuModal}
        onAddToWatchlist={handleAddToWatchlist}
        onUnmarkAsWatched={handleMarkAsWatch}
        isInWatchlist={!!watchList}
        isMarkedAsWatched={!!markAsWatchInfo}
        isPendingWatchlist={isPendingToggleWatchlist}
        isPendingMarkAsWatched={isPendingToggleWatched}
        onClose={() => setShowMenuModal(false)}
      />

      <DeleteRateModal
        isDeleting={isPendingDeleteRate}
        isVisible={showDeleteRateModal}
        onClose={() => setShowDeleteRateModal(false)}
        onDeleteConfirm={handleDeleteRate}
      />

      <View style={styles.header}>
        <Pressable onPress={handleBackPress} style={styles.backButton}>
          <Ionicons name='arrow-back' size={24} color='white' />
        </Pressable>

        {isShowSkeleton ? (
          <View style={styles.headerImageContainer} pointerEvents='none'>
            <Skeleton width={200} height={200} />
          </View>
        ) : (
          <View style={styles.headerImageContainer} pointerEvents='none'>
            <ExpoImage source={{ uri: episodeImage }} style={styles.headerImage} contentFit='contain' />
          </View>
        )}
      </View>

      <Spacer height={32} />

      <View style={styles.showInfo}>
        {isShowSkeleton ? (
          <Skeleton width={150} height={28} />
        ) : (
          <ThemedText type='subtitleMedium' pointerEvents='box-none'>
            {episode?.title?.trim()}
          </ThemedText>
        )}

        <Spacer height={13.5} />

        {isShowSkeleton ? (
          <Skeleton width={160} height={20} />
        ) : (
          <ThemedText style={styles.episodePublishDate} type='small' pointerEvents='box-none'>
            {episodePublishDate} • {episodeDuration}
          </ThemedText>
        )}

        <Spacer height={13.5} />

        {isShowSkeleton ? (
          <Skeleton width={180} height={24} />
        ) : (
          <TouchableOpacity
            activeOpacity={0.7}
            onPress={() => router.push(`/(app)/episode/${episodeId}/rating`)}
            style={styles.ratingContainer}
          >
            <StarRateFill style={styles.rateIcon} width={20} height={20} color={theme.colors.stateWarning} />

            <ThemedText style={styles.ratingNumber}>{avgRate}</ThemedText>

            <ThemedText style={styles.ratingText}>
              (Based on {formattedRateCount} {Number(rateCount) > 1 ? 'reviews' : 'review'})
            </ThemedText>

            {podcastRate?.rate && podcastRate?.rate !== 0 && (
              <>
                <StarRateFill height={16} width={16} color={theme.colors.primary} style={styles.userRateIcon} />
                <ThemedText style={styles.userRateText}>{userRate}</ThemedText>
              </>
            )}
          </TouchableOpacity>
        )}

        <Spacer height={16} />

        {isShowSkeleton ? (
          <View style={styles.podcastInfoContainer}>
            <Skeleton width={40} height={40} />
            <Spacer width={12} />
            <Skeleton width={80} height={24} />
          </View>
        ) : (
          <TouchableOpacity
            activeOpacity={0.7}
            onPress={handleDirectToPodcastDetail}
            style={styles.podcastInfoContainer}
          >
            <ExpoImage source={{ uri: episode?.podcastImageUrl }} style={styles.podcastImage} />

            <Spacer width={12} />

            <ThemedText type='tinyMedium' style={styles.fullFlex}>
              {episode?.podcastTitle}
            </ThemedText>
          </TouchableOpacity>
        )}

        <Spacer height={12} />

        <View style={styles.descriptionContainer}>
          {isShowSkeleton ? (
            <>
              <Skeleton width={'100%'} height={18} />
              <Spacer height={2} />
              <Skeleton width={'100%'} height={20} />
              <Spacer height={2} />
              <Skeleton width={'80%'} height={20} />
            </>
          ) : (
            <ThemedText style={styles.description} numberOfLines={numberOfLines} onTextLayout={onTextLayout}>
              {episode?.podcastDescription?.trim()}
            </ThemedText>
          )}

          {showButton && (
            <TouchableOpacity activeOpacity={0.7} onPress={toggleExpanded} style={styles.viewMoreButton}>
              <ThemedText style={styles.viewMoreText}>{isExpanded ? 'View Less' : 'View More'}</ThemedText>
              <Ionicons
                name={isExpanded ? 'chevron-up' : 'chevron-down'}
                size={16}
                color={theme.colors.neutralWhite}
                style={styles.dropdownIcon}
              />
            </TouchableOpacity>
          )}
        </View>
      </View>

      <Spacer height={24} />

      {isShowSkeleton ? (
        <View style={styles.actionButtons}>
          <View style={styles.fullFlex}>
            <Skeleton width={'100%'} height={38} radius={999} />
          </View>
          <View style={styles.fullFlex}>
            <Skeleton width={'100%'} height={38} radius={999} />
          </View>
          <View style={styles.fullFlex}>
            <Skeleton width={'100%'} height={38} radius={999} />
          </View>
          <Skeleton width={38} height={38} radius={999} />
        </View>
      ) : (
        <View style={styles.actionButtons}>
          <JoinCommunityButton onPress={handleInteract} />

          <LikeShowButton isLiked={episode?.hasLiked || false} onPress={handleLike} />

          <RateButton
            isRated={!!podcastRate?.rate || false}
            rating={podcastRate?.rate}
            onPress={handleRatePress}
            onDeleteRate={onDeleteRatePressed}
            onEditRate={() => setShowRateModal(true)}
          />

          <Pressable style={styles.menuButton} onPress={handleMenuPress}>
            <Icons.EllipsisVertical size={16} color={theme.colors.neutralWhite} />
          </Pressable>
        </View>
      )}
    </View>
  );
});

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    paddingRight: 24,
  },
  header: {
    width: '100%',
    height: 200,
    position: 'relative',
    borderRadius: 12,
  },
  backButton: {
    position: 'absolute',
    top: 0,
    left: 0,
    zIndex: 10,
    width: 40,
    height: 40,
    borderRadius: 999,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.neutralBackground,
  },
  headerImageContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 12,
    overflow: 'hidden',
    pointerEvents: 'none',
  },
  headerImage: {
    width: '100%',
    height: '100%',
    objectFit: 'contain',
  },
  content: {
    flex: 1,
  },
  showInfo: {
    width: '100%',
  },
  podcastTitleText: {
    fontSize: 16,
    color: theme.colors.neutralWhite,
    opacity: 0.8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rateIcon: {
    marginRight: 12,
  },
  userRateIcon: {
    marginLeft: 12,
  },
  userRateText: {
    fontSize: 12,
    color: theme.colors.neutralWhite,
    marginLeft: 8,
  },
  ratingNumber: {
    fontSize: 12,
    color: theme.colors.neutralWhite,
    marginLeft: 4,
  },
  ratingText: {
    fontSize: 12,
    color: '#AAAAAA',
    marginLeft: 4,
  },
  descriptionContainer: {
    flexDirection: 'column',
  },
  description: {
    fontSize: 14,
    color: theme.colors.neutralWhite,
    opacity: 0.56,
    lineHeight: 20,
    pointerEvents: 'box-none',
  },
  viewMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'center',
    marginTop: 8,
    paddingVertical: 4,
    paddingHorizontal: 8,
    gap: 3,
  },
  viewMoreText: {
    fontSize: 12,
    color: theme.colors.neutralWhite,
    ...theme.fw500,
  },
  dropdownIcon: {
    marginLeft: 3,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginBottom: 24,
    maxHeight: 38,
    gap: 8,
  },
  menuButton: {
    width: 36,
    height: 36,
    borderRadius: 999,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#FFFFFF3D',
  },
  episodePublishDate: {
    color: theme.colors.textSecondary,
  },
  podcastImage: {
    width: 40,
    height: 40,
    borderRadius: 8,
  },
  podcastInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  fullFlex: {
    flex: 1,
  },
}));
