import BottomModal from '@/components/BottomModal';
import { ThemedText } from '@/components/ThemedText';
import { CustomButton } from '@/components/ui/CustomButton';
import { View } from 'react-native';
import { useStyles } from 'react-native-unistyles';

type DeleteCommentModalProps = {
  visible: boolean;
  onClose: () => void;
  onDelete: () => void;
  isPendingDelete: boolean;
};

function DeleteCommentModal({ visible, onClose, onDelete, isPendingDelete }: DeleteCommentModalProps) {
  const { theme } = useStyles();

  return (
    <BottomModal isVisible={visible} onClose={onClose}>
      <View style={{ padding: 24 }}>
        <ThemedText type='subtitleMedium' style={{ color: theme.colors.whiteOpacity80, marginBottom: 28 }}>
          Are you sure you want to delete this post community?
        </ThemedText>

        <CustomButton
          type='danger'
          onPress={onDelete}
          style={{ marginBottom: 14 }}
          isLoading={isPendingDelete}
          textStyle={{ ...theme.fw700 }}
        >
          Yes
        </CustomButton>

        <CustomButton type='outlined' onPress={onClose} textStyle={{ ...theme.fw700 }}>
          Cancel
        </CustomButton>
      </View>
    </BottomModal>
  );
}

export default DeleteCommentModal;
