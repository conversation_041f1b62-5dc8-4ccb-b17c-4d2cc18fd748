import { Icons } from '@/assets/icons';
import { ThemedText } from '@/components/ThemedText';
import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import { StyleProp, TouchableOpacity, View, ViewStyle } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import RateMenu from './RateMenu';
import { useScaleFocus } from '@/hooks/useScaleFocus';
import Animated from 'react-native-reanimated';

type ButtonSize = 'sm' | 'md' | 'w-full';

interface RateButtonProps {
  isRated: boolean;
  rating?: number;
  onPress: () => void;
  onDeleteRate: () => void;
  onEditRate: () => void;
  style?: StyleProp<ViewStyle>;
  size?: ButtonSize;
  containerStyle?: StyleProp<ViewStyle>;
}

const RateButton: React.FC<RateButtonProps> = ({
  containerStyle,
  isRated,
  onPress,
  onDeleteRate,
  onEditRate,
  style,
  size = 'md',
}) => {
  const { styles, theme } = useStyles(stylesheet);

  const { animatedStyle, handlePressIn, handlePressOut } = useScaleFocus(0.95);

  const [open, setOpen] = React.useState(false);

  const handleEdit = () => {
    onEditRate();
    setOpen(false);
  };

  const handleDelete = () => {
    onDeleteRate();
    setOpen(false);
  };

  const sizeStyles: Record<ButtonSize, ViewStyle> = {
    md: styles.buttonMd,
    sm: styles.buttonSm,
    'w-full': styles.button,
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {!isRated && (
        <Animated.View style={[animatedStyle, styles.buttonContainer]}>
          <TouchableOpacity
            activeOpacity={0.7}
            onPressIn={handlePressIn}
            onPressOut={handlePressOut}
            onPress={onPress}
            style={[styles.button, sizeStyles[size], style]}
          >
            <View style={styles.iconContainer}>
              <Icons.StarRateOutlineV2 size={16} color='#fff' strokeOpacity={1} />
            </View>

            <ThemedText type='tinyMedium' numberOfLines={1} style={styles.buttonText}>
              Rate
            </ThemedText>
          </TouchableOpacity>
        </Animated.View>
      )}

      {isRated && (
        <RateMenu open={open} setOpen={setOpen} onEditRate={handleEdit} onDeleteRate={handleDelete}>
          <View style={[styles.button, styles.buttonRated, style]}>
            <Icons.StarRateFill color={theme.colors.primary} width={16} height={16} />

            <ThemedText type='tinyMedium' numberOfLines={1} style={styles.buttonText}>
              {isRated ? `Rated` : 'Rate'}
            </ThemedText>

            <Ionicons name='chevron-down' color={theme.colors.neutralWhite} />
          </View>
        </RateMenu>
      )}
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 38,
    flex: 1,
  },
  buttonContainer: {
    flex: 1,
  },
  buttonRated: {
    pointerEvents: 'none',
    borderColor: '#D9FF031A',
  },
  button: {
    height: 38,
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 9999,
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#D9FF031A',
    gap: 8,
  },
  buttonMd: {
    paddingVertical: 10,
    paddingHorizontal: 15,
  },
  buttonSm: {
    paddingVertical: 7,
    paddingHorizontal: 12,
    height: 38,
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    color: theme.colors.neutralWhite,
    // marginRight: 8,
    lineHeight: 16,
    flexWrap: 'nowrap',
    pointerEvents: 'none',
  },
}));

export default RateButton;
