import BottomModal from '@/components/BottomModal';
import { ThemedText } from '@/components/ThemedText';
import { CustomButton } from '@/components/ui/CustomButton';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

interface DeleteRateModalProps {
  isVisible: boolean;
  onClose: () => void;
  onDeleteConfirm: () => void;
  isDeleting: boolean;
}

const DeleteRateModal = ({ isVisible, onClose, onDeleteConfirm, isDeleting }: DeleteRateModalProps) => {
  const { styles } = useStyles(stylesheet);

  return (
    <BottomModal isVisible={isVisible} onClose={onClose}>
      <View style={styles.deleteRateModalContent}>
        <ThemedText style={styles.deleteRateModalTitle}>Are you sure you want to delete this rating?</ThemedText>
        <View style={styles.deleteRateModalButtons}>
          <CustomButton type='danger' isLoading={isDeleting} onPress={onDeleteConfirm} textStyle={styles.confirmText}>
            Yes
          </CustomButton>
          <CustomButton type='outlined' disabled={isDeleting} onPress={onClose} textStyle={styles.confirmText}>
            Cancel
          </CustomButton>
        </View>
      </View>
    </BottomModal>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  confirmText: {
    ...theme.fw700,
  },
  deleteRateModalContent: {
    padding: 24,
    alignItems: 'center',
  },
  deleteRateModalTitle: {
    width: '100%',
    fontSize: 20,
    ...theme.fw500,
    color: theme.colors.neutralWhite,
    marginBottom: 28,
    lineHeight: 24,
  },
  deleteRateModalButtons: {
    width: '100%',
    gap: 14,
  },
  deleteRateYesButton: {
    width: '100%',
  },
  deleteRateCancelButton: {
    width: '100%',
  },
}));

export default DeleteRateModal;
