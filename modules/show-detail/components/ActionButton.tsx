import { ThemedText } from '@/components/ThemedText';
import { useScaleFocus } from '@/hooks/useScaleFocus';
import React, { ReactNode } from 'react';
import { TouchableOpacity, View } from 'react-native';
import Animated from 'react-native-reanimated';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

interface ActionButtonProps {
  onPress: () => void;
  icon: ReactNode;
  text: string;
}

const ActionButton: React.FC<ActionButtonProps> = ({ onPress, icon, text }) => {
  const { styles } = useStyles(styleSheet);

  const { animatedStyle, handlePressIn, handlePressOut } = useScaleFocus(0.95);

  return (
    <Animated.View style={[animatedStyle, styles.fullFlex]}>
      <TouchableOpacity
        activeOpacity={0.7}
        onPress={onPress}
        style={[styles.button]}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
      >
        <View style={styles.iconContainer}>{icon}</View>

        <ThemedText numberOfLines={1} type='tinySemiBold' style={styles.buttonText}>
          {text}
        </ThemedText>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styleSheet = createStyleSheet((theme) => ({
  fullFlex: {
    flex: 1,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 9999,
    borderWidth: 1,
    borderColor: theme.colors.neutralGrey,
    gap: 8,
    flex: 1,
    paddingVertical: 0,
    paddingHorizontal: 16,
    height: 38,
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    color: theme.colors.neutralWhite,
  },
}));

export default ActionButton;
