import { Spacer } from '@/components/Spacer';
import { Skeleton } from 'moti/skeleton';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

const EpisodeSkeleton = () => {
  const { styles } = useStyles(stylesheet);

  return (
    <View style={styles.skeletonContainer}>
      <View style={styles.episodeRow}>
        <Skeleton width={96} height={96} radius={4} />

        <View style={styles.episodeInfo}>
          <Skeleton width={'70%'} height={18} />

          <Spacer height={4} />

          <Skeleton width={'90%'} height={20} />

          <Spacer height={4} />

          <Skeleton width={'40%'} height={18} />
        </View>
      </View>

      <View style={styles.episodeActions}>
        <View style={styles.fullFlex}>
          <Skeleton width={'100%'} height={32} radius={16} />
        </View>

        <View style={styles.fullFlex}>
          <Skeleton width={'100%'} height={32} radius={16} />
        </View>

        <View style={styles.fullFlex}>
          <Skeleton width={'100%'} height={32} radius={16} />
        </View>
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet(() => ({
  skeletonContainer: {
    flexDirection: 'column',
    gap: 16,
    flex: 1,
  },
  episodeRow: {
    flexDirection: 'row',
  },
  episodeInfo: {
    flex: 1,
    marginLeft: 24,
    justifyContent: 'center',
  },
  episodeActions: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
    flex: 1,
  },
  fullFlex: {
    flex: 1,
  },
}));

export default EpisodeSkeleton;
