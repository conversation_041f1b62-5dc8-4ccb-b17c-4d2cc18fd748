import BottomModal from '@/components/BottomModal';
import { RateStar } from '@/components/RateStar';
import { ThemedText } from '@/components/ThemedText';
import { CustomButton } from '@/components/ui/CustomButton';
import { ExpoImage } from '@/components/ui/Image';
import { useEffect, useState } from 'react';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

interface RatingModalProps {
  onRateSubmit: (rating: number) => void;
  isVisible: boolean;
  onClose: () => void;
  defaultRate?: number;
  isAddingRate?: boolean;
  image?: string;
  enjoyName: string;
}

const RatingModal = ({
  onRateSubmit,
  isVisible,
  onClose,
  defaultRate,
  isAddingRate,
  enjoyName,
  image,
}: RatingModalProps) => {
  const DEFAULT_RATE = defaultRate || 0;
  const { styles } = useStyles(stylesheet);
  const [selectedRating, setSelectedRating] = useState(DEFAULT_RATE);

  const handleRating = (rating: number) => {
    setSelectedRating(rating);
  };

  const handleSubmit = () => {
    if (selectedRating > 0) {
      onRateSubmit(selectedRating);
    }
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    setSelectedRating(DEFAULT_RATE);
  }, [isVisible, DEFAULT_RATE]);

  return (
    <BottomModal isVisible={isVisible} onClose={onClose}>
      <View style={styles.contentContainer}>
        {image && <ExpoImage source={{ uri: image }} style={styles.showImage} />}

        <View style={styles.paddingH}>
          <ThemedText style={styles.title}>How Much Do You Enjoy {enjoyName}?</ThemedText>
        </View>

        <View style={[styles.starsContainer]}>
          <RateStar rating={selectedRating || 0} size={48} gap={16} onChange={handleRating} enabled />
        </View>

        <View style={[styles.paddingH, styles.fullWidth]}>
          <CustomButton
            textType='default'
            isLoading={isAddingRate}
            style={styles.rateButton}
            onPress={handleSubmit}
            disabled={selectedRating === 0}
            textStyle={styles.rateText}
          >
            Rate
          </CustomButton>
        </View>
      </View>
    </BottomModal>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  rateText: {
    ...theme.fw700,
  },
  contentContainer: {
    backgroundColor: theme.colors.neutralCard,
    paddingVertical: 32,
    paddingTop: 56,
    borderRadius: 40,
    alignItems: 'center',
  },
  paddingH: {
    paddingHorizontal: 32,
  },
  showImage: {
    width: 72,
    height: 72,
    borderRadius: 8,
    marginBottom: 24,
  },
  title: {
    fontSize: 20,
    lineHeight: 28,
    ...theme.fw500,
    color: theme.colors.neutralWhite,
    textAlign: 'center',
    marginBottom: 56,
  },
  starsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 72,
  },
  starButton: {
    padding: 10,
  },
  rateButton: {
    width: '100%',
  },
  rateButtonTextActive: {
    color: theme.colors.neutralWhite,
  },
  fullWidth: {
    width: '100%',
  },
}));

export default RatingModal;
