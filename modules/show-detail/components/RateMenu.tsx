import DeleteOutline from '@/assets/icons/delete-outline';
import EditOutline from '@/assets/icons/edit-outline';
import { ThemedText } from '@/components/ThemedText';
import { DropdownMenu, MenuOption } from '@/components/dropdown-menu';
import { PropsWithChildren } from 'react';
import { StyleProp, View, ViewStyle } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

interface RateMenuProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  onEditRate: () => void;
  onDeleteRate: () => void;
  triggerStyle?: StyleProp<ViewStyle>;
}

function RateMenu({
  open,
  setOpen,
  onEditRate,
  onDeleteRate,
  triggerStyle,
  children,
}: PropsWithChildren<RateMenuProps>) {
  const { styles, theme } = useStyles(stylesheet);
  return (
    <DropdownMenu
      visible={open}
      handleOpen={() => setOpen(true)}
      handleClose={() => setOpen(false)}
      trigger={children}
      triggerStyle={triggerStyle as ViewStyle}
    >
      <MenuOption onSelect={onEditRate}>
        <EditOutline />
        <ThemedText style={styles.buttonText}>Edit Rate</ThemedText>
      </MenuOption>

      <View style={styles.divider} />

      <MenuOption onSelect={onDeleteRate}>
        <DeleteOutline color={theme.colors.stateError} />

        <ThemedText style={styles.deleteButtonText}>Delete Rate</ThemedText>
      </MenuOption>
    </DropdownMenu>
  );
}

const stylesheet = createStyleSheet((theme) => ({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 20,
    paddingVertical: 10,
    paddingHorizontal: 15,
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: theme.colors.neutralGrey,
    width: 114,
  },
  optionsContainerStyle: {
    marginTop: 52,
    backgroundColor: theme.colors.neutralDarkGrey,
    borderRadius: 8,
  },
  menuOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    width: 172,
  },
  buttonText: {
    marginLeft: 8,
    color: theme.colors.neutralWhite,
    ...theme.fw600,
    fontSize: 12,
    lineHeight: 12,
    marginRight: 8,
  },
  deleteButtonText: {
    marginLeft: 6,
    color: theme.colors.stateError,
    ...theme.fw600,
    lineHeight: 12,
    fontSize: 12,
  },
  divider: { height: 2, width: '100%', backgroundColor: theme.colors.neutralGrey },
}));

export default RateMenu;
