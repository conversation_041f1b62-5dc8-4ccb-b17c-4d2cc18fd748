import BottomModal from '@/components/BottomModal';
import { ThemedText } from '@/components/ThemedText';
import { Ionicons } from '@expo/vector-icons';
import { TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

interface EpisodeMenuModalProps {
  isVisible: boolean;
  onClose: () => void;
  onAddToWatchlist: () => void;
  onUnmarkAsWatched: () => void;
  isInWatchlist?: boolean;
  isMarkedAsWatched?: boolean;
  isPendingMarkAsWatched?: boolean;
  isPendingWatchlist?: boolean;
}

const EpisodeMenuModal = ({
  isVisible,
  onClose,
  onAddToWatchlist,
  onUnmarkAsWatched,
  isInWatchlist,
  isMarkedAsWatched,
  isPendingMarkAsWatched,
  isPendingWatchlist,
}: EpisodeMenuModalProps) => {
  const { theme, styles } = useStyles(styleSheet);

  return (
    <BottomModal isVisible={isVisible} onClose={onClose}>
      <View style={styles.contentContainer}>
        {!isInWatchlist ? (
          <TouchableOpacity disabled={isPendingWatchlist} style={styles.menuItem} onPress={onAddToWatchlist}>
            <Ionicons name='bookmark-outline' size={24} color={theme.colors.neutralWhite} />
            <ThemedText style={styles.menuItemText}>Add to Watchlist</ThemedText>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity disabled={isPendingWatchlist} style={styles.menuItem} onPress={onAddToWatchlist}>
            <Ionicons name='bookmark' size={24} color={theme.colors.neutralWhite} />
            <ThemedText style={styles.menuItemText}>Remove From Watchlist</ThemedText>
          </TouchableOpacity>
        )}

        {!isMarkedAsWatched ? (
          <TouchableOpacity disabled={isPendingMarkAsWatched} style={styles.menuItem} onPress={onUnmarkAsWatched}>
            <Ionicons name='checkmark-circle-outline' size={24} color={theme.colors.neutralWhite} />
            <ThemedText style={styles.menuItemText}>Mark as Watched</ThemedText>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity disabled={isPendingMarkAsWatched} style={styles.menuItem} onPress={onUnmarkAsWatched}>
            <Ionicons name='checkmark-circle' size={24} color={theme.colors.neutralWhite} />
            <ThemedText style={styles.menuItemText}>Unmark as Watched</ThemedText>
          </TouchableOpacity>
        )}
      </View>
    </BottomModal>
  );
};

const styleSheet = createStyleSheet((theme) => ({
  contentContainer: {
    backgroundColor: theme.colors.neutralCard,
    padding: 32,
    borderRadius: 40,
    gap: 24,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuItemText: {
    color: theme.colors.neutralWhite,
    fontSize: 16,
    marginLeft: 24,
    ...theme.fw500,
  },
}));

export default EpisodeMenuModal;
