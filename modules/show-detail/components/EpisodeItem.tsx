import {
  useDeleteEpisodeRate,
  useRateEpisode,
  useToggleLikeEpisode,
  useToggleWatched,
  useToggleWatchlist,
} from '@/apis/episode/mutations';
import { Episode } from '@/apis/episode/types';
import StarRateFill from '@/assets/icons/start-rate-fill';
import { ThemedText } from '@/components/ThemedText';
import { ExpoImage } from '@/components/ui/Image';
import { formatCompactNumber } from '@/utils/func';
import { toastError, toastSuccess } from '@/utils/toast';
import { Ionicons } from '@expo/vector-icons';
import { format } from 'date-fns';
import { router } from 'expo-router';
import { memo, useState } from 'react';
import { Pressable, TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import ActionButton from './ActionButton';
import DeleteRateModal from './DeleteRateModal';
import EpisodeMenuModal from './EpisodeMenuModal';
import LikeShowButton from './LikeShowButton';
import RateButton from './RateButton';
import RatingModal from './RatingModal';
import bigDecimal from 'js-big-decimal';
import { episodeDetailDirect } from '@/utils/router-prefetch';
import { useQueryClient } from '@tanstack/react-query';
import { useCheckRestrictAccount } from '@/hooks/useCheckRestrictAccount';

type Props = {
  item: Episode;
};

export const EpisodeItem = memo(({ item }: Props) => {
  const { styles } = useStyles(stylesheet);
  const queryClient = useQueryClient();

  const { onCheckAccountRestricted } = useCheckRestrictAccount();
  const { mutateAsync: toggleLike } = useToggleLikeEpisode();
  const { mutateAsync: toggleWatchlist, isPending: isPendingWatchlist } = useToggleWatchlist();
  const { mutateAsync: toggleWatched, isPending: isPendingWatched } = useToggleWatched();
  const { mutateAsync: rateEpisode, isPending: isRatingPending } = useRateEpisode();
  const { mutateAsync: deleteEpisodeRate, isPending: isPendingDeleteRate } = useDeleteEpisodeRate();

  const [isShowRateModal, setShowRateModal] = useState(false);
  const [isShowMenuModal, setShowMenuModal] = useState(false);
  const [showDeleteRateModal, setShowDeleteRateModal] = useState(false);

  const handleRateSubmit = async (value: number | null) => {
    if (!value) {
      toastError('Rate the episode by selecting at least a half star');
      return;
    }

    try {
      await rateEpisode({
        episodeId: item.id,
        rate: value,
      });
      toastSuccess({ description: 'Rated episode successfully' });
      setShowRateModal(false);
    } catch (error) {
      toastError(error);
    }
  };

  const handleToggleWatchlist = async () => {
    try {
      const isRestricted = onCheckAccountRestricted();
      if (isRestricted) {
        setShowMenuModal(false);
        return;
      }

      const isInWatchlist = !!item.isWatchlist;

      await toggleWatchlist({
        episodeId: item.id,
        isInWatchlist: isInWatchlist,
      });

      setShowMenuModal(false);

      toastSuccess({
        description: isInWatchlist ? 'Removed from watchlist successfully' : 'Added to watchlist successfully',
      });
    } catch (error) {
      toastError(error);
    }
  };

  const handleToggleWatched = async () => {
    const isWatched = !!item.isWatched;

    try {
      const isRestricted = onCheckAccountRestricted();
      if (isRestricted) {
        setShowMenuModal(false);
        return;
      }

      await toggleWatched({
        episodeId: item.id,
        isWatched: isWatched,
      });
      toastSuccess({ description: isWatched ? 'Unmarked as watched successfully' : 'Marked as watched successfully' });
      setShowMenuModal(false);
    } catch (error) {
      toastError(error);
    }
  };

  const handleDeleteRate = async () => {
    try {
      await deleteEpisodeRate(item.id);
      toastSuccess({ description: 'Deleted rate successfully' });
      setShowDeleteRateModal(false);
    } catch (error) {
      toastError(error);
    }
  };

  const handleAddCommentToThisEpisode = () => {
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    router.push({
      pathname: '/(app)/episode/[episodeId]/add-post',
      params: {
        episodeId: item.id,
      },
    });
  };

  const handleOpenMenu = () => {
    setShowMenuModal(true);
  };

  const handleToggleLike = async () => {
    const isLiked = item.hasLiked;

    try {
      const isRestricted = onCheckAccountRestricted();
      if (isRestricted) return;

      await toggleLike({
        episodeId: item.id,
        isLiked: !isLiked,
      });
    } catch (error) {
      toastError(error);
    }
  };

  const handleOpenRateDialog = () => {
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    setShowRateModal(true);
  };

  const handleOpenDeleteRateDialog = () => {
    const isRestricted = onCheckAccountRestricted();
    if (isRestricted) return;

    setShowDeleteRateModal(true);
  };

  const handleDirect = async () => {
    episodeDetailDirect(queryClient, item.id.toString());
  };

  const avgRate = bigDecimal.round(item?.avgRate, 1, bigDecimal.RoundingModes.HALF_UP);

  return (
    <View>
      <TouchableOpacity activeOpacity={0.7} onPress={handleDirect} style={styles.episodeContainer}>
        <View style={styles.episodeRow}>
          <View style={styles.episodeThumbnail}>
            <ExpoImage source={{ uri: item.imageUrl }} style={styles.thumbnailImage} />
          </View>

          <View style={styles.episodeInfo}>
            <View style={styles.episodeHeader}>
              <ThemedText style={styles.episodeTitle}>
                {item.podcastTitle} • {format(new Date(item.releaseDate), 'MMM dd')}
              </ThemedText>
            </View>

            <ThemedText style={styles.episodeNumber} numberOfLines={2}>
              {item.title}
            </ThemedText>

            <View style={styles.episodeRating}>
              <StarRateFill color='#FFCC00' width={16} height={16} />

              <ThemedText style={styles.ratingText}>
                <ThemedText style={styles.ratedText}>
                  {avgRate}
                  {' • '}
                </ThemedText>
                {formatCompactNumber(Number(item?.rateCount || 0))}
              </ThemedText>
            </View>
          </View>

          <Pressable onPress={handleOpenMenu}>
            <Ionicons style={styles.ellipsisIcon} name='ellipsis-horizontal' size={24} color='#FFFFFF' />
          </Pressable>
        </View>

        <View style={styles.episodeActions}>
          <View style={styles.fullFlex}>
            <ActionButton
              icon={<Ionicons name='chatbox-outline' size={16} color='#FFFFFF' />}
              text='Add Comment'
              onPress={handleAddCommentToThisEpisode}
            />
          </View>

          <View style={styles.actionBoxRight}>
            <LikeShowButton isLiked={item.hasLiked} onPress={handleToggleLike} size='sm' />

            <View style={{ flex: 1.2 }}>
              <RateButton
                isRated={item.hasRated}
                onPress={handleOpenRateDialog}
                onDeleteRate={handleOpenDeleteRateDialog}
                onEditRate={handleOpenRateDialog}
                size='sm'
              />
            </View>
          </View>
        </View>
      </TouchableOpacity>

      <RatingModal
        isVisible={isShowRateModal}
        onClose={() => setShowRateModal(false)}
        onRateSubmit={handleRateSubmit}
        isAddingRate={isRatingPending}
        defaultRate={item.userRate || 0}
        enjoyName={item.title}
        image={item.imageUrl}
      />

      <EpisodeMenuModal
        isVisible={isShowMenuModal}
        onClose={() => setShowMenuModal(false)}
        onAddToWatchlist={handleToggleWatchlist}
        onUnmarkAsWatched={handleToggleWatched}
        isInWatchlist={item?.isWatchlist}
        isMarkedAsWatched={item?.isWatched}
        isPendingWatchlist={isPendingWatchlist}
        isPendingMarkAsWatched={isPendingWatched}
      />

      <DeleteRateModal
        isDeleting={isPendingDeleteRate}
        isVisible={showDeleteRateModal}
        onClose={() => setShowDeleteRateModal(false)}
        onDeleteConfirm={handleDeleteRate}
      />
    </View>
  );
});

const stylesheet = createStyleSheet((theme, rt) => ({
  actionBoxRight: {
    flex: 1.3,
    gap: 8,
    flexDirection: 'row',
  },
  fullFlex: {
    flex: 1,
    flexDirection: 'row',
    height: 38,
  },
  episodeContainer: {
    flexDirection: 'column',
    gap: 16,
  },
  episodeRow: {
    flexDirection: 'row',
  },
  episodeThumbnail: {
    width: 96,
    height: 96,
    borderRadius: 4,
    marginRight: 24,
    overflow: 'hidden',
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
  },
  episodeInfo: {
    flex: 1,
  },
  episodeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  skeletonContainer: {
    width: '100%',
    gap: 12,
    paddingHorizontal: 24,
  },
  episodeTitle: {
    color: '#FFFFFF',
    fontSize: 12,
    lineHeight: 18,
    ...theme.fw500,
    opacity: 0.56,
  },
  episodeNumber: {
    color: '#FFFFFF',
    fontSize: 14,
    lineHeight: 20,
    ...theme.fw500,
    letterSpacing: 0,
    marginBottom: 4,
  },
  episodeRating: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  ratingText: {
    color: '#AAAAAA',
    fontSize: 12,
    marginLeft: 4,
    lineHeight: 18,
    ...theme.fw500,
  },
  ratedText: {
    color: '#FFFFFF',
    fontSize: 12,
    lineHeight: 18,
    ...theme.fw500,
    marginLeft: 4,
  },
  episodeActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8,
    flex: 1,
  },
  separator: {
    height: 1,
    backgroundColor: theme.colors.whiteOpacity10,
    marginTop: 24,
    marginBottom: 12,
  },
  ellipsisIcon: {
    marginLeft: 16,
  },
  loadingFooter: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
    gap: 8,
  },
  loadingText: {
    color: '#AAAAAA',
    fontSize: 14,
    ...theme.fw500,
  },
}));
