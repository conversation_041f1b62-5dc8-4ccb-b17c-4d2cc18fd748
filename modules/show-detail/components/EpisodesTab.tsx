import { useGetEpisodesInfiniteQuery } from '@/apis/episode/queries';
import { Episode } from '@/apis/episode/types';
import { memo, useCallback, useMemo } from 'react';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { EpisodeItem } from './EpisodeItem';
import { IconLoading } from '@/components/IconLoading';
import { useLocalSearchParams } from 'expo-router';
import { TabFlashList } from '@/components/collapsing-tabs/TabFlashList';
import { Divider } from '@/components/Divider';
import { Spacer } from '@/components/Spacer';

const LIMIT_PER_PAGE = 20;
const EpisodesTab = () => {
  const { styles } = useStyles(stylesheet);
  const localParams = useLocalSearchParams();
  const podcastId = localParams['podcastId'] as string;

  const { data, isFetchingNextPage, hasNextPage, fetchNextPage, isFetching } = useGetEpisodesInfiniteQuery({
    podcastId: Number(podcastId),
    limit: LIMIT_PER_PAGE,
  });

  const allEpisodes = useMemo(() => {
    const pages = data?.pages ?? [];
    return pages.flatMap((page) => page.data) || [];
  }, [data?.pages]);

  const handleLoadMore = useCallback(async () => {
    if (hasNextPage && !isFetchingNextPage) {
      await fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const renderEpisodeCallback = useCallback(({ item }: { item: Episode }) => <EpisodeItem item={item} />, []);

  const keyExtractor = useCallback((item: Episode) => item.id.toString(), []);

  const renderItemSeparator = useCallback(() => <Divider style={styles.separator} />, [styles.separator]);

  const renderSkeletonComponent = useCallback(() => {
    return <IconLoading />;
  }, []);

  return (
    <TabFlashList
      ListHeaderComponent={<Spacer height={24} />}
      data={allEpisodes}
      showsVerticalScrollIndicator={false}
      scrollEventThrottle={16}
      style={styles.container}
      contentContainerStyle={styles.episodesContainer}
      renderItem={renderEpisodeCallback}
      keyExtractor={keyExtractor}
      ItemSeparatorComponent={renderItemSeparator}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={5}
      ListFooterComponent={isFetchingNextPage || isFetching ? renderSkeletonComponent : null}
      ListFooterComponentStyle={styles.skeletonContainer}
      estimatedItemSize={150}
    />
  );
};

export default memo(EpisodesTab);

const stylesheet = createStyleSheet((theme) => ({
  container: {},
  episodesContainer: {
    paddingHorizontal: 24,
    paddingBottom: 48,
  },
  skeletonContainer: {
    gap: 12,
  },
  separator: {
    marginVertical: 24,
  },
}));
