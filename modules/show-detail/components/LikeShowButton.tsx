import { Icons } from '@/assets/icons';
import { ThemedText } from '@/components/ThemedText';
import { useScaleFocus } from '@/hooks/useScaleFocus';
import React from 'react';
import { TouchableOpacity, ViewStyle } from 'react-native';
import Animated from 'react-native-reanimated';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type ButtonSize = 'sm' | 'md';

interface LikeShowButtonProps {
  isLiked: boolean;
  onPress: () => void;
  size?: ButtonSize;
}

const LikeShowButton: React.FC<LikeShowButtonProps> = ({ isLiked, onPress, size = 'md' }) => {
  const { styles } = useStyles(stylesheet);
  const { animatedStyle, handlePressIn, handlePressOut } = useScaleFocus(0.95);

  const sizeStyles: Record<ButtonSize, ViewStyle> = {
    md: styles.buttonMd,
    sm: styles.buttonSm,
  };

  return (
    <Animated.View style={[animatedStyle, styles.fullFlex]}>
      <TouchableOpacity
        activeOpacity={0.7}
        onPress={onPress}
        style={[styles.button, sizeStyles[size]]}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
      >
        {isLiked ? (
          <Icons.HeartLikeFill size={16} color={'#FF4287'} />
        ) : (
          <Icons.HeartLikeOutline size={16} color={'#fff'} />
        )}

        <ThemedText type='tinyMedium' style={[styles.buttonText, isLiked ? styles.buttonTextLiked : undefined]}>
          {isLiked ? 'Liked' : 'Like'}
        </ThemedText>
      </TouchableOpacity>
    </Animated.View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  fullFlex: {
    flex: 1,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    borderRadius: 999,
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#D9FF031A',
    height: 38,
    gap: 8,
  },
  buttonMd: {
    paddingVertical: 10,
    paddingHorizontal: 15,
  },
  buttonSm: {
    paddingVertical: 7,
    paddingHorizontal: 16,
    height: 38,
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    color: theme.colors.neutralWhite,
    ...theme.fw600,
    fontSize: 12,
    lineHeight: 16,
  },
  buttonTextLiked: {
    color: '#FF4287',
  },
}));

export default LikeShowButton;
