import { ICommentResponse } from '@/apis/comment/types';
import { Icons } from '@/assets/icons';
import { Show } from '@/components/Show';
import { ThemedText } from '@/components/ThemedText';
import { useScaleFocus } from '@/hooks/useScaleFocus';
import { formatCompactNumber } from '@/utils/func';
import { Ionicons } from '@expo/vector-icons';
import { Pressable, View } from 'react-native';
import Animated from 'react-native-reanimated';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

interface CommentActionsProps {
  comment: Pick<ICommentResponse, 'hasLiked' | 'likeCount' | 'replyCount'> &
    Partial<Pick<ICommentResponse, 'upVotes' | 'downVotes' | 'userVoteDirection'>>;
  onLikePress: () => void;
  onVotePress?: (direction: 'up' | 'down') => void;
}

function CommentActions({ comment, onLikePress, onVotePress }: CommentActionsProps) {
  const { styles, theme } = useStyles(stylesheet);
  const { animatedStyle, handlePressIn, handlePressOut } = useScaleFocus(0.95);

  const handleUpVotePress = () => {
    onVotePress?.('up');
  };

  const handleDownVotePress = () => {
    onVotePress?.('down');
  };

  const isLiked = comment.hasLiked;
  const isUpVoted = comment.userVoteDirection === 'up';
  const isDownVoted = comment.userVoteDirection === 'down';
  const likeColor = isLiked ? '#FF4287' : theme.colors.neutralLightGrey;
  const likeFill = isLiked ? '#FF4287' : 'none';
  const upVoteCircleColor = isUpVoted ? theme.colors.primary : theme.colors.neutralLightGrey;
  const upVoteArrowColor = isUpVoted ? theme.colors.neutralBackground : theme.colors.neutralLightGrey;
  const upVoteFill = isUpVoted ? theme.colors.primary : 'none';
  const downVoteCircleColor = isDownVoted ? theme.colors.primary : theme.colors.neutralLightGrey;
  const downVoteArrowColor = isDownVoted ? theme.colors.neutralBackground : theme.colors.neutralLightGrey;
  const downVoteFill = isDownVoted ? theme.colors.primary : 'none';

  return (
    <View style={styles.actions}>
      <Pressable
        onPress={onLikePress}
        style={styles.actionButton}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
      >
        <Animated.View style={animatedStyle}>
          <Icons.HeartLikeOutline color={likeColor} size={16} fill={likeFill} />
        </Animated.View>

        <ThemedText style={[styles.actionText, isLiked ? styles.likeText : null]}>
          {formatCompactNumber(Number(comment.likeCount || 0))}
        </ThemedText>
      </Pressable>

      <View style={styles.actionButton}>
        <Ionicons name='chatbox-outline' size={16} color={theme.colors.neutralLightGrey} />

        <ThemedText style={styles.actionText}>{formatCompactNumber(Number(comment.replyCount || 0))}</ThemedText>
      </View>

      <Show when={!!onVotePress}>
        <Pressable
          style={styles.actionButton}
          onPress={handleUpVotePress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
        >
          <Icons.ArrowUpCircleIcon
            size={16}
            arrowColor={upVoteArrowColor}
            circleColor={upVoteCircleColor}
            fill={upVoteFill}
          />

          <ThemedText style={[styles.actionText, isUpVoted ? { color: theme.colors.primary } : null]}>
            {formatCompactNumber(Number(comment.upVotes || 0))}
          </ThemedText>
        </Pressable>

        <Pressable
          onPress={handleDownVotePress}
          style={styles.actionButton}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
        >
          <Icons.ArrowUpCircleIcon
            size={16}
            arrowColor={downVoteArrowColor}
            circleColor={downVoteCircleColor}
            fill={downVoteFill}
            style={{ transform: [{ rotate: '180deg' }] }}
          />

          <ThemedText style={[styles.actionText, isDownVoted ? { color: theme.colors.primary } : null]}>
            {formatCompactNumber(Number(comment.downVotes || 0))}
          </ThemedText>
        </Pressable>
      </Show>
    </View>
  );
}

const stylesheet = createStyleSheet((theme) => ({
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    minWidth: 50,
  },
  actionText: {
    color: theme.colors.whiteOpacity56,
    marginLeft: 6,
    fontSize: 12,
    ...theme.fw500,
  },
  likeText: {
    color: '#FF4287',
  },
}));

export default CommentActions;
