import { Icons } from '@/assets/icons';
import { ThemedText } from '@/components/ThemedText';
import React from 'react';
import { TouchableOpacity } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import Animated from 'react-native-reanimated';
import { useScaleFocus } from '@/hooks/useScaleFocus';

interface InteractButtonProps {
  onPress: () => void;
}

const InteractButton: React.FC<InteractButtonProps> = ({ onPress }) => {
  const { theme, styles } = useStyles(stylesheet);
  const { animatedStyle, handlePressIn, handlePressOut } = useScaleFocus(0.95);

  return (
    <Animated.View style={animatedStyle}>
      <TouchableOpacity
        activeOpacity={0.7}
        onPress={onPress}
        style={[styles.button]}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
      >
        <Icons.EditOutline width={16} height={16} color={theme.colors.background} />

        <ThemedText numberOfLines={1} type='tinyMedium' style={[styles.buttonText]}>
          Interact
        </ThemedText>
      </TouchableOpacity>
    </Animated.View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  button: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    borderRadius: 999,
    justifyContent: 'center',
    backgroundColor: theme.colors.primary,
    height: 38,
  },
  buttonText: {
    marginLeft: 8,
    ...theme.fw600,
    color: theme.colors.background,
    lineHeight: 16,
  },
  joinText: {
    color: theme.colors.background,
  },
}));

export default InteractButton;
