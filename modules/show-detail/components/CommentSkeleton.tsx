import { Spacer } from '@/components/Spacer';
import { Skeleton } from 'moti/skeleton';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

export const CommentSkeleton = () => {
  const { styles } = useStyles(stylesheet);

  return (
    <View style={styles.container}>
      <Skeleton width={48} height={48} radius={999} />

      <Spacer width={24} />

      <View style={styles.commentBox}>
        <Skeleton width={200} height={18} />
        <Skeleton width={100} height={24} />
        <Skeleton width={150} height={20} />
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet(() => ({
  container: {
    flexDirection: 'row',
  },
  commentBox: {
    flex: 1,
    gap: 4,
  },
}));
