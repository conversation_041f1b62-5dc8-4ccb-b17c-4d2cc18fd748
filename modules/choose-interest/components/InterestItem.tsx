import { ThemedText } from '@/components/ThemedText';
import { TouchableOpacity } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type InterestItemProps = {
  label: string;
  isSelected: boolean;
  onToggle: () => void;
};

export const InterestItem = ({ label, isSelected, onToggle }: InterestItemProps) => {
  const { styles } = useStyles(stylesheet);

  return (
    <TouchableOpacity style={[styles.container, isSelected && styles.selected]} onPress={onToggle} activeOpacity={0.7}>
      <ThemedText style={[styles.label, isSelected && styles.selectedLabel]}>{label}</ThemedText>
    </TouchableOpacity>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 25,
    backgroundColor: theme.colors.neutralCard,
    borderWidth: 1,
    borderColor: theme.colors.neutralGrey,
  },
  selected: {
    backgroundColor: theme.colors.primary,
    borderColor: '#0E100F4D',
  },
  label: {
    color: theme.colors.neutralWhite,
    fontSize: 16,
    ...theme.fw500,
  },
  selectedLabel: {
    color: '#000',
  },
}));
