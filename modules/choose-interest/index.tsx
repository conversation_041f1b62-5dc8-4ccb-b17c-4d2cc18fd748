import { useGetProfileQuery } from '@/apis/auth/queries';
import { ICategoryResponse, useAddCategoriesToFavoritesMutation } from '@/apis/category';
import { useGetAllCategoriesQuery } from '@/apis/category/queries';
import { planPaymentMutation } from '@/apis/user';
import { ThemedText } from '@/components/ThemedText';
import { Button } from '@/components/ui/Button';
import { Header } from '@/components/ui/Header';
import { toastError } from '@/utils/toast';
import { useQueryClient } from '@tanstack/react-query';
import { router } from 'expo-router';
import { useState } from 'react';
import { ScrollView, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { InterestItem } from './components/InterestItem';
import queryKeys from '@/utils/queryKeys';

export const ChooseInterestModule = () => {
  const { styles } = useStyles(stylesheet);

  const queryClient = useQueryClient();
  const { data: allCategories } = useGetAllCategoriesQuery();
  const [selectedInterests, setSelectedInterests] = useState<ICategoryResponse[]>([]);
  const { mutateAsync: addCategories, isPending: isPendingAddCategories } = useAddCategoriesToFavoritesMutation();
  const { mutateAsync: chooseInterest, isPending: isPendingChooseInterest } = planPaymentMutation();
  const { data: userProfile, isRefetching: isRefetchingProfile } = useGetProfileQuery();

  const handleToggleInterest = (interest: ICategoryResponse) => {
    setSelectedInterests((prev) =>
      prev.includes(interest) ? prev.filter((item) => item.id !== interest.id) : [...prev, interest]
    );
  };

  const refetchProfile = async () => {
    await Promise.all([
      queryClient.refetchQueries({ queryKey: queryKeys.auth.profile() }),
      queryClient.refetchQueries({ queryKey: queryKeys.userProfile.byUserId(userProfile?.id || '') }),
    ]);
  };

  const handleContinue = async () => {
    try {
      await addCategories({ categoryIds: selectedInterests.map((item) => Number(item.id)) });

      await chooseInterest({
        status: 'choose_podcast',
      });

      await refetchProfile();

      router.replace('/(app)/choose-podcast');
    } catch (error) {
      toastError(error);
    }
  };

  const handleSkip = async () => {
    await chooseInterest({
      status: 'choose_podcast',
    });

    await refetchProfile();

    router.replace('/(app)/choose-podcast');
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Header
          title='Choose Your Interest'
          rightAction={
            <TouchableOpacity onPress={handleSkip}>
              <ThemedText style={styles.skipButton}>Skip</ThemedText>
            </TouchableOpacity>
          }
        />
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.interestsContainer}
        showsVerticalScrollIndicator={false}
      >
        <ThemedText style={styles.description}>
          Select your interests and receive podcasts that fit you. Feel free to choose more than one!
        </ThemedText>
        <View style={styles.interestsWrapper}>
          {allCategories?.map((category) => (
            <InterestItem
              key={category.id}
              label={category.name}
              isSelected={selectedInterests.some((item) => item.name === category.name)}
              onToggle={() => handleToggleInterest(category)}
            />
          ))}
        </View>
      </ScrollView>

      <View style={styles.buttonContainer}>
        <Button
          isLoading={isPendingAddCategories || isPendingChooseInterest || isRefetchingProfile}
          onPress={handleContinue}
          disabled={selectedInterests.length === 0}
        >
          Continue
        </Button>
      </View>
    </SafeAreaView>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
    backgroundColor: '#000000',
    position: 'relative',
  },
  header: {
    paddingTop: 20,
    paddingHorizontal: 24,
  },
  skipButton: {
    color: theme.colors.primary,
  },
  skipText: {
    color: '#daff00',
    fontSize: 14,
    ...theme.fw600,
  },
  description: {
    color: theme.colors.neutralWhite,
    fontSize: 16,
    marginTop: 28,
    marginBottom: 32,
    opacity: 0.64,
    lineHeight: 25.6,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 24,
  },
  interestsContainer: {
    paddingBottom: 24,
  },
  interestsWrapper: {
    gap: 20,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  buttonContainer: {
    position: 'static',
    paddingTop: 44,
    paddingHorizontal: 59,
    backgroundColor: '#0E100FBF',
    paddingBottom: 24,
  },
}));
