import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  icon: React.ReactNode;
  title: string;
};

export const Benefit = (props: Props) => {
  const { styles } = useStyles(stylesheet);

  return (
    <View style={styles.container}>
      {props.icon}

      <Spacer width={16} />

      <ThemedText type='smallNormal'>{props.title}</ThemedText>
    </View>
  );
};

const stylesheet = createStyleSheet(() => ({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
}));
