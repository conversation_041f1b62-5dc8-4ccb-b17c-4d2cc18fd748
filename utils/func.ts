import { format } from 'date-fns';
import { UnistylesRuntime } from 'react-native-unistyles';

export const formatCompactNumber = (number: number) => {
  if (number < 1e3) {
    return number.toString();
  } else if (number < 1e6) {
    return Math.floor(number / 1e2) / 10 + 'K'; // Formats thousands
  } else if (number < 1e9) {
    return Math.floor(number / 1e5) / 10 + 'M'; // Formats millions
  } else {
    return Math.floor(number / 1e8) / 10 + 'B'; // Formats billions
  }
};

export const getItemSizeFlatList = (containerPadding: number, numColumns: number, gap: number) => {
  const screenWidth = UnistylesRuntime.screen.width;
  const availableSpace = screenWidth - 2 * containerPadding - (numColumns - 1) * gap;

  return availableSpace / numColumns;
};

export const formatIntervalToNowDuration = (time: string) => {
  const now = new Date();
  const start = new Date(time);
  const diffInSeconds = Math.floor((now.getTime() - start.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return `${diffInSeconds}S`;
  }

  if (diffInSeconds < 3600) {
    return `${Math.floor(diffInSeconds / 60)}M`;
  }

  if (diffInSeconds < 86400) {
    return `${Math.floor(diffInSeconds / 3600)}H`;
  }

  if (diffInSeconds < 604800) {
    return `${Math.floor(diffInSeconds / 86400)}D`;
  }

  if (diffInSeconds < 2419200) {
    return `${Math.floor(diffInSeconds / 604800)}W`;
  }

  return format(new Date(time), 'dd MMM, yyyy'); // Output example: 01 Jan, 2023
};

export const durationTime = (seconds: number) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  if (hours === 0) {
    return `${minutes} ${minutes > 1 ? 'Mins' : 'Min'}`;
  } else if (hours === 1 && minutes === 0) {
    return `1 Hr`;
  } else {
    return `${hours} ${hours > 1 ? 'Hrs' : 'Hr'} ${minutes} ${minutes > 1 ? 'Mins' : 'Min'}`;
  }
};

export const checkIsOwner = (currentUserId?: string | number, targetUserId?: string | number): boolean => {
  if (!currentUserId || !targetUserId) return false;
  return currentUserId.toString() === targetUserId.toString();
};

export const getUserIdForFavorites = (
  routerUserId?: string | number,
  profileUserId?: string | number
): string | undefined => {
  return routerUserId?.toString() || profileUserId?.toString();
};

export const getWindowDynamic = (itemLength = 0) => {
  const window = Math.ceil(itemLength > 50 ? itemLength / 4 : 21);
  return window;
};

export const truncateHeader = (title: string, max = 15) => {
  const lastIndexOfSpacce = title.lastIndexOf(' ');
  if (lastIndexOfSpacce === -1) {
    return truncateNumberCharacter(title, max);
  }
  const first = truncateNumberCharacter(title.slice(0, lastIndexOfSpacce));
  const last = title.slice(lastIndexOfSpacce + 1);
  return `${first}${last ? ` ${last}` : ''}`;
};

export const truncateNumberCharacter = (charaters: string, max: number = 10) => {
  if (charaters.length > max) {
    return charaters.slice(0, max) + '...';
  }
  return charaters;
};
