/**
 * Converts a date string from DD/MM/YYYY format to YYYY/MM/DD format
 * @param dateString The date string in DD/MM/YYYY format
 * @returns The date string in YYYY/MM/DD format or empty string if invalid
 */
export const convertDateFormat = (dateString: string): string => {
  if (!dateString) return '';

  // Check if the date string matches the expected format
  const dateRegex = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/;
  const match = dateString.match(dateRegex);

  if (!match) return '';

  const month = match[1].padStart(2, '0');
  const day = match[2].padStart(2, '0');
  const year = match[3];

  return `${year}-${month}-${day}`;
};

/**
 * Validates if a string is in DD/MM/YYYY format
 * @param dateString The date string to validate
 * @returns True if the date string is valid, false otherwise
 */
export const isValidDateFormat = (dateString: string): boolean => {
  if (!dateString) return false;

  const dateRegex = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/;
  const match = dateString.match(dateRegex);

  if (!match) return false;

  const day = parseInt(match[1], 10);
  const month = parseInt(match[2], 10);
  const year = parseInt(match[3], 10);

  // Basic validation
  if (month < 1 || month > 12) return false;
  if (day < 1 || day > 31) return false;
  if (year < 1900 || year > new Date().getFullYear()) return false;

  // Check days in month
  if ([4, 6, 9, 11].includes(month) && day > 30) return false;
  if (month === 2) {
    // Leap year check
    const isLeapYear = (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0;
    if (day > (isLeapYear ? 29 : 28)) return false;
  }

  return true;
};

/**
 * Converts a date or timestamp to a relative time string (e.g., "2d", "3w", or "15 Jan, 2023")
 * @param date Date string or timestamp to convert
 * @returns Formatted relative time string
 */
export const getRelativeTimeString = (date: string | number | Date): string => {
  const currentDate = new Date();
  const inputDate = new Date(date);

  const diffInSeconds = Math.floor((currentDate.getTime() - inputDate.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return `${diffInSeconds}S`;
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes}M`;
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours}H`;
  }

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) {
    return `${diffInDays}D`;
  }

  const diffInWeeks = Math.floor(diffInDays / 7);
  if (diffInWeeks < 4) {
    return `${diffInWeeks}W`;
  }

  // For posts older than 4 weeks, show exact date in "DD MMM, YYYY" format
  const day = inputDate.getDate();
  const month = inputDate.toLocaleString('en', { month: 'short' });
  const year = inputDate.getFullYear();

  return `${day} ${month}, ${year}`;
};
