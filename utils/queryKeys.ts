const queryKeys = {
  categories: {
    categories: (...keys: any[]) => ['categories'].concat(...keys),
    categoriesInfinite: (...keys: any[]) => ['categories-infinite'].concat(...keys),
    userCategories: () => ['my-categories'],
  },
  podcasts: {
    topShows: (...keys: any[]) => ['top-shows'].concat(...keys),
    podcasts: (...keys: any[]) => ['podcasts'].concat(...keys),
    podcastsInfinite: (...keys: any[]) => ['podcasts-infinite'].concat(...keys),
    podcastsInfiniteSearch: (...keys: any[]) => ['podcasts-infinite-search'].concat(...keys),
    item: (...keys: any[]) => ['podcasts-detail'].concat(...keys),
    newestEpisodes: (...keys: any[]) => ['newest-episodes'].concat(...keys),
    communityFeed: (...keys: any[]) => ['community-feed'].concat(...keys),
  },
  comments: {
    list: (...keys: any[]) => ['comments'].concat(...keys),
    item: (...keys: any[]) => ['comment'].concat(...keys),
    itemEpisode: (...keys: any[]) => ['comment-episode'].concat(...keys),
    listEpisode: (...keys: any[]) => ['comments-episode'].concat(...keys),
  },
  commentsReply: {
    list: (...keys: any[]) => ['reply-comments'].concat(...keys),
    item: (...keys: any[]) => ['reply-comment'].concat(...keys),
    listEpisode: (...keys: any[]) => ['reply-comments-episode'].concat(...keys),
    itemEpisode: (...keys: any[]) => ['reply-comment-episode'].concat(...keys),
  },
  rates: {
    episodeRateList: (...keys: any[]) => ['episodeRateList'].concat(...keys),
    episodeRateStats: (...keys: any[]) => ['episodeRateStats'].concat(...keys),
    list: (...keys: any[]) => ['podcast-rate'].concat(...keys),
    stat: (...keys: any[]) => ['podcast-rate-stat'].concat(...keys),
    byPodcastId: (...keys: any[]) => ['rates'].concat(...keys),
  },
  markAsWatched: {
    byPodcastId: (...keys: any[]) => ['mark-as-watch'].concat(...keys),
    getEpisodeMarkAsWatchedRequest: (...keys: any[]) => ['getEpisodeMarkAsWatchedRequest'].concat(...keys),
  },
  watchlist: {
    byPodcastId: (...keys: any[]) => ['watchlist'].concat(...keys),
    getEpisodeWatchlistRequest: (...keys: any[]) => ['getEpisodeWatchlistRequest'].concat(...keys),
  },
  episodes: {
    getEpisodesListInfiniteRequest: (...keys: any[]) => ['getEpisodesListInfiniteRequest'].concat(...keys),
    getEpisodeByIdRequest: (...keys: any[]) => ['getEpisodeByIdRequest'].concat(...keys),
    getEpisodeRateByIdRequest: (...keys: any[]) => ['getEpisodeRateByIdRequest'].concat(...keys),
  },
  favorites: {
    podcastsInfinite: (...keys: any[]) => ['getFavoritePodcastsRequestInfinite'].concat(...keys),
    episodesInfinite: (...keys: any[]) => ['getFavoriteEpisodesRequestInfinite'].concat(...keys),
  },
  recentSearch: {
    list: (...keys: any[]) => ['recent-search'].concat(...keys),
  },
  userProfile: {
    byUserId: (...keys: any[]) => ['user-profile', keys],
    postUserHistoryInfinite: (...keys: any[]) => ['user-post-history', keys],
    replyUserHistoryInfinite: (...keys: any[]) => ['user-reply-history', keys],
    getUserLikesPodcastsHistoryInfinite: (...keys: any[]) => ['user-likes-podcast-history', keys],
    getUserLikesEpisodesHistoryInfinite: (...keys: any[]) => ['user-likes-episode-history', keys],
    getUserLikesPostsHistoryInfinite: (...keys: any[]) => ['user-likes-post-history', keys],
    getUserRateStatsReview: (...keys: any[]) => ['user-rate-stats-review', keys],
    getUserRatesReviewInfinite: (...keys: any[]) => ['user-rates-review', keys],
    getUserWatchlistHistoryInfinite: (...keys: any[]) => ['user-watchlist-history', keys],
    getUserWatchedHistoryInfinite: (...keys: any[]) => ['user-watched-history', keys],
    identities: (...keys: any[]) => ['user-identities', keys],
    getSubscriptionStatus: (...keys: any[]) => ['user-subscription-status', keys],
    getUserUpVoteHistoryInfinite: (...keys: any[]) => ['user-upvote-history', keys],
  },
  follow: {
    followersList: (...keys: any[]) => ['getFollowersRequest'].concat(...keys),
    followingList: (...keys: any[]) => ['getFollowingRequest'].concat(...keys),
    followersInfinite: (...keys: any[]) => ['getFollowersRequestInfinite'].concat(...keys),
    followingInfinite: (...keys: any[]) => ['getFollowingRequestInfinite'].concat(...keys),
  },
  auth: {
    profile: (...keys: any[]) => ['my-profile'].concat(...keys),
  },
  activities: {
    myActivitiesInfinite: (...keys: any[]) => ['my-activities-infinite'].concat(...keys),
    followingActivitiesInfinite: (...keys: any[]) => ['following-activities-infinite'].concat(...keys),
  },
  banner: {
    item: (...keys: any[]) => ['banner'].concat(...keys),
  },
  referral: {
    friends: (...keys: any[]) => ['referral-friends'].concat(...keys),
  },
};

export default queryKeys;
