diff --git a/src/Container.tsx b/src/Container.tsx
index e782b903b0220ec0114c62e268e7efb635e4be43..eb7f0c82586fd1e13bbc68c19afdcc42aa06b9bb 100644
--- a/src/Container.tsx
+++ b/src/Container.tsx
@@ -229,7 +229,10 @@ export const Container = React.memo(
       const toggleSyncScrollFrame = (toggle: boolean) =>
         syncScrollFrame.setActive(toggle)
       const syncScrollFrame = useFrameCallback(({ timeSinceFirstFrame }) => {
-        syncCurrentTabScrollPosition()
+        // syncCurrentTabScrollPosition()
+        if (timeSinceFirstFrame % 100 === 0) {
+          syncCurrentTabScrollPosition()
+        }
         if (timeSinceFirstFrame > 1500) {
           runOnJS(toggleSyncScrollFrame)(false)
         }
