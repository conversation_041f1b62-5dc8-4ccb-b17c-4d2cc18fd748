import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size: number;
};

function Gift({ size, color = '#fff', ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 28 28' fill='none' {...props}>
      <Path
        d='M14 9.042V7.097m0 1.945h-1.944a3.889 3.889 0 01-3.89-3.89c0-1.073.871-1.944 1.945-1.944A3.889 3.889 0 0114 7.098m0 1.944h1.944a3.889 3.889 0 003.89-3.89c0-1.073-.871-1.944-1.945-1.944A3.889 3.889 0 0014 7.098m0 1.944H6.708a2.333 2.333 0 00-2.333 2.333v.292c0 .863.47 1.617 1.167 2.02M14 9.043h7.292a2.333 2.333 0 012.333 2.333v.292c0 .863-.47 1.617-1.167 2.02M14 9.043V14m-8.458-.312v7.604a2.333 2.333 0 002.333 2.333H14m-8.458-9.937c.343.198.741.312 1.166.312H14m-8.458-.312v-.271m16.916.27v7.605a2.333 2.333 0 01-2.333 2.333H14m8.458-9.937a2.322 2.322 0 01-1.166.312H14m8.458-.312v-.271M14 14v9.625'
        stroke={color}
        strokeWidth={1.5}
        strokeLinejoin='round'
      />
    </Svg>
  );
}

export default Gift;
