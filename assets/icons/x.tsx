import Svg, { Path, Rect, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size: number;
};

function XIcon({ color = '#fff', size, ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 36 36' fill='none' {...props}>
      <Rect x={0.32184} y={0.32184} width={35.3563} height={35.3563} rx={17.6782} fill='#000' />
      <Rect
        x={0.32184}
        y={0.32184}
        width={35.3563}
        height={35.3563}
        rx={17.6782}
        stroke='#434343'
        strokeWidth={0.643679}
      />
      <Path
        d='M22.217 11.269h2.249l-4.914 5.616 5.78 7.641h-4.525l-3.545-4.634-4.056 4.634h-2.25l5.255-6.006-5.545-7.251h4.64l3.205 4.236 3.706-4.236zm-.79 11.911h1.247L14.63 12.544h-1.338l8.136 10.636z'
        fill={color}
      />
    </Svg>
  );
}

export default XIcon;
