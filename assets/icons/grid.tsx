import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size: number;
};

function GridIcon({ size, color = '#fff', ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 24 24' fill='none' {...props}>
      <Path
        d='M3 6.75c0-1.768 0-2.652.55-3.2C4.097 3 4.981 3 6.75 3c1.769 0 2.652 0 3.2.55.55.548.55 1.432.55 3.2 0 1.768 0 2.652-.55 3.2-.548.55-1.432.55-3.2.55-1.768 0-2.652 0-3.2-.55C3 9.403 3 8.519 3 6.75zm0 10.507c0-1.768 0-2.652.55-3.2.548-.55 1.432-.55 3.2-.55 1.768 0 2.652 0 3.2.55.55.548.55 1.432.55 3.2 0 1.768 0 2.652-.55 3.2-.548.55-1.432.55-3.2.55-1.768 0-2.652 0-3.2-.55C3 19.91 3 19.025 3 17.257zM13.5 6.75c0-1.768 0-2.652.55-3.2.548-.55 1.432-.55 3.2-.55 1.768 0 2.652 0 3.2.55.55.548.55 1.432.55 3.2 0 1.768 0 2.652-.55 3.2-.548.55-1.432.55-3.2.55-1.768 0-2.652 0-3.2-.55-.55-.548-.55-1.432-.55-3.2zm0 10.507c0-1.768 0-2.652.55-3.2.548-.55 1.432-.55 3.2-.55 1.768 0 2.652 0 3.2.55.55.548.55 1.432.55 3.2 0 1.768 0 2.652-.55 3.2-.548.55-1.432.55-3.2.55-1.768 0-2.652 0-3.2-.55-.55-.548-.55-1.432-.55-3.2z'
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </Svg>
  );
}

export default GridIcon;
