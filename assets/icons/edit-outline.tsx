import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
};

function EditOutline({ color = '#fff', ...props }: Props) {
  return (
    <Svg width='20' height='20' viewBox='0 0 20 20' fill='none' {...props}>
      <Path d='M11.457 17.0355H17.5009' stroke={color} strokeWidth='1.5' strokeLinecap='round' strokeLinejoin='round' />
      <Path
        fill-rule='evenodd'
        clip-rule='evenodd'
        d='M10.65 3.16233C11.2964 2.38982 12.4583 2.27655 13.2469 2.90978C13.2905 2.94413 14.6912 4.03232 14.6912 4.03232C15.5575 4.55599 15.8266 5.66925 15.2912 6.51882C15.2627 6.56432 7.34329 16.4704 7.34329 16.4704C7.07981 16.7991 6.67986 16.9931 6.25242 16.9978L3.21961 17.0358L2.53628 14.1436C2.44055 13.7369 2.53628 13.3098 2.79975 12.9811L10.65 3.16233Z'
        stroke={color}
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <Path
        d='M9.18359 5.00098L13.7271 8.49025'
        stroke={color}
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </Svg>
  );
}

export default EditOutline;
