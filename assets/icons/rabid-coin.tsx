import Svg, { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size: number;
};

function RabidCoin({ size, color = '#D9FF03', ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 24 24' fill='none' {...props}>
      <G clipPath='url(#clip0_4653_25312)' fill={color}>
        <Path d='M17.249 3.295c4.279 2.372 4.95 7.515 1.496 11.487-3.452 3.972-9.72 5.268-14 2.896-4.279-2.372-4.95-7.514-1.497-11.486 3.453-3.972 9.721-5.269 14-2.897zm-1.736 2.793c-1.777-2.598-2.58 1.507-2.4 3.113-.218-.896-1.191-1.904-2.528-2.21-1.406-.32-1.3.878-.134 2.43-1.948-.413-3.932.211-4.525 1.46-.615 1.299.502 2.786 2.5 3.344-.33.24-.728.43-1.062.59-.25.118-.463.22-.586.314-.255.194.025.327.417.368.232.024.494.006.733-.025l.402-.06c1.967-.327 3.24-1.083 3.955-1.525.122-.076.19-.119.318-.207.702-.483 1.55-1.28 2.233-2.237 1.104-1.642 1.415-3.396.946-4.805a2.5 2.5 0 00-.269-.55zm-6.969 4.77c.416-.246.905-.261 1.092-.***************.606-.414.851-.416.245-.905.261-1.092.036-.187-.225-.001-.606.414-.851zm1.508.835c.416-.245.905-.26 1.092-.036.186.226 0 .607-.415.852-.416.245-.905.26-1.092.036-.187-.226 0-.607.415-.852z' />
        <Path d='M20.214 6.716c2.886 2.634 3 6.966 0 10.416-3.452 3.972-9.72 5.27-14 2.898-1.232-.684-2.163-1.598-2.784-2.647.437.4.936.762 1.5 1.074 4.279 2.372 10.546 1.076 13.999-2.895 2.46-2.83 2.825-6.253 1.285-8.846z' />
      </G>
      <Defs>
        <ClipPath id='clip0_4653_25312'>
          <Path fill='#fff' d='M0 0H24V24H0z' />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default RabidCoin;
