import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
};

function Rabid({ width = 28, height = 48, color = '#D9FF03', ...props }: Props) {
  return (
    <Svg width={width} height={height} viewBox='0 0 28 48' fill='none' {...props}>
      <Path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M27.13 20.119c.424 4.424-.018 8.621-.826 11.51-.148.531-.23.796-.392 1.27-1.008 2.967-2.943 8.277-8.515 13.255-.048.043.053-.044 0 0-.606.54-1.31 1.083-2.032 1.43-1.221.585-2.353.654-2.106-.453.119-.536.477-1.267.896-2.12.559-1.14 1.225-2.5 1.57-3.902-7.096 1.82-13.983-1.056-15.449-6.49-1.41-5.228 2.71-10.939 9.304-13.144C2.39 18.47-.886 14.698 3.927 13.177c4.577-1.447 9.86.11 12.71 2.676-4.505-4.99-12.406-20.069-.873-14.722.713.351 1.465.804 2.133 1.328 4.84 3.806 8.309 10.192 9.233 17.66zM15.011 33.316c1.094.402 2.45-.55 3.03-2.126.58-1.576.163-3.18-.93-3.583-1.095-.403-2.451.549-3.03 2.125-.58 1.576-.163 3.18.93 3.584zM11.66 31.19c-.58 1.577-1.937 2.528-3.03 2.126-1.094-.403-1.511-2.008-.931-3.584.58-1.576 1.936-2.528 3.03-2.125 1.093.403 1.51 2.007.93 3.584z'
        fill={color}
      />
    </Svg>
  );
}

export default Rabid;
