import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size: number;
};

function Setting({ size = 24, color = '#fff', ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 24 24' fill='none' {...props}>
      <Path
        d='M11.002 3.325a2 2 0 011.996 0l6.25 3.598a2 2 0 011.002 1.733v6.688a2 2 0 01-1.002 1.733l-6.25 3.598a2 2 0 01-1.996 0l-6.25-3.598a2 2 0 01-1.002-1.733V8.656a2 2 0 011.002-1.733l6.25-3.598z'
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap='square'
      />
      <Path
        d='M15.25 12a3.25 3.25 0 11-6.5 0 3.25 3.25 0 016.5 0z'
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap='square'
      />
    </Svg>
  );
}

export default Setting;
