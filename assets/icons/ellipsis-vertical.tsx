import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size: number;
};

function EllipsisVertical({ size, color = '#fff', ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 20 20' fill='none' {...props}>
      <Path
        d='M8.75 4.792a1.458 1.458 0 112.917 0 1.458 1.458 0 01-2.917 0zM8.75 15.208a1.458 1.458 0 112.917 0 1.458 1.458 0 01-2.917 0zM8.75 9.917a1.458 1.458 0 112.917 0V10a1.458 1.458 0 01-2.917 0v-.083z'
        fill={color}
      />
    </Svg>
  );
}

export default EllipsisVertical;
