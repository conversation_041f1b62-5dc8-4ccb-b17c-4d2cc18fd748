import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size: number;
};

function History({ size, color = '#fff', ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 24 24' fill='none' {...props}>
      <Path
        d='M12 7.75V12l3.5 3.5M2.75 4.75v4h4'
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <Path
        d='M3.25 15.083a9.267 9.267 0 008.736 6.167c5.117 0 9.264-4.141 9.264-9.25s-4.147-9.25-9.264-9.25A9.266 9.266 0 003.45 8.403'
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </Svg>
  );
}

export default History;
