import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size: number;
};

function BookmarkOutline({ size, color = '#fff', ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 24 24' fill='none' {...props}>
      <Path
        d='M19.25 20.252V4.75a2 2 0 00-2-2H6.75a2 2 0 00-2 2v15.502a1 1 0 001.558.829l4.575-3.08a2 2 0 012.234 0l4.575 3.08a1 1 0 001.558-.83z'
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </Svg>
  );
}

export default BookmarkOutline;
