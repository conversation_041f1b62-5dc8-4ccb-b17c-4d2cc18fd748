import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size: number;
};

function Copy({ size, color = '#D9FF03', ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 24 24' fill='none' {...props}>
      <Path
        d='M18.328 7.286h-8.044A1.93 1.93 0 008.36 9.224v10.088c0 1.07.862 1.938 1.925 1.938h8.044a1.929 1.929 0 001.925-1.938V9.224c0-1.07-.862-1.938-1.925-1.938z'
        stroke={color}
        strokeWidth={2}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <Path
        d='M15.644 7.286V4.688a1.943 1.943 0 00-1.188-1.79 1.92 1.92 0 00-.737-.148H5.675c-.51 0-1 .204-1.36.568a1.95 1.95 0 00-.565 1.37v10.088c0 .514.203 1.007.564 1.37.361.363.85.568 1.361.568H8.36'
        stroke={color}
        strokeWidth={2}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </Svg>
  );
}

export default Copy;
