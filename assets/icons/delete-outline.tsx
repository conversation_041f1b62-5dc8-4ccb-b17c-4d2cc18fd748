import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
};

function DeleteOutline({ color = '#fff', ...props }: Props) {
  return (
    <Svg width='21' height='20' viewBox='0 0 21 20' fill='none' {...props}>
      <Path
        d='M16.6032 7.89062C16.6032 7.89062 16.1507 13.5031 15.8882 15.8673C15.7632 16.9965 15.0657 17.6581 13.9232 17.679C11.749 17.7181 9.57234 17.7206 7.39901 17.6748C6.29984 17.6523 5.61401 16.9823 5.49151 15.8731C5.22734 13.4881 4.77734 7.89062 4.77734 7.89062'
        stroke={color}
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <Path d='M17.7567 5.19987H3.625' stroke={color} strokeWidth='1.5' strokeLinecap='round' strokeLinejoin='round' />
      <Path
        d='M15.0335 5.20047C14.3793 5.20047 13.816 4.73797 13.6877 4.09714L13.4852 3.0838C13.3602 2.6163 12.9368 2.29297 12.4543 2.29297H8.92682C8.44432 2.29297 8.02099 2.6163 7.89599 3.0838L7.69349 4.09714C7.56516 4.73797 7.00182 5.20047 6.34766 5.20047'
        stroke={color}
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </Svg>
  );
}

export default DeleteOutline;
