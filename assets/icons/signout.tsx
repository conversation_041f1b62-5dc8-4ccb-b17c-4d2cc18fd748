import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size: number;
};

function Signout({ size = 25, color = '#fff', ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 -0.5 25 25' fill='none' {...props}>
      <Path
        d='M7.044 9.532a.75.75 0 00-1.058-1.064l1.058 1.064zm-4.073 1.936a.75.75 0 001.058 1.064l-1.058-1.064zm1.058 0a.75.75 0 00-1.058 1.064l1.058-1.064zm1.957 4.064a.75.75 0 001.058-1.064l-1.058 1.064zM3.5 11.25a.75.75 0 000 1.5v-1.5zm14 1.5a.75.75 0 000-1.5v1.5zM5.986 8.468l-3.015 3 1.058 1.064 3.015-3-1.058-1.064zm-3.015 4.064l3.015 3 1.058-1.064-3.015-3-1.058 1.064zm.529.218h14v-1.5h-14v1.5z'
        fill={color}
      />
      <Path
        d='M9.5 15a4 4 0 004 4h4a4 4 0 004-4V9a4 4 0 00-4-4h-4a4 4 0 00-4 4'
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </Svg>
  );
}

export default Signout;
