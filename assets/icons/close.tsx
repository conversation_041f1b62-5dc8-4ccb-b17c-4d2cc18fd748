import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size: number;
};

function Close({ size, color = '#fff', ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 24 24' fill='none' {...props}>
      <Path d='M4.75 4.75l14.5 14.5m0-14.5l-14.5 14.5' stroke={color} strokeWidth={1.5} strokeLinecap='round' />
    </Svg>
  );
}

export default Close;
