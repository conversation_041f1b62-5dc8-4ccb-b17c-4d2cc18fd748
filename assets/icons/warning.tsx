import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size: number;
};

function Warning({ color = '#D9FF03', size = 20, ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 20 20' fill='none' {...props}>
      <Path
        d='M17.54 15.5891L10.8826 3.22578C10.4107 2.34922 9.15368 2.34922 8.68142 3.22578L2.02439 15.5891C1.92193 15.7794 1.87056 15.993 1.8753 16.209C1.88004 16.4251 1.94071 16.6363 2.05141 16.8219C2.16211 17.0075 2.31904 17.1613 2.50689 17.2681C2.69474 17.375 2.90709 17.4313 3.12321 17.4316H16.4392C16.6555 17.4317 16.8681 17.3756 17.0562 17.2688C17.2444 17.1621 17.4016 17.0084 17.5125 16.8227C17.6234 16.637 17.6843 16.4258 17.6891 16.2095C17.6939 15.9933 17.6425 15.7795 17.54 15.5891ZM9.7822 15.5176C9.62768 15.5176 9.47664 15.4718 9.34816 15.3859C9.21968 15.3001 9.11955 15.1781 9.06042 15.0353C9.00129 14.8925 8.98582 14.7355 9.01596 14.5839C9.04611 14.4324 9.12051 14.2932 9.22977 14.1839C9.33903 14.0746 9.47824 14.0002 9.62978 13.9701C9.78133 13.9399 9.93842 13.9554 10.0812 14.0145C10.2239 14.0737 10.3459 14.1738 10.4318 14.3023C10.5176 14.4308 10.5634 14.5818 10.5634 14.7363C10.5634 14.9435 10.4811 15.1422 10.3346 15.2888C10.1881 15.4353 9.9894 15.5176 9.7822 15.5176ZM10.6306 7.66016L10.4064 12.4258C10.4064 12.5915 10.3406 12.7505 10.2234 12.8677C10.1061 12.9849 9.94718 13.0508 9.78142 13.0508C9.61566 13.0508 9.45669 12.9849 9.33948 12.8677C9.22227 12.7505 9.15642 12.5915 9.15642 12.4258L8.9322 7.66211C8.92716 7.54828 8.94509 7.4346 8.98492 7.32784C9.02475 7.22109 9.08567 7.12344 9.16404 7.04073C9.24242 6.95802 9.33664 6.89194 9.4411 6.84642C9.54555 6.8009 9.6581 6.77688 9.77204 6.77578H9.78025C9.89496 6.77572 10.0085 6.79891 10.114 6.84393C10.2195 6.88896 10.3148 6.95489 10.3941 7.03776C10.4735 7.12063 10.5352 7.21871 10.5756 7.32608C10.6159 7.43346 10.6341 7.5479 10.6291 7.6625L10.6306 7.66016Z'
        fill={color}
      />
    </Svg>
  );
}

export default Warning;
