import Svg, { ClipP<PERSON>, Defs, G, Path, Rect, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  size: number;
};

function FacebookIcon({ size, ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 36 36' fill='none' {...props}>
      <Rect width={size} height={size} rx={18} fill='#0866FF' />
      <G clipPath='url(#clip0_4793_46231)'>
        <G clipPath='url(#clip1_4793_46231)'>
          <Path d='M18.66 25.392a7.724 7.724 0 10-2.802-.171l.15-.63h2.36l.292.801z' fill='#fff' />
          <Path
            d='M15.858 25.221v-5.136h-1.594v-2.361h1.594v-1.017c0-2.63 1.19-3.847 3.77-3.847.488 0 1.33.096 1.677.192v2.138c-.182-.018-.5-.028-.893-.028-1.267 0-1.755.479-1.755 1.727v.835h2.524l-.432 2.36H18.66v5.308a7.8 7.8 0 01-2.802-.17z'
            fill='#0866FF'
          />
        </G>
      </G>
      <Defs>
        <ClipPath id='clip0_4793_46231'>
          <Path fill='#fff' transform='translate(10 10)' d='M0 0H15.4483V15.4483H0z' />
        </ClipPath>
        <ClipPath id='clip1_4793_46231'>
          <Path fill='#fff' transform='translate(10 10)' d='M0 0H15.4483V15.4483H0z' />
        </ClipPath>
      </Defs>
    </Svg>
  );
}

export default FacebookIcon;
