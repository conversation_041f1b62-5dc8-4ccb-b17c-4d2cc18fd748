import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size: number;
};

function PeopleEdit({ size, color = '#fff', ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 32 32' fill='none' {...props}>
      <Path
        d='M16 13.6667C18.7614 13.6667 21 11.4281 21 8.66669C21 5.90526 18.7614 3.66669 16 3.66669C13.2386 3.66669 11 5.90526 11 8.66669C11 11.4281 13.2386 13.6667 16 13.6667Z'
        stroke={color}
        strokeWidth={1.5}
        strokeLinejoin='round'
      />
      <Path
        d='M13.6681 27H8.93061C7.35925 27 6.09973 25.6243 6.6448 24.1506C8.04948 20.3524 11.48 17.6667 16.0015 17.6667C17.1943 17.6667 18.3111 17.8536 19.3348 18.1986'
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <Path
        d='M18.3359 28.3334V27.1046C18.3359 26.3974 18.6169 25.7191 19.117 25.219L24.5026 19.8334C25.1469 19.1891 26.1917 19.1891 26.8359 19.8334C27.4802 20.4778 27.4802 21.5224 26.8359 22.1667L21.4503 27.5523C20.9502 28.0524 20.2719 28.3334 19.5647 28.3334H18.3359Z'
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </Svg>
  );
}

export default PeopleEdit;
