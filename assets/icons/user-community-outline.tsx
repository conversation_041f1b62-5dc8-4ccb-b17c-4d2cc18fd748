import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
};

function UserCommunityOutline({ color = '#0E100F', ...props }: Props) {
  return (
    <Svg width='17' height='16' viewBox='0 0 17 16' fill='none' {...props}>
      <Path
        d='M10 1.83301C11.2887 1.83301 12.3334 2.87767 12.3334 4.16634C12.3334 5.45501 11.2887 6.49967 10 6.49967M13.8334 13.4997H14.1667C14.903 13.4997 15.5166 12.8895 15.2936 12.1876C14.8399 10.7589 13.6746 9.53967 12.1667 9.00554M6.16668 6.49967C4.87802 6.49967 3.83334 5.45501 3.83334 4.16634C3.83334 2.87767 4.87802 1.83301 6.16668 1.83301C7.45537 1.83301 8.50004 2.87767 8.50004 4.16634C8.50004 5.45501 7.45537 6.49967 6.16668 6.49967ZM0.869695 12.1661C1.5179 10.1008 3.64394 8.49967 6.16668 8.49967C8.68944 8.49967 10.8154 10.1008 11.4636 12.1661C11.6842 12.8687 11.0697 13.4789 10.3334 13.4789H2.00001C1.26363 13.4788 0.649183 12.8687 0.869695 12.1661Z'
        stroke={color}
        strokeWidth='1.5'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </Svg>
  );
}

export default UserCommunityOutline;
