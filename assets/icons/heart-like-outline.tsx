import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size?: number;
};

function HeartLikeOutline({ color = 'white', size = 16, ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 16 16' fill='none' {...props}>
      <Path
        d='M14.1654 6.625C14.1654 10.5781 8.51256 13.5 7.9987 13.5C7.48483 13.5 1.83203 10.5781 1.83203 6.625C1.83203 3.875 3.54499 2.5 5.25796 2.5C6.9709 2.5 7.9987 3.53125 7.9987 3.53125C7.9987 3.53125 9.0265 2.5 10.7394 2.5C12.4524 2.5 14.1654 3.875 14.1654 6.625Z'
        stroke={color}
        strokeWidth='1.5'
        strokeLinejoin='round'
      />
    </Svg>
  );
}

export default HeartLikeOutline;
