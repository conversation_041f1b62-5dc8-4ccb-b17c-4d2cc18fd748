import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size: number;
};

function Notebook({ size, color = '#fff', ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 32 32' fill='none' {...props}>
      <Path
        d='M11.6641 4.33337H8.33073C6.85797 4.33337 5.66406 5.52728 5.66406 7.00004V25C5.66406 26.4728 6.85797 27.6667 8.33073 27.6667H11.6641M11.6641 4.33337H23.6641C25.1369 4.33337 26.3307 5.52728 26.3307 7.00004V25C26.3307 26.4728 25.1369 27.6667 23.6641 27.6667H11.6641M11.6641 4.33337V27.6667M16.9974 10.3334H20.9974M16.9974 15.6667H20.9974'
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </Svg>
  );
}

export default Notebook;
