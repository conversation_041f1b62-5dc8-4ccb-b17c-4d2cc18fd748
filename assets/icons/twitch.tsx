import Svg, { Path, Rect, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  size: number;
};

function TwitchIcon({ size, ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 36 36' fill='none' {...props}>
      <Rect width={36} height={36} rx={18} fill='#9146FF' />
      <Path
        d='M13.82 9.975l-.803.804-1.408 9.047 4.222 2.211.603.804 4.021-1.407 4.423-3.418.201-3.216-.201-4.624-.603-.201H13.82z'
        fill='#fff'
      />
      <Path
        d='M13.333 9.34L10 12.434v11.134h4v3.092l3.333-3.092H20L26 18V9.34H13.333zm11.334 8.042L22 19.856h-2.667L17 22.02v-2.164h-3v-9.278h10.667v6.804z'
        fill='#000'
      />
      <Path d='M22.667 12.743h-1.333v3.71h1.333v-3.71zM19.001 12.743h-1.333v3.71h1.333v-3.71z' fill='#000' />
    </Svg>
  );
}

export default TwitchIcon;
