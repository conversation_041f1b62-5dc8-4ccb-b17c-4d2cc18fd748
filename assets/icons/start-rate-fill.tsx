import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size?: number;
};

function StarRateFill({ size, color = '#FFB200', ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 48 48' fill='none' {...props}>
      <Path
        fill={color}
        d='M22.63 4.36c.55-1.146 2.19-1.146 2.74 0l4.737 9.86c.222.46.662.78 1.172.846l10.899 1.427c1.267.166 1.774 1.721.846 2.597l-7.968 7.512c-.374.353-.543.87-.45 1.374l2.002 10.733c.233 1.25-1.093 2.21-2.216 1.605l-9.67-5.212a1.524 1.524 0 00-1.445 0l-9.67 5.212c-1.122.605-2.448-.355-2.215-1.605l2.001-10.733a1.508 1.508 0 00-.449-1.374L4.976 19.09c-.928-.876-.421-2.431.846-2.597l10.9-1.427c.509-.067.95-.385 1.171-.846l4.737-9.86z'
      />
    </Svg>
  );
}

export default StarRateFill;
