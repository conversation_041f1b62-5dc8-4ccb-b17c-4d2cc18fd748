import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size?: number;
};

function StarRateHalf({ size, color = '#fff', strokeWidth, ...props }: Props) {
  return (
    <Svg width={size || 48} height={size || 48} viewBox='0 0 48 48' fill='none' {...props}>
      <Path
        d='M25.37 4.36c-.55-1.146-2.19-1.146-2.74 0l-4.737 9.86c-.222.46-.662.78-1.172.846L5.822 16.493c-1.267.166-1.774 1.721-.846 2.597l7.968 7.512c.374.353.543.87.45 1.374l-2.002 10.733c-.233 1.25 1.093 2.21 2.216 1.605l9.67-5.212a1.524 1.524 0 011.445 0l9.67 5.212c1.122.605 2.448-.355 2.215-1.605l-2.001-10.733a1.508 1.508 0 01.449-1.374l7.968-7.512c.928-.876.421-2.431-.846-2.597l-10.9-1.427a1.518 1.518 0 01-1.171-.846L25.37 4.36z'
        stroke={color}
        strokeOpacity={0.4}
        strokeWidth={1.5}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <Path
        d='M25 3.871c-.712-.623-1.914-.46-2.37.488l-4.736 9.86c-.222.462-.663.78-1.172.848l-10.9 1.426c-1.267.166-1.774 1.722-.845 2.597l7.967 7.512c.374.352.543.87.45 1.374l-2.001 10.733c-.233 1.25 1.092 2.21 2.214 1.605l9.67-5.212c.451-.243.995-.243 1.446 0l.277.15V3.871z'
        fill='#FFB200'
      />
    </Svg>
  );
}

export default StarRateHalf;
