import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size: number;
};

function Image({ size, color = '#fff', ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 32 32' fill='none' {...props}>
      <Path
        d='M7.667 6h16.666V4H7.667v2zM26 7.667v16.666h2V7.667h-2zM5.707 22.374l3.781-3.781-1.414-1.415-3.781 3.782 1.414 1.414zM6 24.334v-2.667H4v2.666h2zm0-2.667v-14H4v14h2zm5.845-3.074l9.115 9.114 1.414-1.414-9.115-9.114-1.414 1.414zM24.333 26h-2.666v2h2.666v-2zm-2.666 0h-14v2h14v-2zM9.488 18.593c.651-.651 1.706-.651 2.357 0l1.414-1.415a3.667 3.667 0 00-5.185 0l1.414 1.415zM4 24.333A3.667 3.667 0 007.667 28v-2C6.747 26 6 25.254 6 24.333H4zm22 0c0 .92-.746 1.667-1.667 1.667v2A3.667 3.667 0 0028 24.333h-2zM24.333 6C25.253 6 26 6.746 26 7.667h2A3.667 3.667 0 0024.333 4v2zM7.667 4A3.667 3.667 0 004 7.667h2C6 6.747 6.746 6 7.667 6V4zM21 12.667c0 .92-.746 1.666-1.667 1.666v2A3.667 3.667 0 0023 12.667h-2zm-1.667 1.666c-.92 0-1.666-.746-1.666-1.666h-2a3.667 3.667 0 003.666 3.666v-2zm-1.666-1.666c0-.92.746-1.667 1.666-1.667V9a3.667 3.667 0 00-3.666 3.667h2zM19.333 11c.92 0 1.667.746 1.667 1.667h2A3.667 3.667 0 0019.333 9v2z'
        fill={color}
        opacity={0.56}
      />
    </Svg>
  );
}

export default Image;
