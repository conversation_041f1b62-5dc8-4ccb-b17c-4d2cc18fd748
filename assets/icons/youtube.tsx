import Svg, { Path, Rect, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  size: number;
};

function YoutubeIcon({ size, ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 36 36' fill='none' {...props}>
      <Rect width={size} height={size} rx={18} fill='red' />
      <Path
        d='M27.583 13.186a2.509 2.509 0 00-1.769-1.77C24.255 11 18 11 18 11s-6.252 0-7.814.417a2.509 2.509 0 00-1.77 1.769C8 14.745 8 18 8 18s0 3.255.417 4.814c.229.86.907 1.537 1.769 1.77C11.748 25 18 25 18 25s6.255 0 7.814-.417a2.509 2.509 0 001.77-1.769C28 21.255 28 18 28 18s0-3.255-.417-4.814z'
        fill='#fff'
      />
      <Path d='M16.002 21l5.196-3-5.196-3v6z' fill='red' />
    </Svg>
  );
}

export default YoutubeIcon;
