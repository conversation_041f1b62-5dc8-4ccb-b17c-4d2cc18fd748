import Svg, { Path, Rect, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  circleColor?: string;
  arrowColor?: string;
  size: number;
};

function ArrowUpCircleIcon({ circleColor = '#fff', arrowColor = '#fff', size, ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 16 16' fill='none' {...props}>
      <Rect x={0.75} y={0.75} width={14.5} height={14.5} rx={7.25} stroke={circleColor} strokeWidth={1.5} />
      <Path
        d='M8 12V4m0 0L5 6.963M8 4l3 2.963'
        stroke={arrowColor}
        strokeWidth={1.4}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </Svg>
  );
}

export default ArrowUpCircleIcon;
