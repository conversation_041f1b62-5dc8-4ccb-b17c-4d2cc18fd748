import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size: number;
};

const Edit = ({ size, color = '#fff', ...props }: Props) => {
  return (
    <Svg width={size} height={size} viewBox='0 0 24 24' fill='none' {...props}>
      <Path
        d='M1.75 5.75h7.5M1.75 9.75h4.5M21.336 6.974l-1.31-1.31a2 2 0 00-2.828 0l-8.862 8.862a2 2 0 00-.586 1.414v3.31h3.31a2 2 0 001.414-.586l8.862-8.862a2 2 0 000-2.828z'
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </Svg>
  );
};

export default Edit;
