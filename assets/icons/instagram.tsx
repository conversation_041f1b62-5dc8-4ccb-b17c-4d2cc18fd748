import Svg, { Defs, LinearGradient, Path, Rect, Stop, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  size: number;
};

function InstagramIcon({ size, ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 36 36' fill='none' {...props}>
      <Rect width={size} height={size} rx={18} fill='url(#paint0_linear_4793_46234)' />
      <Path
        d='M15.334 18A2.667 2.667 0 1120.668 18a2.667 2.667 0 01-5.334.001zm-1.442 0a4.109 4.109 0 108.217.001 4.109 4.109 0 00-8.217 0zm7.419-4.272a.96.96 0 10.96-.96.96.96 0 00-.96.96zm-6.543 10.786c-.78-.035-1.204-.165-1.486-.275a2.487 2.487 0 01-.92-.599 2.47 2.47 0 01-.598-.92c-.11-.281-.24-.706-.275-1.486-.04-.843-.047-1.097-.047-3.234s.008-2.39.047-3.234c.035-.78.166-1.203.275-1.486.145-.373.318-.64.598-.92.28-.28.546-.454.92-.599.282-.11.706-.24 1.486-.275.843-.04 1.097-.047 3.232-.047 2.136 0 2.39.009 3.233.047.78.035 1.204.166 1.486.275.373.145.64.319.92.599.28.28.453.546.599.92.11.282.24.706.275 1.486.039.844.046 1.097.046 3.234s-.007 2.39-.046 3.234c-.036.78-.166 1.204-.275 1.486a2.48 2.48 0 01-.599.92c-.28.28-.546.453-.92.599-.282.11-.706.24-1.486.275-.843.039-1.096.047-3.233.047-2.136 0-2.389-.008-3.232-.047zm-.066-14.468c-.852.039-1.434.174-1.942.372a3.925 3.925 0 00-1.417.923 3.912 3.912 0 00-.923 1.417c-.198.51-.333 1.09-.372 1.943-.039.853-.048 1.126-.048 3.3 0 2.172.009 2.445.048 3.298.04.852.174 1.434.372 1.943.204.526.478.973.923 1.418.445.444.89.718 1.417.922.51.198 1.09.334 1.942.372.853.039 1.126.049 3.298.049 2.173 0 2.446-.01 3.299-.049.851-.038 1.433-.174 1.942-.372a3.934 3.934 0 001.417-.922 3.92 3.92 0 00.922-1.418c.198-.509.334-1.09.372-1.943.039-.854.048-1.126.048-3.299s-.01-2.446-.048-3.3c-.039-.851-.174-1.433-.372-1.942a3.935 3.935 0 00-.922-1.417 3.915 3.915 0 00-1.417-.923c-.51-.198-1.09-.334-1.942-.372-.853-.039-1.125-.048-3.298-.048s-2.446.009-3.3.048z'
        fill='#fff'
      />
      <Defs>
        <LinearGradient
          id='paint0_linear_4793_46234'
          x1={35.3081}
          y1={36}
          x2={-0.691919}
          y2={-9.69019e-7}
          gradientUnits='userSpaceOnUse'
        >
          <Stop stopColor='#FBE18A' />
          <Stop offset={0.21} stopColor='#FCBB45' />
          <Stop offset={0.38} stopColor='#F75274' />
          <Stop offset={0.52} stopColor='#D53692' />
          <Stop offset={0.74} stopColor='#8F39CE' />
          <Stop offset={1} stopColor='#5B4FE9' />
        </LinearGradient>
      </Defs>
    </Svg>
  );
}

export default InstagramIcon;
