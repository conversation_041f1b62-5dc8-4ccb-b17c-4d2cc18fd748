import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size: number;
};

function CircleQuestionmark({ size, color = '#fff', ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 32 32' fill='none' {...props}>
      <Path
        d='M15.9974 28.3333C22.8089 28.3333 28.3307 22.8114 28.3307 16C28.3307 9.18845 22.8089 3.66663 15.9974 3.66663C9.18589 3.66663 3.66406 9.18845 3.66406 16C3.66406 22.8114 9.18589 28.3333 15.9974 28.3333Z'
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <Path
        d='M15.9974 21.3333V21.32M16.3307 21.3333C16.3307 21.5175 16.1815 21.6667 15.9974 21.6667C15.8133 21.6667 15.6641 21.5175 15.6641 21.3333C15.6641 21.1492 15.8133 21 15.9974 21C16.1815 21 16.3307 21.1492 16.3307 21.3333Z'
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <Path
        d='M13 12.3334C13 11.2288 13.8955 10.3334 15 10.3334H16.6116C17.9307 10.3334 19 11.4027 19 12.7218C19 13.5203 18.6009 14.2662 17.9364 14.7091L17.1875 15.2084C16.4456 15.703 16 16.5356 16 17.4272V17.6667'
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </Svg>
  );
}

export default CircleQuestionmark;
