import Svg, { Path, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  color?: string;
  size?: number;
};

function StarRateOutlineV2({ size, color = 'white', strokeOpacity = 0.4, ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 17 16' fill='none' {...props}>
      <Path
        d='M8.043 1.453a.507.507 0 01.914 0l1.579 3.287c.074.154.22.26.39.282l3.633.476a.503.503 0 01.282.865l-2.656 2.504a.503.503 0 00-.15.458l.668 3.578a.506.506 0 01-.739.535l-3.223-1.737a.508.508 0 00-.482 0l-3.223 1.737a.506.506 0 01-.739-.535l.667-3.578a.503.503 0 00-.15-.458L2.16 6.363a.503.503 0 01.282-.865l3.633-.476a.506.506 0 00.39-.282l1.58-3.287z'
        stroke={color}
        strokeWidth={1.5}
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </Svg>
  );
}

export default StarRateOutlineV2;
