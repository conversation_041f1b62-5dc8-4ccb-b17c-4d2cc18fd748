import Svg, { Path, Rect, SvgProps } from 'react-native-svg';

type Props = SvgProps & {
  size: number;
};

function LinkedinIcon({ size, ...props }: Props) {
  return (
    <Svg width={size} height={size} viewBox='0 0 36 36' fill='none' {...props}>
      <Rect width={36} height={36} rx={18} fill='#069' />
      <Path
        d='M13.632 25.653V15.325h-3.43v10.328h3.43zm-1.714-11.738c1.196 0 1.94-.793 1.94-1.784-.022-1.014-.744-1.785-1.917-1.785-1.174 0-1.941.771-1.941 1.785 0 .99.744 1.784 1.895 1.784h.023zm3.613 11.738h3.43v-5.767c0-.308.022-.617.112-.837.248-.617.812-1.256 1.76-1.256 1.24 0 1.737.947 1.737 2.335v5.525H26v-5.922c0-3.172-1.692-4.648-3.948-4.648-1.85 0-2.663 1.035-3.114 1.74h.022v-1.498h-3.43c.046.97 0 10.329 0 10.329z'
        fill='#fff'
      />
    </Svg>
  );
}

export default LinkedinIcon;
