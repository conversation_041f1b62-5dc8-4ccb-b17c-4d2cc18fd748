import { ZOD_ERRORS } from '@/config/zodError';
import { REGEX } from '@/constants/regex';
import { differenceInYears } from 'date-fns';
import { z } from 'zod';

export const SignUpSchema = z.object({
  identifier: z
    .string()
    .min(1, ZOD_ERRORS.required)
    .refine(
      (val) => {
        const looksLikePhone = val.startsWith('+') && REGEX.DIGIT_ONLY.test(val.slice(1));
        const looksLikePhoneWithoutCountryCode = REGEX.DIGIT_ONLY.test(val);

        if (looksLikePhone) {
          const isValidPhone = REGEX.PHONE.test(val);
          // ignore character '+'
          if (val.length < 10 || val.length > 16) {
            return false;
          }
          if (!isValidPhone) {
            return false;
          }
          return true;
        } else if (looksLikePhoneWithoutCountryCode) {
          const isValidPhone = REGEX.PHONE_NO_COUNTRY_CODE.test(val);

          if (!isValidPhone) return false;

          return true;
        } else {
          // Validate as email
          const isValidEmail = REGEX.EMAIL.test(val);
          if (!isValidEmail) {
            return false;
          }
          if (val.length > 254) {
            return false;
          }
          return true;
        }
      },
      (val) => {
        const looksLikePhone = val.startsWith('+') && REGEX.DIGIT_ONLY.test(val.slice(1));
        const looksLikePhoneWithoutCountryCode = REGEX.DIGIT_ONLY.test(val);

        if (looksLikePhone) {
          const isValidPhone = REGEX.PHONE.test(val);
          // ignore character '+'
          if (val.length < 10 || val.length > 16) {
            return { message: ZOD_ERRORS.phoneNumberLength };
          }
          if (!isValidPhone) {
            return { message: ZOD_ERRORS.invalidPhoneNumber };
          }
        } else if (looksLikePhoneWithoutCountryCode) {
          const isValidPhone = REGEX.PHONE_NO_COUNTRY_CODE.test(val);

          if (!isValidPhone) return { message: ZOD_ERRORS.phoneNumberNoCodeLength };
        } else {
          // Validate as email
          const isValidEmail = REGEX.EMAIL.test(val);
          if (!isValidEmail) {
            return { message: ZOD_ERRORS.invalidEmail };
          }
          if (val.length > 254) {
            return { message: ZOD_ERRORS.emailLength };
          }
        }
        return { message: '' };
      }
    ),
});

export type SignUpSchemaData = z.infer<typeof SignUpSchema>;

// Password validation schema using zod
export const PasswordSchema = z
  .object({
    password: z
      .string()
      .min(8, ZOD_ERRORS.passwordLength)
      .max(64, ZOD_ERRORS.passwordLength)
      .regex(REGEX.PASSWORD, ZOD_ERRORS.passwordRegex),
    confirmPassword: z
      .string()
      .min(8, ZOD_ERRORS.passwordLength)
      .max(64, ZOD_ERRORS.passwordLength)
      .regex(REGEX.PASSWORD, ZOD_ERRORS.passwordRegex),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: ZOD_ERRORS.passwordMatch,
    path: ['confirmPassword'],
  });

// Export the type for the schema
export type PasswordSchemaData = z.infer<typeof PasswordSchema>;

// Zod schemas
export const UpdateUserProfileZodSchema = z.object({
  username: z.string().min(1, ZOD_ERRORS.required).max(32, ZOD_ERRORS.usernameLength).trim(),
  email: z
    .string()
    .trim()
    .min(1, ZOD_ERRORS.required)
    .max(254, ZOD_ERRORS.emailLength)
    .regex(REGEX.EMAIL, ZOD_ERRORS.invalidEmail),
  dateOfBirth: z
    .string()
    .optional()
    // .regex(/^(\d{2})\/(\d{2})\/(\d{4})$/, ZOD_ERRORS.dateOfBirthFormat)
    .refine(
      (value) => {
        if (!value) return true;
        const dateRegex = /^(\d{2})\/(\d{2})\/(\d{4})$/;
        const match = value.match(dateRegex);

        if (!match) return false;

        // Check if the date is valid
        const [month, day, year] = value.split('/').map(Number);
        const date = new Date(year, month - 1, day);
        const isValidDate =
          date && date.getFullYear() === year && date.getMonth() === month - 1 && date.getDate() === day;

        return isValidDate;
      },
      { message: ZOD_ERRORS.dateOfBirthFormat }
    )
    .refine(
      (value) => {
        if (!value) return true;
        const [month, day, year] = value.split('/').map(Number);
        const date = new Date(year, month - 1, day);

        if (date > new Date()) {
          return false;
        }
        return true;
      },
      { message: ZOD_ERRORS.dateOfBirthFuture }
    )
    .refine(
      (value) => {
        if (!value) return true;
        const [month, day, year] = value.split('/').map(Number);
        const date = new Date(year, month - 1, day);

        const age = differenceInYears(new Date(), date);
        return age >= 18;
      },
      { message: ZOD_ERRORS.dateOfBirthTooOld }
    ),
  referral: z.string().trim().optional(),
  confirmation: z.boolean().optional(),
  bio: z.string().trim().max(600, ZOD_ERRORS.bioLength).optional(),
  phoneNumber: z
    .string()
    .trim()
    .refine(
      (val) => {
        if (!val) return true;

        const hasOnlyValidChars = REGEX.PHONE_CHARS_ONLY.test(val);

        if (!hasOnlyValidChars) {
          return false;
        }

        const looksLikePhone = val.startsWith('+') && REGEX.DIGIT_ONLY.test(val.slice(1));
        const looksLikePhoneWithoutCountryCode = REGEX.DIGIT_ONLY.test(val);

        if (looksLikePhone) {
          const isValidPhone = REGEX.PHONE.test(val);
          // ignore character '+'
          if (val.length < 10 || val.length > 16) {
            return false;
          }
          if (!isValidPhone) {
            return false;
          }
          return true;
        } else if (looksLikePhoneWithoutCountryCode) {
          const isValidPhone = REGEX.PHONE_NO_COUNTRY_CODE.test(val);

          if (!isValidPhone) return false;

          return true;
        }

        return false;
      },
      (val) => {
        const hasOnlyValidChars = REGEX.PHONE_CHARS_ONLY.test(val);
        if (!hasOnlyValidChars) {
          return { message: ZOD_ERRORS.invalidPhoneNumber };
        }

        const looksLikePhone = val.startsWith('+') && REGEX.DIGIT_ONLY.test(val.slice(1));
        const looksLikePhoneWithoutCountryCode = REGEX.DIGIT_ONLY.test(val);

        if (looksLikePhone) {
          const isValidPhone = REGEX.PHONE.test(val);
          // ignore character '+'
          if (val.length < 10 || val.length > 16) {
            return { message: ZOD_ERRORS.phoneNumberLength };
          }
          if (!isValidPhone) {
            return { message: ZOD_ERRORS.invalidPhoneNumber };
          }
        } else if (looksLikePhoneWithoutCountryCode) {
          const isValidPhone = REGEX.PHONE_NO_COUNTRY_CODE.test(val);

          if (!isValidPhone) return { message: ZOD_ERRORS.phoneNumberNoCodeLength };
        }
        return { message: ZOD_ERRORS.invalidPhoneNumber };
      }
    )
    .optional(),
  address: z.string().trim().max(300, ZOD_ERRORS.addressLength).optional(),
});

export type UpdateUserProfileZodData = z.infer<typeof UpdateUserProfileZodSchema>;
