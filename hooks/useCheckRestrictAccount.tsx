import { useGetProfileQuery } from '@/apis/auth/queries';
import { useUserStore } from '@/store/user';
import { useCallback } from 'react';

export const useCheckRestrictAccount = () => {
  const { data: userProfile } = useGetProfileQuery();
  const setIsShowWarningRestricted = useUserStore.use.setIsShowWarningRestricted();

  const onCheckAccountRestricted = useCallback(() => {
    if (!userProfile) return true;

    if (!userProfile?.isActive) {
      setIsShowWarningRestricted(true);
      return true;
    }

    return false;
  }, [userProfile, setIsShowWarningRestricted]);

  return {
    onCheckAccountRestricted,
  };
};
