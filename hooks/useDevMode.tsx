import { useCommonStore } from '@/store/common';
import { router } from 'expo-router';
import { useCallback, useState } from 'react';

export const useDevMode = () => {
  const [countToggle, setCountToggle] = useState(0);

  const isDevModeOpen = useCommonStore.use.isOpenDevMode();
  const toggleDevMode = useCommonStore.use.toggleOpenDevMode();
  const isStagingENV = useCommonStore.use.isStagingENV();
  const toggleStagingENV = useCommonStore.use.toggleStagingENV();

  const handleToggle = useCallback(() => {
    if (isDevModeOpen) {
      return router.navigate('/(app)/settings/dev-mode');
    }

    if (countToggle === 5) {
      toggleDevMode(true);
    } else {
      setCountToggle((prev) => prev + 1);
    }
  }, [countToggle, isDevModeOpen, toggleDevMode]);

  return {
    onToggle: handleToggle,
    toggleDevMode,
    isDevModeOpen,
    isStagingENV,
    toggleStagingENV,
  };
};
