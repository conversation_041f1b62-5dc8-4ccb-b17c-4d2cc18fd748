import { useState, useCallback } from 'react';

interface UseExpandableTextProps {
  maxLines?: number;
}

export const useExpandableText = ({ maxLines = 3 }: UseExpandableTextProps = {}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showButton, setShowButton] = useState(false);

  const toggleExpanded = useCallback(() => {
    setIsExpanded((prev) => !prev);
  }, []);

  const onTextLayout = useCallback(
    (event: any) => {
      const { lines } = event.nativeEvent;

      if (lines.length >= maxLines) {
        setShowButton(true);
      } else {
        setShowButton(false);
      }
    },
    [maxLines]
  );

  return {
    isExpanded,
    showButton,
    toggleExpanded,
    onTextLayout,
    numberOfLines: isExpanded ? undefined : maxLines,
  };
};
