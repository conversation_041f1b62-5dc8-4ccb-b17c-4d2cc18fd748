import { useAnimatedStyle, useSharedValue, withSpring } from 'react-native-reanimated';

export const useScaleFocus = (scale: number) => {
  const pressed = useSharedValue(false);

  const handlePressIn = () => {
    pressed.value = true;
  };

  const handlePressOut = () => {
    pressed.value = false;
  };

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: withSpring(pressed.value ? scale : 1) }],
    };
  });

  return {
    animatedStyle,
    pressed,
    handlePressIn,
    handlePressOut,
  };
};
