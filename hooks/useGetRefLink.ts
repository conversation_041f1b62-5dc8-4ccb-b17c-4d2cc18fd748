import { env } from '@/utils/const';
import { useCommonStore } from '@/store/common';
import { useGetProfileQuery } from '@/apis/auth/queries';

export const useGetRefLink = () => {
  const { data: userProfile } = useGetProfileQuery();
  const isStagingENV = useCommonStore.use.isStagingENV();
  const refCode = userProfile?.referral ?? '';
  const refLink = `${isStagingENV ? env.API_URL_STAGING : env.API_URL_DEV}/sign-up?code=${refCode}`;

  return {
    refLink,
    refCode,
  };
};
