import { useState } from 'react';
import { LayoutChangeEvent } from 'react-native';
import { useAnimatedScrollHand<PERSON>, useSharedValue } from 'react-native-reanimated';

export const useHeaderStyleAnimated = () => {
  const scrollY = useSharedValue(0);
  const [headerHeight, setHeaderHeight] = useState(0);

  const onHeaderLayout = (event: LayoutChangeEvent) => {
    const height = event.nativeEvent.layout.height;
    setHeaderHeight(height);
  };

  const onScroll = useAnimatedScrollHandler((event) => {
    scrollY.value = event.contentOffset.y;
  });

  return {
    scrollY,
    headerHeight,
    onScroll,
    onHeaderLayout,
  };
};
