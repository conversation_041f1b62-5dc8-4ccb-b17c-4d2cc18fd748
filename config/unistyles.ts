import { Colors } from '@/constants/Colors';
import { DimensionValue, TextStyle } from 'react-native';
import { UnistylesRegistry } from 'react-native-unistyles';

const lightTheme = {
  colors: {
    ...Colors.light,
  },
  gap: (v: number) => v * 8,
  wFull: {
    width: '100%' as DimensionValue,
  },
  fw400: {
    fontWeight: 400 as TextStyle['fontWeight'],
    fontFamily: 'Inter',
  },
  fw500: {
    fontWeight: 500 as TextStyle['fontWeight'],
    fontFamily: 'InterMedium',
  },
  fw600: {
    fontWeight: 600 as TextStyle['fontWeight'],
    fontFamily: 'InterSemiBold',
  },
  fw700: {
    fontWeight: 700 as TextStyle['fontWeight'],
    fontFamily: 'InterBold',
  },
  fw800: {
    fontWeight: 800 as TextStyle['fontWeight'],
    fontFamily: 'InterExtraBold',
  },
  fw900: {
    fontWeight: 900 as TextStyle['fontWeight'],
    fontFamily: 'InterBlack',
  },
};

const darkTheme = {
  colors: {
    ...Colors.light,
    ...Colors.dark,
  },
  gap: (v: number) => v * 8,
  wFull: {
    width: '100%' as DimensionValue,
  },
  fw400: {
    fontWeight: 400 as TextStyle['fontWeight'],
    fontFamily: 'Inter',
  },
  fw500: {
    fontWeight: 500 as TextStyle['fontWeight'],
    fontFamily: 'InterMedium',
  },
  fw600: {
    fontWeight: 600 as TextStyle['fontWeight'],
    fontFamily: 'InterSemiBold',
  },
  fw700: {
    fontWeight: 700 as TextStyle['fontWeight'],
    fontFamily: 'InterBold',
  },
  fw800: {
    fontWeight: 800 as TextStyle['fontWeight'],
    fontFamily: 'InterExtraBold',
  },
  fw900: {
    fontWeight: 900 as TextStyle['fontWeight'],
    fontFamily: 'InterBlack',
  },
};

const breakpoints = {
  xs: 0,
  sm: 300,
  md: 500,
  lg: 800,
  xl: 1200,
};

type AppBreakpoints = typeof breakpoints;
type AppThemes = {
  light: typeof lightTheme;
  dark: typeof darkTheme;
};

declare module 'react-native-unistyles' {
  export interface UnistylesBreakpoints extends AppBreakpoints {}
  export interface UnistylesThemes extends AppThemes {}
}

UnistylesRegistry.addBreakpoints(breakpoints)
  .addThemes({
    light: lightTheme,
    dark: darkTheme,
    // register other themes with unique names
  })
  .addConfig({
    // you can pass here optional config described below
    adaptiveThemes: true,
  });
