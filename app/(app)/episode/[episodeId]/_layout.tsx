import { Stack } from 'expo-router';

export default function EpisodeDetailLayout() {
  return (
    <Stack initialRouteName='add-post'>
      <Stack.Screen name='index' options={{ headerShown: false }} />
      <Stack.Screen name='detail' options={{ headerShown: false }} />
      <Stack.Screen name='add-post' options={{ headerShown: false }} />
      <Stack.Screen name='edit-post/[postId]' options={{ headerShown: false }} />
      <Stack.Screen name='rating' options={{ headerShown: false }} />
      <Stack.Screen name='review/[postId]' options={{ headerShown: false }} />
    </Stack>
  );
}
