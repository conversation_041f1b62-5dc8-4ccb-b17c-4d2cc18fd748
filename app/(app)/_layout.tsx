import { useGetProfileQuery } from '@/apis/auth/queries';
import { SetupAddressPatronModal } from '@/components/SetupAddressPatronModal';
import { WarningRestrictModal } from '@/components/WarningRestrictModal';
import ChoosePlanProvider from '@/modules/choose-plan/ChoosePlanProvider';
import { useUserStore } from '@/store/user';
import { Stack } from 'expo-router';
import { View } from 'react-native';

export default function AppLayout() {
  const { data: userProfile } = useGetProfileQuery();
  const isShowedWarningRestricted = useUserStore.use.isShowedWarningRestricted();
  const isShowSetupAddressPatronModal = useUserStore.use.isShowSetupAddressPatronModal();

  const isVisibleRestrict = userProfile ? isShowedWarningRestricted : false;

  return (
    <ChoosePlanProvider>
      <View style={{ flex: 1 }}>
        <Stack screenOptions={{ contentStyle: { backgroundColor: 'transparent' } }}>
          <Stack.Screen name='(tabs)' options={{ headerShown: false }} />

          <Stack.Screen name='[userId]/index' options={{ headerShown: false }} />

          <Stack.Screen name='[userId]/account-likes' options={{ headerShown: false }} />

          <Stack.Screen name='[userId]/account-upvote' options={{ headerShown: false }} />

          <Stack.Screen name='[userId]/account-reviews' options={{ headerShown: false }} />

          <Stack.Screen name='[userId]/account-watchlist' options={{ headerShown: false }} />

          <Stack.Screen name='[userId]/account-watched' options={{ headerShown: false }} />

          <Stack.Screen name='categories-all' options={{ headerShown: false }} />

          {/* <Stack.Screen name='show' options={{ headerShown: false }} /> */}

          <Stack.Screen name='podcast' options={{ headerShown: false }} />

          <Stack.Screen name='episode' options={{ headerShown: false }} />

          <Stack.Screen name='category/[id]' options={{ headerShown: false }} />

          <Stack.Screen name='discover-search' options={{ headerShown: false, animation: 'none' }} />

          {/* <Stack.Screen name='review/[id]' options={{ headerShown: false }} /> */}

          <Stack.Screen name='profile-followers' options={{ headerShown: false }} />

          <Stack.Screen name='favorite-shows' options={{ headerShown: false }} />

          <Stack.Screen name='favorite-episodes' options={{ headerShown: false }} />

          <Stack.Screen name='account-update' options={{ headerShown: false }} />

          <Stack.Screen name='settings' options={{ headerShown: false }} />

          <Stack.Screen name='spread-vibes' options={{ headerShown: false }} />

          <Stack.Screen name='subscription-plan' options={{ headerShown: false }} />

          <Stack.Screen name='edit-account' options={{ headerShown: false }} />

          <Stack.Screen name='create-password' options={{ headerShown: false }} />

          <Stack.Screen name='change-password' options={{ headerShown: false }} />

          <Stack.Screen name='verify-code-create-password' options={{ headerShown: false }} />

          <Stack.Screen name='verify-code' options={{ headerShown: false }} />

          <Stack.Screen name='choose-plan' options={{ headerShown: false }} />

          <Stack.Screen name='update-info' options={{ headerShown: false }} />

          <Stack.Screen name='choose-interest' options={{ headerShown: false }} />

          <Stack.Screen name='choose-podcast' options={{ headerShown: false }} />

          <Stack.Screen name='set-address' options={{ headerShown: false }} />
        </Stack>

        <WarningRestrictModal isVisible={isVisibleRestrict} />
        <SetupAddressPatronModal isVisible={isShowSetupAddressPatronModal} />
      </View>
    </ChoosePlanProvider>
  );
}
