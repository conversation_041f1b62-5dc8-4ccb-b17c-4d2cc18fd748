import { Stack } from 'expo-router';

export default function SettingsLayout() {
  return (
    <Stack initialRouteName='index'>
      <Stack.Screen name='index' options={{ headerShown: false }} />

      <Stack.Screen name='account' options={{ headerShown: false }} />
      <Stack.Screen name='your-referral' options={{ headerShown: false }} />
      <Stack.Screen name='help-support' options={{ headerShown: false }} />
      <Stack.Screen name='terms' options={{ headerShown: false }} />
      <Stack.Screen name='privacy' options={{ headerShown: false }} />

      <Stack.Screen name='dev-mode' options={{ headerShown: false }} />
    </Stack>
  );
}
