import { useGetProfileQuery } from '@/apis/auth/queries';
import { Icons } from '@/assets/icons';
import TabBar from '@/components/TabBar';
import { useLoggedIn } from '@/hooks/useLoggedIn';
import { useCommonStore } from '@/store/common';
import { Redirect, Tabs } from 'expo-router';
import { View } from 'react-native';
import Animated from 'react-native-reanimated';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

export default function TabsLayout() {
  const isFirstTime = useCommonStore.use.isFirstTime();

  const { isLoggedIn } = useLoggedIn();
  const { data: userProfile } = useGetProfileQuery();
  const { theme, styles } = useStyles(stylesheet);

  if (isFirstTime && !isLoggedIn) {
    return <Redirect href='/onboarding' />;
  }

  if (!isLoggedIn) {
    return <Redirect href='/sign-up' />;
  }

  if (userProfile?.status === 'update_profile') {
    return <Redirect href='/(app)/update-info' />;
  }

  if (userProfile?.status === 'plan_payment') {
    return <Redirect href='/(app)/choose-plan' />;
  }

  if (userProfile?.status === 'choose_interest') {
    return <Redirect href='/(app)/choose-interest' />;
  }

  if (userProfile?.status === 'choose_podcast') {
    return <Redirect href='/(app)/choose-podcast' />;
  }

  return (
    <Animated.View style={styles.tabsViewContainer}>
      <Tabs
        tabBar={(props) => <TabBar {...props} />}
        screenOptions={{
          tabBarActiveTintColor: theme.colors.primary,
          tabBarInactiveTintColor: theme.colors.neutralLightGrey,
          // tabBarBackground: () => <View style={styles.container} />,
          tabBarStyle: styles.tabBarStyle,
          lazy: false,
          sceneStyle: {
            backgroundColor: 'transparent',
          },
        }}
      >
        <Tabs.Screen
          name='index'
          options={{
            headerShown: false,
            tabBarIcon: ({ color, focused }) => <Icons.Home color={color} size={24} />,
            title: 'Home',
          }}
        />

        <Tabs.Screen
          name='discover'
          options={{
            headerShown: false,
            tabBarIcon: ({ color, focused }) => <Icons.Global color={color} size={24} />,
            title: 'Discover',
          }}
        />

        <Tabs.Screen
          name='interact'
          options={{
            headerShown: false,
            tabBarIcon: () => {
              return (
                <View style={styles.interactBox}>
                  <Icons.Plus color={theme.colors.neutralBackground} size={24} />
                </View>
              );
            },
            title: 'Interact',
          }}
        />

        <Tabs.Screen
          name='activity'
          options={{
            headerShown: false,
            tabBarIcon: ({ color }) => <Icons.History color={color} size={24} />,
            title: 'Activity',
          }}
        />

        <Tabs.Screen
          name='account'
          key={'tab_account'}
          options={{
            headerShown: false,
            tabBarIcon: ({ color }) => <Icons.PeopleCircle color={color} size={24} />,
            title: 'You',
          }}
        />
      </Tabs>
    </Animated.View>
  );
}

const stylesheet = createStyleSheet((theme, rt) => ({
  tabsViewContainer: {
    flex: 1,
  },
  indicatorContainer: {
    position: 'absolute',
    bottom: rt.insets.bottom - 10,
    width: '20%',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  indicator: {
    borderBottomWidth: 4,
    borderBottomColor: theme.colors.primary,
    width: 40,
    borderRadius: 999,
  },
  container: {
    backgroundColor: theme.colors.neutralBackground,
  },
  tabBarStyle: {
    borderTopColor: theme.colors.whiteOpacity16,
    // height: 80,
  },
  interactBox: {
    backgroundColor: theme.colors.primary,
    borderRadius: 999,
    width: 52,
    height: 52,
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    bottom: 4,
    // transform: [{ scale: 1 }, { translateY: -8 }],
    elevation: 4,
    shadowColor: 'black',
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
    shadowOpacity: 0.2,
  },
  interactIcon: {
    color: theme.colors.neutralBackground,
  },
}));
