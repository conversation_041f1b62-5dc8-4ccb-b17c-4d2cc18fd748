import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

export default function InteractScreen() {
  const { styles } = useStyles(stylesheet);

  return (
    <ThemedView style={styles.container}>
      <ThemedText>InteractScreen</ThemedText>
    </ThemedView>
  );
}

const stylesheet = createStyleSheet({
  container: {
    backgroundColor: '#000',
    flex: 1,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
