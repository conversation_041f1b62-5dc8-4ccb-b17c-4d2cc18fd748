import { Stack } from 'expo-router';

export default function PodcastDetailLayout() {
  return (
    <Stack initialRouteName='index'>
      <Stack.Screen name='index' options={{ headerShown: false }} />
      <Stack.Screen name='add-post' options={{ headerShown: false }} />
      <Stack.Screen name='edit-post/[postId]' options={{ headerShown: false }} />
      <Stack.Screen name='rating' options={{ headerShown: false }} />
      <Stack.Screen name='review/[postId]' options={{ headerShown: false }} />
    </Stack>
  );
}
