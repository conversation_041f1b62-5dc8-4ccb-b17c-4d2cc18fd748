import { ISource } from '@/apis/params';
import { router } from 'expo-router';
import { PropsWithChildren } from 'react';
import { TextStyle } from 'react-native';
import { Text } from 'react-native';

type Props = {
  userId: string | number;
  userType?: ISource;
  style?: TextStyle;
};

export const UserProfileText = ({ children, userId, userType, style }: PropsWithChildren<Props>) => {
  const handleDirect = () => {
    if (userType === 'podchaser') return;
    router.push(`/(app)/${userId}`);
  };

  return (
    <Text
      disabled={!userType || userType === 'podchaser'}
      onPress={handleDirect}
      style={[style, { fontFamily: 'Inter' }]}
    >
      {children}
    </Text>
  );
};
