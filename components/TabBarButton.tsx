import { useScaleFocus } from '@/hooks/useScaleFocus';
import { BottomTabNavigationOptions } from '@react-navigation/bottom-tabs';
import { useEffect } from 'react';
import { Pressable } from 'react-native';
import Animated, { SharedValue, useAnimatedStyle, useSharedValue, withSpring } from 'react-native-reanimated';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  isFocused: boolean;
  label?: BottomTabNavigationOptions['tabBarLabel'] | BottomTabNavigationOptions['title'];
  routeName: string;
  color: string;
  tabBarIcon: BottomTabNavigationOptions['tabBarIcon'];
  onPress: () => void;
  onLongPress: () => void;
  iconScale: SharedValue<number>;
};

const TabBarButton = (props: Props) => {
  const { isFocused, label, routeName, color, tabBarIcon, iconScale } = props;
  const { animatedStyle, handlePressIn, handlePressOut } = useScaleFocus(0.95);
  const { styles } = useStyles(stylesheet);

  const scale = useSharedValue(0);

  useEffect(() => {
    scale.value = withSpring(typeof isFocused === 'boolean' ? (isFocused ? 1 : 0) : isFocused, { duration: 350 });
  }, [scale, isFocused]);

  const scaleStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: isFocused ? iconScale.value : 1 }],
    };
  });

  const isCenteredTab = routeName === 'interact';

  return (
    <Pressable
      {...props}
      style={styles.container}
      onPressIn={isCenteredTab ? handlePressIn : undefined}
      onPressOut={isCenteredTab ? handlePressOut : undefined}
    >
      <Animated.View style={[styles.iconBox, scaleStyle, animatedStyle, { width: 24, height: 24 }]}>
        {tabBarIcon?.({ color, focused: isFocused, size: 24 })}
      </Animated.View>

      {label && typeof label === 'string' && (
        <Animated.Text style={[styles.textStyle, { color }]}>{label}</Animated.Text>
      )}
    </Pressable>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 10,
  },
  textStyle: {
    fontSize: 10,
    ...theme.fw500,
  },
  iconBox: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 5,
  },
}));

export default TabBarButton;
