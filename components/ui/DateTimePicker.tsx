import React, { useState } from 'react';
import { StyleProp, TextInput, TextStyle, TouchableOpacity, View, ViewStyle } from 'react-native';
import DatePicker from 'react-native-date-picker';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { ThemedText } from '../ThemedText';
import { Icons } from '@/assets/icons';

interface CustomDateTimePickerProps {
  value: string;
  onChange: (dateString: string) => void;
  label?: string;
  error?: string | boolean;
  helperText?: string;
  containerStyle?: StyleProp<ViewStyle>;
  placeholder?: string;
  labelStyle?: StyleProp<TextStyle>;
}

export const DateTimePickerInput: React.FC<CustomDateTimePickerProps> = ({
  value,
  onChange,
  label,
  error,
  helperText,
  containerStyle,
  placeholder = 'Select a date',
  labelStyle,
}) => {
  const [open, setOpen] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const hasError = !!error;
  const { styles } = useStyles(stylesheet);

  // Parse string date to Date object for the date picker
  const parseStringToDate = (dateString: string): Date => {
    if (!dateString) return new Date();

    const parts = dateString.split('/');
    if (parts.length === 3) {
      const day = parseInt(parts[1]);
      const month = parseInt(parts[0]) - 1; // JS months are 0-indexed
      const year = parseInt(parts[2]);

      const date = new Date(year, month, day);
      if (!isNaN(date.getTime())) {
        return date;
      }
    }
    return new Date();
  };

  // Format Date to string in DD/MM/YYYY format
  const formatDateToString = (date: Date): string => {
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${month}/${day}/${year}`;
  };

  const handleConfirm = (date: Date) => {
    setOpen(false);
    const formattedDate = formatDateToString(date);
    onChange(formattedDate);
  };

  const handleCalendarPress = () => {
    setOpen(true);
  };

  const handleCancel = () => {
    setOpen(false);
  };

  const handleTextChange = (text: string) => {
    // Only allow numbers and forward slashes
    const cleanText = text.replace(/[^\d/]/g, '');

    // Automatically add forward slashes
    let formattedText = cleanText;
    if (cleanText.length === 2 && !cleanText.includes('/')) {
      formattedText = cleanText + '/';
    } else if (cleanText.length === 5 && cleanText.indexOf('/', 3) === -1) {
      formattedText = cleanText + '/';
    }

    // Prevent deletion of existing value
    onChange(text.length >= value.length ? formattedText : text);
  };

  const handleInputBlur = () => {
    setIsFocused(false);
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {label && (
        <ThemedText style={[styles.label, labelStyle]} type='small'>
          {label}
        </ThemedText>
      )}

      <View style={[styles.inputContainer, isFocused && styles.inputFocused, hasError && styles.inputError]}>
        <TextInput
          style={styles.dateText}
          value={value}
          onChangeText={handleTextChange}
          placeholder={placeholder}
          placeholderTextColor='#999'
          onFocus={() => setIsFocused(true)}
          onBlur={handleInputBlur}
          keyboardType='number-pad'
          maxLength={10}
        />
        <TouchableOpacity onPress={handleCalendarPress} style={styles.iconContainer}>
          <Icons.Calendar size={24} color='#FFFFFF' />
        </TouchableOpacity>
      </View>

      <DatePicker
        modal
        open={open}
        date={parseStringToDate(value)}
        mode='date'
        maximumDate={new Date()}
        onConfirm={handleConfirm}
        onCancel={handleCancel}
      />

      {hasError && typeof error === 'string' ? (
        <ThemedText type='tiny' style={styles.errorText}>
          {error}
        </ThemedText>
      ) : helperText ? (
        <ThemedText style={styles.helperText}>{helperText}</ThemedText>
      ) : null}
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    width: '100%',
  },
  label: {
    color: theme.colors.neutralWhite,
    fontSize: 16,
    marginBottom: 8,
    ...theme.fw500,
  },
  errorLabel: {
    color: theme.colors.stateError,
  },
  inputContainer: {
    backgroundColor: '#1F1E1E',
    borderRadius: 16,
    height: 48,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: 'transparent',
    flexDirection: 'row',
    alignItems: 'center',
  },
  inputFocused: {
    borderColor: theme.colors.primary,
  },
  inputError: {
    borderColor: theme.colors.stateError,
  },
  iconContainer: {
    // marginRight: 10,
  },
  dateText: {
    color: theme.colors.neutralWhite,
    fontSize: 16,
    flex: 1,
  },
  errorText: {
    color: theme.colors.stateError,
    marginTop: 6,
    textAlign: 'right',
  },
  helperText: {
    color: theme.colors.neutralWhite,
    fontSize: 12,
    marginTop: 4,
  },
}));

export default DateTimePickerInput;
