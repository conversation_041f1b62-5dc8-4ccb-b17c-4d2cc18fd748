import { ActivityIndicator, TextStyle, TouchableOpacity, TouchableOpacityProps } from 'react-native';
import { UnistylesVariants, createStyleSheet, useStyles } from 'react-native-unistyles';
import { Spacer } from '../Spacer';
import { ThemedText, ThemedTextProps } from '../ThemedText';

type ComponentProps = UnistylesVariants<typeof stylesheet>;

type Props = ComponentProps &
  TouchableOpacityProps & {
    isLoading?: boolean;
    textStyle?: TextStyle;
    textType?: ThemedTextProps['type'];
  };

export const CustomButton = ({
  type = 'primary',
  style,
  children,
  isLoading,
  disabled,
  textStyle,
  textType = 'default',
  ...rest
}: Props) => {
  const { styles } = useStyles(stylesheet, {
    type,
  });

  return (
    <TouchableOpacity
      style={[styles.button, style, isLoading || disabled ? { opacity: 0.5 } : undefined]}
      disabled={disabled || isLoading}
      activeOpacity={0.7}
      {...rest}
    >
      {isLoading && (
        <>
          <ActivityIndicator color='#000' size='small' />
          {children && <Spacer width={16} />}
        </>
      )}
      {typeof children === 'string' ? (
        <ThemedText style={[styles.buttonText, textStyle]} type={textType} numberOfLines={1}>
          {children}
        </ThemedText>
      ) : (
        children
      )}
    </TouchableOpacity>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  button: {
    paddingHorizontal: 12,
    minHeight: 48,
    borderRadius: 999,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    variants: {
      type: {
        primary: {
          backgroundColor: theme.colors.primary,
        },
        primaryOpacity10: {
          backgroundColor: theme.colors.primaryOpacity10,
        },
        danger: {
          backgroundColor: theme.colors.stateError,
        },
        outlined: {
          backgroundColor: 'transparent',
          borderWidth: 1,
          borderColor: theme.colors.neutralGrey,
        },
        text: {
          backgroundColor: 'transparent',
          borderWidth: 0,
        },
      },
    },
  },
  buttonText: {
    textAlign: 'center',
    variants: {
      type: {
        primary: {
          color: theme.colors.background,
        },
        primaryOpacity10: {
          color: theme.colors.primary,
          ...theme.fw600,
        },
        danger: {
          color: theme.colors.neutralWhite,
        },
        outlined: {
          color: theme.colors.neutralWhite,
        },
        text: {
          color: theme.colors.neutralWhite,
        },
      },
    },
  },
}));
