import { TextStyle, View } from 'react-native';
import { createStyleSheet, mq, useStyles } from 'react-native-unistyles';
import { ThemedText } from '../ThemedText';
import { Ionicons } from '@expo/vector-icons';
import { Show } from '../Show';
import { CustomButton } from './CustomButton';
import { ModalRn } from '../Modal';
import { ReactNode } from 'react';

interface ConfirmationModalProps {
  isVisible: boolean;
  onClose: () => void;
  onCancel?: () => void;
  onConfirm: () => void;
  title: string;
  description?: string | ReactNode;
  descriptionStyle?: TextStyle;
  cancelText?: string;
  confirmText?: string;
  isLoading?: boolean;
  icon?: ReactNode;
}

export const ConfirmationModal = ({
  isVisible,
  onClose,
  onCancel,
  onConfirm,
  title,
  description,
  descriptionStyle,
  cancelText = 'Cancel',
  confirmText = 'Confirm',
  isLoading = false,
  icon,
}: ConfirmationModalProps) => {
  const { styles, theme } = useStyles(stylesheet);

  return (
    <ModalRn
      isVisible={isVisible}
      onBackdropPress={onClose}
      backdropColor={'rgba(0, 0, 0, 0.7)'}
      style={styles.modal}
      contentStyle={styles.container}
    >
      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <View style={styles.warningIcon}>
            {icon ? icon : <Ionicons name='warning' size={40} color={theme.colors.primary} />}
          </View>
        </View>

        <View style={styles.textContainer}>
          <ThemedText style={styles.title}>{title}</ThemedText>

          <Show when={!!description}>
            {typeof description === 'string' ? (
              <ThemedText style={[styles.description, descriptionStyle]}>{description}</ThemedText>
            ) : (
              description
            )}
          </Show>
        </View>

        <View style={styles.buttonContainer}>
          <CustomButton
            textType='defaultBold'
            type='primary'
            style={styles.confirmButton}
            onPress={onConfirm}
            isLoading={isLoading}
          >
            {confirmText}
          </CustomButton>

          <Show when={!!onCancel}>
            <CustomButton
              textType='defaultBold'
              type='outlined'
              style={styles.cancelButton}
              onPress={onCancel}
              disabled={isLoading}
            >
              {cancelText}
            </CustomButton>
          </Show>
        </View>
      </View>
    </ModalRn>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  modal: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
    paddingHorizontal: 24,
  },
  container: {
    backgroundColor: theme.colors.neutralCard,
    borderRadius: 32,
    paddingVertical: 48,
    paddingHorizontal: { [mq.only.width(430)]: 24, [mq.only.width('xs')]: 20 },
    width: '100%',
  },
  content: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconContainer: {
    marginBottom: 24,
  },
  warningIcon: {
    width: 72,
    height: 72,
    borderRadius: 999,
    backgroundColor: theme.colors.primaryOpacity10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  textContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 20,
    ...theme.fw700,
    color: theme.colors.neutralWhite,
    textAlign: 'center',
    lineHeight: 28,
    letterSpacing: 0,
  },
  description: {
    fontSize: 18,
    ...theme.fw400,
    color: theme.colors.neutralWhite,
    textAlign: 'center',
    lineHeight: 20,
    opacity: 0.8,
    marginTop: 20,
  },
  buttonContainer: {
    flexDirection: 'column',
    gap: 16,
    width: '100%',
  },
  cancelButton: {
    minHeight: 48,
  },
  confirmButton: {
    height: 48,
  },
}));
