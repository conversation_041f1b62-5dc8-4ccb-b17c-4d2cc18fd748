import { useRecyclingState } from '@shopify/flash-list';
import { Image, ImageProps } from 'expo-image';
import Animated from 'react-native-reanimated';

type TSource = ImageProps['source'];
type Props = {} & ImageProps;

const blurhash =
  '|rF?hV%2WCj[ayj[a|j[az_NaeWBj@ayfRayfQfQM{M|azj[azf6fQfQfQIpWXofj[ayj[j[fQayWCoeoeaya}j[ayfQa{oLj?j[WVj[ayayj[fQoff7azayj[ayj[j[ayofayayayj[fQj[ayayj[ayfjj[j[ayjuayj[';

export const ExpoImage = ({ source, ...props }: Props) => {
  const [currentSource, setCurrentSource] = useRecyclingState<TSource>(source, [source]);

  const handleLoadError = (error: any) => {
    console.warn(error, source, currentSource);
    setCurrentSource(require('@/assets/images/empty_cover.png'));
  };

  const recyclingKey =
    typeof source === 'object' && !Array.isArray(source) && source && 'uri' in source ? source.uri : `source_${source}`;

  if (!source) return null;

  return (
    <Image
      {...props}
      transition={100}
      cachePolicy='disk'
      onError={handleLoadError}
      placeholder={{ blurhash }}
      recyclingKey={recyclingKey}
      placeholderContentFit='cover'
      source={currentSource}
    />
  );
};

export const ExpoImageAnimated = Animated.createAnimatedComponent(ExpoImage);
