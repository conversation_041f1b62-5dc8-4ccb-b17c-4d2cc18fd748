import { ActivityIndicator, TouchableOpacity, TouchableOpacityProps } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { Spacer } from '../Spacer';
import { ThemedText } from '../ThemedText';

type Props = TouchableOpacityProps & {
  type?: 'default' | 'outline' | 'text';
  isLoading?: boolean;
};

export const Button = ({ type = 'default', style, children, isLoading, disabled, ...rest }: Props) => {
  const { styles } = useStyles(stylesheet);

  return (
    <TouchableOpacity
      style={[
        styles.button,
        type === 'default' ? styles.default : undefined,
        type === 'outline' ? styles.outline : undefined,
        type === 'text' ? styles.text : undefined,
        style,
        isLoading || disabled ? { opacity: 0.5 } : undefined,
      ]}
      disabled={disabled || isLoading}
      {...rest}
    >
      {isLoading && (
        <>
          <ActivityIndicator color='#000' size='small' />

          <Spacer width={16} />
        </>
      )}
      {typeof children === 'string' ? (
        <ThemedText
          style={[
            styles.buttonText,
            type === 'outline' ? styles.buttonTextOutline : undefined,
            type === 'text' ? styles.buttonTextText : undefined,
          ]}
        >
          {children}
        </ThemedText>
      ) : (
        children
      )}
    </TouchableOpacity>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  button: {
    padding: 12,
    borderRadius: 999,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  default: {
    backgroundColor: theme.colors.primary,
  },
  outline: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: theme.colors.neutralGrey,
  },
  text: {
    backgroundColor: 'transparent',
    borderWidth: 0,
  },
  buttonText: {
    color: theme.colors.background,
    fontSize: 16,
    ...theme.fw700,
  },
  buttonTextOutline: {
    color: theme.colors.neutralWhite,
  },
  buttonTextText: {
    color: theme.colors.primary,
  },
}));
