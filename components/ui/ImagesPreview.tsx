import { ImageProps } from 'expo-image';
import { router } from 'expo-router';
import { TouchableOpacity } from 'react-native';
import { ExpoImage } from './Image';

type Props = {
  images: string[];
  currentIndex?: number;
} & ImageProps;

export const ImagesPreview = ({ images, currentIndex = 0, ...props }: Props) => {
  const handleDirect = () => {
    router.push({
      pathname: '/(modal)/images',
      params: {
        urls: JSON.stringify(images),
        activeIndex: currentIndex,
      },
    });
  };

  return (
    <TouchableOpacity activeOpacity={0.7} onPress={handleDirect}>
      <ExpoImage {...props} />
    </TouchableOpacity>
  );
};
