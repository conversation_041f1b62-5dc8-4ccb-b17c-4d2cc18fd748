import React from 'react';
import { Path } from 'react-native-svg';

interface RectWithTopBorderRadiusProps {
  x?: string | number;
  y?: string | number;
  width?: string | number;
  height?: string | number;
  fill?: string;
  stroke?: string;
  strokeWidth?: string | number;
  opacity?: string | number;
  topBorderRadius?: number;
  [key: string]: any;
}

const RectWithTopBorderRadius: React.FC<RectWithTopBorderRadiusProps> = ({
  x = 0,
  y = 0,
  width = 1,
  height = 1,
  topBorderRadius = 6,
  fill = '#000000',
  stroke,
  strokeWidth,
  opacity,
  ...otherProps
}) => {
  const rectX = Number(x);
  const rectY = Number(y);
  const rectWidth = Number(width);
  const rectHeight = Number(height);
  const radius = Number(topBorderRadius);

  const pathData = `
    M ${rectX + radius} ${rectY}
    L ${rectX + rectWidth - radius} ${rectY}
    Q ${rectX + rectWidth} ${rectY} ${rectX + rectWidth} ${rectY + radius}
    L ${rectX + rectWidth} ${rectY + rectHeight}
    L ${rectX} ${rectY + rectHeight}
    L ${rectX} ${rectY + radius}
    Q ${rectX} ${rectY} ${rectX + radius} ${rectY}
    Z
  `
    .trim()
    .replace(/\s+/g, ' ');

  return <Path d={pathData} fill={fill} stroke={stroke} strokeWidth={strokeWidth} opacity={opacity} {...otherProps} />;
};

export default RectWithTopBorderRadius;
