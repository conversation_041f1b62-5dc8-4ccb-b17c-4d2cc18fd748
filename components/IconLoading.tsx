import { Icons } from '@/assets/icons';
import { View } from 'react-native';
import Animated, { useAnimatedStyle, withRepeat, withSequence, withTiming } from 'react-native-reanimated';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {};

export const IconLoading = (props: Props) => {
  const { styles } = useStyles(stylesheet);

  const iconsStyle = useAnimatedStyle(() => {
    return {
      transform: [
        {
          rotateY: withSequence(
            withTiming('180deg'),
            withRepeat(withTiming('360deg', { duration: 250 }), Infinity, true),
            withTiming('-180deg')
          ),
        },
        { scale: 0.7 },
      ],
    };
  });

  return (
    <View style={styles.container}>
      <Animated.View style={styles.icon}>
        <Animated.View style={[iconsStyle]}>
          <Icons.Rabid color='#0A0244' />
        </Animated.View>
      </Animated.View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    paddingVertical: 10,
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  icon: {
    width: 50,
    height: 50,
    borderRadius: 999,
    backgroundColor: theme.colors.primary,
    padding: 5,
    justifyContent: 'center',
    alignItems: 'center',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
}));
