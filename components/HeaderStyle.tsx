import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { Header } from './ui/Header';
import { PropsWithChildren } from 'react';
import { Image } from 'expo-image';
import Animated, { interpolate, SharedValue, useAnimatedStyle } from 'react-native-reanimated';
import { LayoutChangeEvent } from 'react-native';

type Props = {
  title: string;
  scrollY?: SharedValue<number>;
  onHeaderLayout?: (event: LayoutChangeEvent) => void;
};
const maxScroll = 40;

export const HeaderStyle = ({ title, scrollY, onHeaderLayout }: PropsWithChildren<Props>) => {
  const { styles } = useStyles(stylesheet);

  const animatedStyle = useAnimatedStyle(() => {
    if (scrollY?.value === undefined) return {};

    const translateX = interpolate(Math.min(scrollY.value, maxScroll), [0, maxScroll], [0, 40], 'clamp');

    return {
      transform: [
        {
          translateX,
        },
        {
          translateY: -4,
        },
      ],
    };
  });

  const containerAnimatedStyle = useAnimatedStyle(() => {
    if (scrollY?.value === undefined) return {};
    const translateY = -Math.min(scrollY.value, maxScroll);

    return {
      transform: [{ translateY }],
    };
  });

  const headerAnimatedStyle = useAnimatedStyle(() => {
    if (scrollY?.value === undefined) return {};

    const translateY = Math.min(scrollY.value, 40);

    return {
      transform: [{ translateY }],
    };
  });

  return (
    <Animated.View style={[styles.container, containerAnimatedStyle]} onLayout={onHeaderLayout}>
      <Image
        source={require('@/assets/images/header_style.png')}
        style={styles.bgImage}
        cachePolicy={'disk'}
        priority={'high'}
      />

      <Animated.View style={[headerAnimatedStyle]}>
        <Header isBack title='' />
      </Animated.View>

      <Animated.View>
        <Animated.Text style={[animatedStyle, styles.title]}>{title}</Animated.Text>
      </Animated.View>
    </Animated.View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  bgImage: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    objectFit: 'cover',
  },
  container: {
    paddingTop: rt.insets.top + 15,
    paddingHorizontal: 24,
    paddingBottom: 50,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1,
  },
  title: {
    color: theme.colors.neutralWhite,
    fontSize: 24,
    ...theme.fw600,
    position: 'absolute',
  },
}));
