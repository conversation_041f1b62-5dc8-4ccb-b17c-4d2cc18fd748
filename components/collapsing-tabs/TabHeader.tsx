import { useCollapsingTabsContext } from '@/contexts/app.context';
import React, { memo } from 'react';
import { Pressable, ViewStyle } from 'react-native';
import Animated, { LinearTransition } from 'react-native-reanimated';

type Props = {
  renderHeader: React.ReactNode | (() => React.JSX.Element);
  headerContainerStyle?: ViewStyle;
};

export const TabHeader = memo(({ renderHeader, headerContainerStyle: headerContainerStyleOvr }: Props) => {
  const { headerContainerStyle, handleHeaderLayout } = useCollapsingTabsContext();

  if (!renderHeader) return null;

  return (
    <Animated.View
      layout={LinearTransition}
      onLayout={handleHeaderLayout}
      pointerEvents='box-none'
      style={[headerContainerStyle, headerContainerStyleOvr]}
    >
      <Pressable pointerEvents='box-none'>{renderHeader}</Pressable>
    </Animated.View>
  );
});
