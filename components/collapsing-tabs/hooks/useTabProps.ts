import React, { Children, useMemo } from 'react';
import { TabsWithProps } from '../TabItem';

type ChildrenWithProps<T> = React.ReactElement<T> | React.ReactElement<T>[];

export type TabOptionValue = TabsWithProps & { index: number };

export const useTabProps = (children: ChildrenWithProps<TabsWithProps>, tabType: Function) => {
  const options = useMemo(() => {
    let tabOptions = new Map<string, TabOptionValue>();

    Children.forEach(children, (child, index) => {
      if (!child) return;

      if (child.type !== tabType)
        throw new Error('Container children must be wrapped in a <TabItem>{children}</TabItem> component');

      if (React.isValidElement<TabsWithProps>(child)) {
        const { tabName, ...props } = child.props;
        tabOptions.set(tabName, {
          index,
          tabName,
          ...props,
        });
      }
    });

    return tabOptions;
  }, [children, tabType]);

  const tabNamesArray = useMemo(() => {
    return Array.from(options.keys());
  }, [options]);

  return [options, tabNamesArray] as const;
};
