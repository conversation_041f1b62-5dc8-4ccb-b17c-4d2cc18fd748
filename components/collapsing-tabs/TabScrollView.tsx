import { useCollapsingTabsContext } from '../../contexts/app.context';
import Animated, { SharedValue } from 'react-native-reanimated';
import { useAnimateList } from './useAnimateList';
import { PropsWithChildren, Ref, useCallback } from 'react';
import { ScrollViewAnimate, ScrollViewAnimateProps } from '../ScrollViewAnimate';

type Props = {} & ScrollViewAnimateProps;

export const TabScrollView = ({ children, ...props }: PropsWithChildren<Props>) => {
  const { sharedProps, pullPosition } = useCollapsingTabsContext();
  const { ref, scrollHandler } = useAnimateList();
  const { contentContainerStyle, scrollIndicatorInsets, scrollEventThrottle } = sharedProps;

  const handlePullPositionChange = useCallback(
    (position: SharedValue<number>) => {
      'worklet';
      pullPosition.value = position.value;
    },
    [pullPosition]
  );

  return (
    <ScrollViewAnimate
      ref={ref as Ref<Animated.ScrollView>}
      {...props}
      onScroll={scrollHandler}
      {...sharedProps}
      contentContainerStyle={
        {
          ...(contentContainerStyle && typeof contentContainerStyle === 'object' ? contentContainerStyle : {}),
          ...(props.contentContainerStyle && typeof props.contentContainerStyle === 'object'
            ? props.contentContainerStyle
            : {}),
        } as any
      }
      scrollIndicatorInsets={{ ...props.scrollIndicatorInsets, ...scrollIndicatorInsets }}
      scrollEventThrottle={scrollEventThrottle || props.scrollEventThrottle || 16}
      onPullPositionChange={handlePullPositionChange}
    >
      {children}
    </ScrollViewAnimate>
  );
};
