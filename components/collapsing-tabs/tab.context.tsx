import { createSafeContext } from '@/utils/create-safe-context';
import { RefTabItem } from './TabItem';
import { RefObject } from 'react';

type TabContextProps = {
  tabName: string;
  tabNames: string[];
  label?: string;
  refTabItem?: RefObject<RefTabItem>;
};

export const [TabContextProvider, useTabContext] = createSafeContext<TabContextProps>(
  'TabContextProvider component was not found in tree'
);
