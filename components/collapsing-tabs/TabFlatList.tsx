import { useCollapsingTabsContext } from '@/contexts/app.context';
import { useAnimateList } from './useAnimateList';
import Animated, { AnimatedRef, FlatListPropsWithLayout } from 'react-native-reanimated';
import { FlatList } from 'react-native';

type Props<T> = {} & FlatListPropsWithLayout<T>;

export const TabFlatList = <T,>(props: Props<T>) => {
  const { sharedProps } = useCollapsingTabsContext();
  const { ref, scrollHandler } = useAnimateList();
  const { contentContainerStyle, scrollIndicatorInsets, scrollEventThrottle } = sharedProps;

  return (
    <Animated.FlatList
      {...props}
      ref={ref as AnimatedRef<FlatList>}
      onScroll={scrollHandler}
      {...sharedProps}
      contentContainerStyle={[contentContainerStyle, props.contentContainerStyle]}
      scrollIndicatorInsets={{ ...props.scrollIndicatorInsets, ...scrollIndicatorInsets }}
      scrollEventThrottle={scrollEventThrottle || props.scrollEventThrottle || 16}
    />
  );
};
