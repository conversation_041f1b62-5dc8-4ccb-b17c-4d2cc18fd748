import { memo, PropsWithChildren } from 'react';
import { TabContext } from './TabContext';
import { TabNavigator, TabBarProps } from './TabNavigator';
import { StyleSheet, ViewStyle } from 'react-native';
import Animated from 'react-native-reanimated';
import { TabsWithProps } from './TabItem';

type Props = {
  renderHeader?: React.ReactNode | (() => React.JSX.Element);
  containerStyle?: ViewStyle;
  headerContainerStyle?: ViewStyle;
  initHeaderHeight?: number;
  minHeaderHeight?: number;
  initialTabIndex?: number;
  children: React.ReactElement<TabsWithProps> | React.ReactElement<TabsWithProps>[];
  renderCustomTabBar?: (props: TabBarProps) => React.ReactNode;
};

export const TabContainer = memo(
  ({
    children,
    renderHeader,
    containerStyle,
    headerContainerStyle,
    initHeaderHeight,
    minHeaderHeight,
    initialTabIndex,
    renderCustomTabBar,
  }: PropsWithChildren<Props>) => {
    return (
      <TabContext
        initHeaderHeight={initHeaderHeight}
        minHeaderHeight={minHeaderHeight}
        initialTabIndex={initialTabIndex}
      >
        <Animated.View style={[styles.container, containerStyle]}>
          <TabNavigator
            headerContainerStyle={headerContainerStyle}
            renderHeader={renderHeader}
            renderCustomTabBar={renderCustomTabBar}
          >
            {children}
          </TabNavigator>
        </Animated.View>
      </TabContext>
    );
  }
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
