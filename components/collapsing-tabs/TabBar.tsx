import { FC, useState } from 'react';
import Animated, { useAnimatedStyle } from 'react-native-reanimated';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { ThemedText } from '../ThemedText';
import { useCollapsingTabsContext } from '@/contexts/app.context';
import { Pressable } from 'react-native';
import { TabOptionValue } from './hooks/useTabProps';

type Props = {
  onTabPress?: (index: number) => void;
  tabNamesArray: string[];
  tabOptions: Map<string, TabOptionValue>;
};

export const TabBar: FC<Props> = ({ onTabPress, tabOptions, ...props }) => {
  const { tabNamesArray } = props;
  const { tabIndex, progressIndicator, refPager } = useCollapsingTabsContext();
  const { styles } = useStyles(stylesheet);

  const tabName = tabNamesArray[tabIndex];

  const [tabLayouts, setTabLayouts] = useState<{ x: number; width: number }[]>([]);

  const indicatorStyle = useAnimatedStyle(() => {
    const offsets = progressIndicator.value;
    const index = Math.floor(offsets);
    const progress = offsets - index;
    if (tabLayouts?.length === 0) return {};

    const cur = tabLayouts?.[index];
    const next = tabLayouts?.[index + 1] || cur;

    const left = cur.x + (next.x - cur?.x) * progress;
    const width = cur.width + (next.width - cur.width) * progress;

    return {
      left,
      width,
    };
  });

  const handleTabPress = (index: number) => {
    onTabPress?.(index);
    refPager.current?.setPage(index);
  };

  return (
    <Animated.View style={styles.tabbar}>
      {tabNamesArray.map((name, i) => {
        const isTabActive = name === tabName;
        const tabLabel = tabOptions.get(name)?.label || name;

        return (
          <Animated.View
            key={name}
            style={styles.tabItem}
            onLayout={({ nativeEvent }) => {
              ('worklet');
              const { x, width } = nativeEvent.layout;
              setTabLayouts((prev) => {
                const newTabLayouts = [...prev];
                newTabLayouts[i] = { x, width };
                return newTabLayouts;
              });
            }}
          >
            <Pressable onPress={() => handleTabPress(i)}>
              <ThemedText style={[styles.tabText, isTabActive ? styles.activeTabText : styles.inactiveTabText]}>
                {tabLabel}
              </ThemedText>
            </Pressable>
          </Animated.View>
        );
      })}

      <Animated.View style={[styles.indicator, indicatorStyle]} />
    </Animated.View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  tabbar: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.neutralBackground,
    gap: 20,
    width: '100%',
    height: 48,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.whiteOpacity10,
  },
  tabItem: {
    paddingHorizontal: 16,
    minWidth: 80,
  },
  tabText: {
    fontSize: 16,
    ...theme.fw600,
    textAlign: 'center',
  },
  activeTabText: {
    color: theme.colors.neutralWhite,
  },
  inactiveTabText: {
    color: theme.colors.whiteOpacity56,
  },
  indicator: {
    position: 'absolute',
    bottom: 0,
    height: 2,
    backgroundColor: theme.colors.neutralWhite,
    left: 0,
    borderRadius: 999,
  },
}));
