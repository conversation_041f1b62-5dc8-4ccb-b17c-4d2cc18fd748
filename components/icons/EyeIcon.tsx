import Svg, { Path } from 'react-native-svg';

interface EyeIconProps {
  width?: number;
  height?: number;
  color?: string;
}

export const EyeIcon = ({ width = 24, height = 24, color = '#FFFFFF', ...props }: EyeIconProps) => (
  <Svg width={width} height={height} viewBox='0 0 24 24' fill='none' {...props}>
    <Path
      d='M9.76045 14.3668C9.18545 13.7928 8.83545 13.0128 8.83545 12.1378C8.83545 10.3848 10.2474 8.9718 11.9994 8.9718C12.8664 8.9718 13.6644 9.3228 14.2294 9.8968'
      stroke={color}
      strokeWidth={1.2}
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <Path
      d='M15.1046 12.6989C14.8726 13.9889 13.8566 15.0069 12.5676 15.2409'
      stroke={color}
      strokeWidth={1.2}
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <Path
      d='M6.65451 17.4723C5.06751 16.2263 3.72351 14.4063 2.74951 12.1373C3.73351 9.85829 5.08651 8.02829 6.68351 6.77229C8.27051 5.51629 10.1015 4.83429 11.9995 4.83429C13.9085 4.83429 15.7385 5.52629 17.3355 6.79129'
      stroke={color}
      strokeWidth={1.2}
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <Path
      d='M19.4475 8.99078C20.1355 9.90478 20.7405 10.9598 21.2495 12.1368C19.2825 16.6938 15.8065 19.4388 11.9995 19.4388C11.1365 19.4388 10.2855 19.2988 9.46753 19.0258'
      stroke={color}
      strokeWidth={1.2}
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <Path
      d='M19.8868 4.24957L4.11279 20.0236'
      stroke={color}
      strokeWidth={1.2}
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  </Svg>
);

export const EyeOpenIcon = ({ width = 24, height = 24, color = '#FFFFFF', ...props }: EyeIconProps) => (
  <Svg width={width} height={height} viewBox='0 0 24 24' fill='none' {...props}>
    <Path
      d='M6.65451 17.4723C5.06751 16.2263 3.72351 14.4063 2.74951 12.1373C3.73351 9.85829 5.08651 8.02829 6.68351 6.77229C8.27051 5.51629 10.1015 4.83429 11.9995 4.83429C13.9085 4.83429 15.7385 5.52629 17.3355 6.79129C18.9215 8.04729 20.2655 9.87729 21.2495 12.1368C19.2825 16.6938 15.8065 19.4388 11.9995 19.4388C8.19251 19.4388 4.71651 16.6938 2.74951 12.1373'
      stroke={color}
      strokeWidth={1.2}
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <Path
      d='M11.9995 8.9718C13.7525 8.9718 15.1645 10.3848 15.1645 12.1378C15.1645 13.8908 13.7525 15.3038 11.9995 15.3038C10.2465 15.3038 8.83451 13.8908 8.83451 12.1378C8.83451 10.3848 10.2465 8.9718 11.9995 8.9718Z'
      stroke={color}
      strokeWidth={1.2}
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  </Svg>
);
