import React, { useRef, useState } from 'react';
import { GestureResponderEvent, Pressable, View, ViewStyle } from 'react-native';
import { UnistylesRuntime, useStyles } from 'react-native-unistyles';
import { dropdownStyles } from './dropdownStyles';
import { ModalRn } from '../Modal';

interface DropdownMenuProps {
  visible: boolean;
  handleClose: () => void;
  handleOpen: () => void;
  trigger: React.ReactNode;
  children: React.ReactNode;
  triggerStyle?: ViewStyle;
  containerStyle?: ViewStyle;
}

const rt = UnistylesRuntime;

export const DropdownMenu: React.FC<DropdownMenuProps> = ({
  visible,
  handleOpen,
  handleClose,
  trigger,
  children,
  triggerStyle,
  containerStyle,
}) => {
  const triggerRef = useRef<View>(null);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const { styles } = useStyles(dropdownStyles);
  const [contentHeight, setContentHeight] = useState(0);

  const handlePressOpen = (e: GestureResponderEvent) => {
    const { pageX, pageY, locationX, locationY } = e.nativeEvent;
    e.target.measureInWindow((x, y, width, height) => {
      const screenHeight = rt.screen.height;

      const isPlaceBottom = screenHeight - pageY + height > 2 * contentHeight;

      if (isPlaceBottom) {
        setPosition({ x: pageX - locationX + width, y: pageY - locationY + height });
      } else {
        setPosition({ x: pageX - locationX + width, y: pageY - locationY - height - contentHeight });
      }
      handleOpen();
    });
  };

  return (
    <View style={[styles.container, containerStyle]}>
      <Pressable onPress={handlePressOpen} style={triggerStyle}>
        <View ref={triggerRef}>{trigger}</View>
      </Pressable>

      <View
        onLayout={(e) => {
          setContentHeight(e.nativeEvent.layout.height);
        }}
        style={[
          styles.menu,
          {
            opacity: 0,
            pointerEvents: 'none',
            zIndex: -1,
          },
        ]}
      >
        {children}
      </View>

      <ModalRn
        isVisible={visible}
        backdropColor='transparent'
        onBackdropPress={handleClose}
        style={styles.modalOverlay}
      >
        <View
          style={[
            styles.menu,
            {
              top: position.y,
              left: position.x,
            },
            {
              transform: [{ translateX: '-100%' }],
            },
          ]}
        >
          {children}
        </View>
      </ModalRn>
    </View>
  );
};
