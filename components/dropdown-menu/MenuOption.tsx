import { ReactNode } from 'react';
import { TouchableOpacity, TouchableOpacityProps } from 'react-native';
import { useStyles } from 'react-native-unistyles';
import { dropdownStyles } from './dropdownStyles';

type Props = {
  onSelect: () => void;
  children: ReactNode;
} & TouchableOpacityProps;

export const MenuOption = ({ onSelect, children, style, ...props }: Props) => {
  const { styles } = useStyles(dropdownStyles);

  return (
    <TouchableOpacity onPress={onSelect} style={[styles.menuOption, style]} {...props}>
      {children}
    </TouchableOpacity>
  );
};
