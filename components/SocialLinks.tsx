import { TSocialLinks } from '@/apis/podcast';
import { Icons } from '@/assets/icons';
import { PropsWithChildren } from 'react';
import { Linking, ScrollView, TouchableOpacity } from 'react-native';
import { Show } from './Show';

type Props = {
  links: TSocialLinks;
};

export const SocialLinks = ({ links }: Props) => {
  return (
    <ScrollView horizontal style={{ gap: 16 }} contentContainerStyle={{ gap: 12 }}>
      <Show when={!!links.facebook}>
        <SocialLinkItem link={links.facebook!}>
          <Icons.FacebookIcon size={36} />
        </SocialLinkItem>
      </Show>

      <Show when={!!links.instagram}>
        <SocialLinkItem link={links.instagram!}>
          <Icons.InstagramIcon size={36} />
        </SocialLinkItem>
      </Show>

      <Show when={!!links.youtube}>
        <SocialLinkItem link={links.youtube!}>
          <Icons.YoutubeIcon size={36} />
        </SocialLinkItem>
      </Show>

      <Show when={!!links.linkedin}>
        <SocialLinkItem link={links.linkedin!}>
          <Icons.LinkedinIcon size={36} />
        </SocialLinkItem>
      </Show>

      <Show when={!!links.tiktok}>
        <SocialLinkItem link={links.tiktok!}>
          <Icons.TiktokIcon size={36} />
        </SocialLinkItem>
      </Show>

      <Show when={!!links.patreon}>
        <SocialLinkItem link={links.patreon!}>
          <Icons.PatronIcon size={36} />
        </SocialLinkItem>
      </Show>

      <Show when={!!links.twitch}>
        <SocialLinkItem link={links.twitch!}>
          <Icons.TwitchIcon size={36} />
        </SocialLinkItem>
      </Show>
    </ScrollView>
  );
};

const SocialLinkItem = ({ link, children }: PropsWithChildren<{ link: string }>) => {
  const onPress = () => {
    if (!link) return;

    Linking.openURL(link);
  };

  return (
    <TouchableOpacity onPress={onPress} activeOpacity={0.7}>
      {children}
    </TouchableOpacity>
  );
};
