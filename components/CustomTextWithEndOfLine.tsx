import { useCallback, useState } from 'react';
import { NativeSyntheticEvent, TextLayoutEventData, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { ThemedText } from './ThemedText';

type Props = {
  text: string;
  textEndOfLine?: string;
  numberOfLines: number;
};

export const CustomTextWithEndOfLine = ({ text, textEndOfLine = '', numberOfLines }: Props) => {
  const { styles } = useStyles(stylesheet);
  const [textTruncateShow, setTextTruncate] = useState<string>();

  const endOfTextWithEllipsis = '...' + textEndOfLine;

  const onTextLayoutClone = useCallback(
    (e: NativeSyntheticEvent<TextLayoutEventData>) => {
      const { lines } = e.nativeEvent;

      // dont need to show more
      if (!numberOfLines || !lines || lines.length <= numberOfLines) {
        setTextTruncate(text + textEndOfLine);
        return;
      }

      // need to show more
      let textTruncateWillShow = '';
      lines.forEach((line, index) => {
        if (index <= numberOfLines - 1) {
          textTruncateWillShow += line.text;
        }
      });

      textTruncateWillShow = textTruncateWillShow.trim();

      if (lines.length > numberOfLines && textEndOfLine.length > 0) {
        const lastSpaceIndex = textTruncateWillShow.lastIndexOf(' ');

        textTruncateWillShow = textTruncateWillShow.slice(0, lastSpaceIndex - endOfTextWithEllipsis.length);
        textTruncateWillShow += endOfTextWithEllipsis;
      }

      setTextTruncate(textTruncateWillShow?.trim());
    },
    [numberOfLines, text, textEndOfLine, endOfTextWithEllipsis]
  );

  return (
    <View style={styles.container}>
      {/** Raw text */}
      <View style={{ position: 'absolute', width: '100%', opacity: 0, zIndex: -1 }}>
        <ThemedText type='tinyMedium' style={[styles.text]} onTextLayout={onTextLayoutClone}>
          {text + endOfTextWithEllipsis}
        </ThemedText>
      </View>

      {/** Truncate text */}
      <ThemedText type='tinyMedium' style={styles.text} numberOfLines={numberOfLines}>
        {textTruncateShow}
      </ThemedText>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flex: 1,
  },
  text: {
    color: theme.colors.whiteOpacity56,
    lineHeight: 18,
    alignItems: 'center',
  },
  endOfLineText: {
    color: theme.colors.whiteOpacity56,
    lineHeight: 18,
    alignItems: 'center',
  },
  endOfLineBox: {
    backgroundColor: theme.colors.neutralBackground,
    position: 'absolute',
  },
}));
