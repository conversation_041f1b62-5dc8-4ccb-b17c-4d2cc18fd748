import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { ThemedText } from './ThemedText';

type Props = {
  tagName: string;
  size: 20 | 28 | 32;
};

const Tag = (props: Props) => {
  const { tagName, size } = props;

  const { styles } = useStyles(stylesheet, {
    size,
  });

  return (
    <View style={styles.box}>
      <View style={[styles.container, { height: size }]}>
        <ThemedText style={styles.text}>{tagName}</ThemedText>
      </View>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  box: {
    flexDirection: 'row',
  },
  container: {
    paddingHorizontal: 8,
    borderRadius: 16,
    backgroundColor: theme.colors.primaryOpacity10,
    variants: {
      size: {
        20: {
          height: 20,
          paddingHorizontal: 8,
        },
        28: {
          height: 28,
          paddingHorizontal: 8,
          paddingVertical: 4,
        },
        32: {
          height: 32,
          paddingHorizontal: 12,
          paddingVertical: 6,
        },
      },
    },
  },
  text: {
    fontSize: 12,
    ...theme.fw500,
    color: theme.colors.primary,
    variants: {
      size: {
        20: {
          fontSize: 8,
          lineHeight: 20,
          ...theme.fw500,
        },
        28: {
          fontSize: 10,
          lineHeight: 20,
          ...theme.fw500,
        },
        32: {
          fontSize: 12,
          lineHeight: 20,
          ...theme.fw500,
        },
      },
    },
  },
}));

export default Tag;
