import { ConfirmationModal } from './ui/ConfirmationModal';
import { View } from 'react-native';
import { Spacer } from './Spacer';
import { ThemedText } from './ThemedText';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { useUserStore } from '@/store/user';

type Props = {
  isVisible: boolean;
};

export const WarningRestrictModal = ({ isVisible }: Props) => {
  const { styles } = useStyles(stylesheet);
  const setIsShowWarningRestricted = useUserStore.use.setIsShowWarningRestricted();

  const handleClose = () => {
    setIsShowWarningRestricted(false);
  };

  return (
    <ConfirmationModal
      isVisible={isVisible}
      onClose={handleClose}
      onConfirm={handleClose}
      confirmText='Dismiss'
      title='Your Account Has Been Restricted'
      description={
        <View style={styles.contentContainer}>
          <Spacer height={20} />

          <ThemedText style={styles.textDescription} type='default'>
            Your account is currently restricted and can only be used in view-only mode within the community.
          </ThemedText>

          <Spacer height={20} />

          <ThemedText style={styles.textDescription} type='default'>
            To lift this restriction, please contact us at{' '}
            <ThemedText style={styles.email} type='default'>
              <EMAIL>
            </ThemedText>
          </ThemedText>
        </View>
      }
    />
  );
};

const stylesheet = createStyleSheet((theme) => ({
  contentContainer: {
    flexDirection: 'column',
    justifyContent: 'center',
  },
  textDescription: {
    textAlign: 'center',
    color: theme.colors.neutralLightGrey,
    ...theme.fw400,
  },
  email: {
    color: theme.colors.primary,
    ...theme.fw500,
  },
}));
