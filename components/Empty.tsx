import { ReactNode } from 'react';
import { ImageStyle, TextStyle, View, ViewStyle } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { Show } from './Show';
import { ThemedText } from './ThemedText';
import { Image } from 'expo-image';

type Props = {
  emptyText?: string | ReactNode;
  type: 'like' | 'check' | 'save' | 'star' | 'post' | 'referral' | 'upvote' | 'follow';
  containerStyle?: ViewStyle;
  imageStyle?: ImageStyle;
  textType?: 'default' | 'subtitleSemiBold' | 'defaultSemiBold';
  textStyle?: TextStyle;
};

export const Empty = ({ emptyText, type, containerStyle, imageStyle, textType, textStyle }: Props) => {
  const { styles } = useStyles(stylesheet);

  let source: number | string | undefined = undefined;

  switch (type) {
    case 'like':
      source = require('@/assets/images/empty_like.png');
      break;
    case 'check':
      source = require('@/assets/images/empty_check.png');
      break;
    case 'save':
      source = require('@/assets/images/empty_save.png');
      break;
    case 'star':
      source = require('@/assets/images/empty_star.png');
      break;
    case 'post':
      source = require('@/assets/images/empty_post.png');
      break;
    case 'upvote':
      source = require('@/assets/images/empty_upvote.png');
      break;
    case 'referral':
      source = require('@/assets/images/empty_referral.png');
      break;
    case 'follow':
      source = require('@/assets/images/empty_followers.png');
      break;

    default:
      break;
  }

  return (
    <View style={[styles.container, containerStyle]}>
      <Show when={!!source}>
        <Image source={source} style={[styles.image, imageStyle]} />
      </Show>

      <Show when={!!emptyText}>
        {typeof emptyText === 'string' ? (
          <ThemedText type={textType} style={[styles.text, textStyle]}>
            {emptyText}
          </ThemedText>
        ) : (
          emptyText
        )}
      </Show>
    </View>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    width: '100%',
  },
  image: {
    width: '100%',
    height: 282,
    objectFit: 'contain',
  },
  text: {
    ...theme.fw700,
    fontSize: 20,
    lineHeight: 32,
    color: theme.colors.neutralWhite,
    textAlign: 'center',
  },
}));
