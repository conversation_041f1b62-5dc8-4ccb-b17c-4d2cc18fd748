import { PropsWithChildren } from 'react';
import { SafeAreaView, SafeAreaViewProps } from 'react-native-safe-area-context';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {} & SafeAreaViewProps;

const Container = ({ style, children }: PropsWithChildren<Props>) => {
  const { styles } = useStyles(stylesheet);

  return <SafeAreaView style={[styles.container, style]}>{children}</SafeAreaView>;
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    backgroundColor: theme.colors.neutralBackground,
  },
}));

export default Container;
