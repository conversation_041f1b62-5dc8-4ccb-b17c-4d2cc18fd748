import { ISource } from '@/apis/params';
import { router } from 'expo-router';
import { PropsWithChildren } from 'react';
import { TouchableOpacity, ViewStyle } from 'react-native';

type Props = {
  userId: string | number;
  userType?: ISource;
  style?: ViewStyle;
};

export const UserProfileTouch = ({ children, userId, userType, style }: PropsWithChildren<Props>) => {
  const handleDirect = () => {
    if (userType === 'podchaser') return;
    router.push(`/(app)/${userId}`);
  };

  return (
    <TouchableOpacity
      activeOpacity={0.7}
      disabled={!userType || userType === 'podchaser'}
      onPress={handleDirect}
      style={style}
    >
      {children}
    </TouchableOpacity>
  );
};
