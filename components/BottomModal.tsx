import { PropsWithChildren } from 'react';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { ModalRn } from './Modal';
import { View } from 'react-native';

interface BottomModalProps {
  isVisible: boolean;
  onClose: () => void;
}

const BottomModal = ({ isVisible, onClose, children }: PropsWithChildren<BottomModalProps>) => {
  const { styles } = useStyles(stylesheet);

  return (
    <ModalRn
      isVisible={isVisible}
      animationType='slide'
      onBackdropPress={onClose}
      style={styles.modal}
      backdropColor='rgba(0, 0, 0, 0.7)'
    >
      <View style={styles.container}>
        <View style={styles.contentContainer}>{children}</View>
      </View>
    </ModalRn>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  modal: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  contentContainer: {
    backgroundColor: theme.colors.neutralCard,
    borderRadius: 40,
    width: '100%',
  },
  container: {
    paddingHorizontal: 24,
    marginBottom: 24,
    width: '100%',
  },
}));

export default BottomModal;
