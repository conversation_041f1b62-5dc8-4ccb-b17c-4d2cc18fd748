import { Icons } from '@/assets/icons';
import StarRating, { StarIconProps } from 'react-native-star-rating-widget';
import { useEffect, useState } from 'react';

type Props = {
  rating: number;
  onChange?: (rating: number) => void;
  size?: number;
  gap?: number;
  enabled?: boolean;
  maxStars?: number;
};

export const RateStar = ({
  rating = 0,
  onChange,
  size = 48,
  gap = 0,
  maxStars = 5,
  enabled = false,
  ...props
}: Props) => {
  const [ratingValue, setRatingValue] = useState(0);

  const handleRating = (rating: number) => {
    if (!enabled) return;
    setRatingValue(rating);
    onChange?.(rating);
  };

  useEffect(() => {
    const decimal = rating % 1;
    let roundedRating;

    if (decimal < 0.25) {
      roundedRating = Math.floor(rating);
    } else if (decimal >= 0.25 && decimal < 0.75) {
      roundedRating = Math.floor(rating) + 0.5;
    } else {
      roundedRating = Math.ceil(rating);
    }

    setRatingValue(roundedRating);
  }, [rating]);

  const StarIcon = ({ color, size, type }: StarIconProps) => {
    if (type === 'empty') {
      return <Icons.StarRateOutline fill={color} width={size} height={size} />;
    }

    if (type === 'half') {
      return <Icons.StarRateHalf fill={color} width={size} height={size} />;
    }

    return <Icons.StarRateFill fill={color} width={size} height={size} />;
  };

  return (
    <StarRating
      starSize={size}
      rating={ratingValue}
      onChange={handleRating}
      StarIconComponent={StarIcon}
      color='transparent'
      maxStars={maxStars}
      animationConfig={{
        scale: 1.2,
      }}
      starStyle={{ marginHorizontal: gap / 2 }}
      {...props}
    />
  );
};
