import { View, ViewStyle } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  style?: ViewStyle;
};

export const Divider = ({ style }: Props) => {
  const { styles } = useStyles(stylesheet);

  return <View style={[styles.divider, style]} />;
};

const stylesheet = createStyleSheet((theme) => ({
  divider: {
    height: 1,
    backgroundColor: theme.colors.whiteOpacity10,
  },
}));
