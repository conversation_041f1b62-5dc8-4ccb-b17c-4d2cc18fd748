import { env } from '@/utils/const';
import { request } from '../axios';
import { IAddMultipleCategoriesParams, ICategoriesResponse, ICategoryQuery, ICategoryResponse } from './types';
import { Image } from 'expo-image';

export const getAllCategoriesRequest = async (): Promise<ICategoryResponse[]> => {
  const { data } = await request({
    url: `${env.API_VERSION}/category?page=1&limit=999&isOnboarding=true`,
    method: 'GET',
  });

  return data.data;
};

export const getCategoriesRequest = async (params: ICategoryQuery): Promise<ICategoriesResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/category`,
    method: 'GET',
    params,
  });

  const categories = data?.data as ICategoriesResponse['data'];

  categories?.map((category) => {
    const categoryImage = category?.imageUrl;
    if (categoryImage) Image.prefetch(categoryImage);
  });

  return data;
};

export const getUserCategoriesRequest = async (): Promise<ICategoryResponse[]> => {
  const { data } = await request({
    url: `${env.API_VERSION}/category/favorites?page=1&limit=999`,
    method: 'GET',
  });

  return data.data;
};

export const addMultipleCategoriesToFavorite = async (
  params: IAddMultipleCategoriesParams
): Promise<ICategoryResponse[]> => {
  const { data } = await request({
    url: `${env.API_VERSION}/category/favorites/bulk`,
    method: 'POST',
    data: params,
  });

  return data.data;
};
