import { IPaginationResponse } from '../params';

export interface ICategoryQuery {
  search?: string;
  limit: number;
  page: number;
  isTrending?: boolean;
}

export interface ICategoryResponse {
  id: string;
  name: string;
  description: any;
  slug: string;
  numOfOrder: number;
  isActive: boolean;
  totalPodcasts: number;
  imageUrl: string;
}

export interface IAddMultipleCategoriesParams {
  categoryIds: number[];
}

export interface ICategoriesResponse extends IPaginationResponse<ICategoryResponse> {}
