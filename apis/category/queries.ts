import queryKeys from '@/utils/queryKeys';
import { UseQueryOptions, useInfiniteQuery, useQuery } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { getAllCategoriesRequest, getCategoriesRequest } from './requests';
import { ICategoriesResponse, ICategoryQuery, ICategoryResponse } from './types';

export const useGetAllCategoriesQuery = (options?: UseQueryOptions<any, AxiosError, ICategoryResponse[], any>) => {
  return useQuery({
    queryKey: ['all-categories'],
    queryFn: getAllCategoriesRequest,
    ...options,
  });
};

export const useGetCategoriesQuery = (
  params: ICategoryQuery,
  options?: UseQueryOptions<ICategoriesResponse, AxiosError, ICategoriesResponse, any>
) => {
  return useQuery({
    queryKey: queryKeys.categories.categories(params),
    queryFn: () => getCategoriesRequest(params),
    ...options,
  });
};

export const useGetCategoriesInfiniteQuery = (params: ICategoryQuery) => {
  return useInfiniteQuery({
    initialPageParam: 1,
    refetchOnMount: true,
    queryKey: queryKeys.categories.categoriesInfinite(params),
    queryFn: ({ pageParam }) => getCategoriesRequest({ ...params, page: pageParam }),
    getNextPageParam: (lastPage) => {
      return lastPage.meta.totalPages > lastPage.meta.currentPage ? lastPage.meta.currentPage + 1 : undefined;
    },
    getPreviousPageParam: (firstPage) => {
      return firstPage.meta.currentPage > 1 ? firstPage.meta.currentPage - 1 : undefined;
    },
  });
};
