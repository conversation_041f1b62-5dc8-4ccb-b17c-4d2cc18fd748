import { useInfiniteQuery, QueryOptions, InfiniteData } from '@tanstack/react-query';
import { IGetFavoritesRequest, IGetFavoritePodcastsResponse, IGetFavoriteEpisodesResponse } from './types';
import { getFavoritePodcastsRequest, getFavoriteEpisodesRequest } from './requests';
import queryKeys from '../../utils/queryKeys';

// Infinite Favorite Podcasts Query
export const useGetFavoritePodcastsInfiniteQuery = (
  params: Omit<IGetFavoritesRequest, 'page'> = {},
  options?: QueryOptions<
    IGetFavoritePodcastsResponse,
    Error,
    InfiniteData<IGetFavoritePodcastsResponse, number>,
    any,
    number
  >
) => {
  return useInfiniteQuery({
    queryKey: queryKeys.favorites.podcastsInfinite(params),
    queryFn: ({ pageParam = 1 }) => getFavoritePodcastsRequest({ ...params, page: pageParam }),
    getNextPageParam: (lastPage) => {
      return lastPage.pagination.totalPages > lastPage.pagination.page ? lastPage.pagination.page + 1 : undefined;
    },
    initialPageParam: 1,
    refetchOnMount: true,
    ...options,
  });
};

// Infinite Favorite Episodes Query
export const useGetFavoriteEpisodesInfiniteQuery = (
  params: Omit<IGetFavoritesRequest, 'page'> = {},
  options?: QueryOptions<
    IGetFavoriteEpisodesResponse,
    Error,
    InfiniteData<IGetFavoriteEpisodesResponse, number>,
    any,
    number
  >
) => {
  return useInfiniteQuery({
    queryKey: queryKeys.favorites.episodesInfinite(params),
    queryFn: ({ pageParam = 1 }) => getFavoriteEpisodesRequest({ ...params, page: pageParam as number }),
    getNextPageParam: (lastPage: any, allPages = []) => {
      return lastPage.pagination.totalPages > allPages.length ? allPages.length + 1 : undefined;
    },
    initialPageParam: 1,
    staleTime: 1000 * 60 * 5,
    refetchOnMount: true,
    ...options,
  });
};
