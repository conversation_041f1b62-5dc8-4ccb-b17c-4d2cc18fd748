import { useMutation, useQueryClient, UseMutationOptions } from '@tanstack/react-query';
import {
  addFavoritePodcastRequest,
  updateFavoritePodcastOrderRequest,
  removeFavoritePodcastRequest,
  addFavoriteEpisodeRequest,
  updateFavoriteEpisodeOrderRequest,
  removeFavoriteEpisodeRequest,
} from './requests';
import {
  IAddFavoritePodcastRequest,
  IAddFavoriteEpisodeRequest,
  IUpdateFavoriteOrderRequest,
  IFavoritePodcastResponse,
  IFavoriteEpisodeResponse,
} from './types';
import queryKeys from '../../utils/queryKeys';

// Podcast Mutations
export const useAddFavoritePodcastMutation = (
  options?: UseMutationOptions<IFavoritePodcastResponse, Error, IAddFavoritePodcastRequest>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: addFavoritePodcastRequest,
    onSettled: (data, error, variables) => {
      // if (data && !error) {
      //   queryClient.setQueriesData<InfiniteData<IGetFavoritePodcastsResponse>>(
      //     { queryKey: queryKeys.favorites.podcastsInfinite() },
      //     (oldData) => {
      //       if (!oldData) return oldData;

      //       // Create new podcast item from response
      //       const newPodcast = {
      //         id: data.id,
      //         order: data.order,
      //         podcastId: data.podcastId,
      //         title: '',
      //         imageUrl: '',
      //         authorName: '',
      //         authorEmail: '',
      //         avgRate: 0,
      //         userRate: undefined,
      //       };

      //       return {
      //         ...oldData,
      //         pages: oldData.pages.map((page, pageIndex) =>
      //           pageIndex === 0 ? { ...page, data: [newPodcast, ...page.data] } : page
      //         ),
      //       };
      //     }
      //   );
      // }

      // Still invalidate to ensure data consistency
      queryClient.invalidateQueries({ queryKey: queryKeys.favorites.podcastsInfinite() });
    },
    ...options,
  });
};

export const useUpdateFavoritePodcastOrderMutation = (
  options?: UseMutationOptions<boolean, Error, IUpdateFavoriteOrderRequest>
) => {
  return useMutation({
    mutationFn: updateFavoritePodcastOrderRequest,
    ...options,
  });
};

export const useRemoveFavoritePodcastMutation = (options?: UseMutationOptions<boolean, Error, number>) => {
  return useMutation({
    mutationFn: removeFavoritePodcastRequest,
    ...options,
  });
};

// Episode Mutations
export const useAddFavoriteEpisodeMutation = (
  options?: UseMutationOptions<IFavoriteEpisodeResponse, Error, IAddFavoriteEpisodeRequest>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: addFavoriteEpisodeRequest,
    onSettled: (data, error, variables) => {
      // if (data && !error) {
      //   queryClient.setQueriesData<InfiniteData<IGetFavoriteEpisodesResponse>>(
      //     { queryKey: queryKeys.favorites.episodesInfinite() },
      //     (oldData) => {
      //       if (!oldData) return oldData;

      //       const newEpisode = {
      //         id: data.id,
      //         order: data.order,
      //         episodeId: data.episodeId,
      //         title: '',
      //         imageUrl: '',
      //         podcastTitle: '',
      //         podcastImageUrl: '',
      //         avgRate: 0,
      //         userRate: undefined,
      //       };

      //       return {
      //         ...oldData,
      //         pages: oldData.pages.map((page, pageIndex) =>
      //           pageIndex === 0 ? { ...page, data: [newEpisode, ...page.data] } : page
      //         ),
      //       };
      //     }
      //   );
      // }

      queryClient.invalidateQueries({ queryKey: queryKeys.favorites.episodesInfinite() });
    },
    ...options,
  });
};

export const useUpdateFavoriteEpisodeOrderMutation = (
  options?: UseMutationOptions<boolean, Error, IUpdateFavoriteOrderRequest>
) => {
  return useMutation({
    mutationFn: updateFavoriteEpisodeOrderRequest,
    ...options,
  });
};

export const useRemoveFavoriteEpisodeMutation = (options?: UseMutationOptions<boolean, Error, number>) => {
  return useMutation({
    mutationFn: removeFavoriteEpisodeRequest,
    ...options,
  });
};
