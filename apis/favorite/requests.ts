import { env } from '@/utils/const';
import { request } from '../axios';
import {
  IAddFavoritePodcastRequest,
  IAddFavoriteEpisodeRequest,
  IUpdateFavoriteOrderRequest,
  IGetFavoritesRequest,
  IFavoritePodcastResponse,
  IFavoriteEpisodeResponse,
  IGetFavoritePodcastsResponse,
  IGetFavoriteEpisodesResponse,
} from './types';

// Podcast Favorites
export const addFavoritePodcastRequest = async (
  params: IAddFavoritePodcastRequest
): Promise<IFavoritePodcastResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/favorite/podcast`,
    method: 'POST',
    data: params,
  });
  return data;
};

export const updateFavoritePodcastOrderRequest = async (params: IUpdateFavoriteOrderRequest): Promise<boolean> => {
  const { data } = await request({
    url: `${env.API_VERSION}/favorite/podcast`,
    method: 'PATCH',
    data: params,
  });
  return data;
};

export const removeFavoritePodcastRequest = async (id: number): Promise<boolean> => {
  const { data } = await request({
    url: `${env.API_VERSION}/favorite/podcast/${id}`,
    method: 'DELETE',
  });
  return data;
};

// Episode Favorites
export const addFavoriteEpisodeRequest = async (
  params: IAddFavoriteEpisodeRequest
): Promise<IFavoriteEpisodeResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/favorite/episode`,
    method: 'POST',
    data: params,
  });
  return data;
};

export const updateFavoriteEpisodeOrderRequest = async (params: IUpdateFavoriteOrderRequest): Promise<boolean> => {
  const { data } = await request({
    url: `${env.API_VERSION}/favorite/episode`,
    method: 'PATCH',
    data: params,
  });
  return data;
};

export const removeFavoriteEpisodeRequest = async (id: number): Promise<boolean> => {
  const { data } = await request({
    url: `${env.API_VERSION}/favorite/episode/${id}`,
    method: 'DELETE',
  });
  return data;
};

// Get Favorites
export const getFavoritePodcastsRequest = async (
  params: IGetFavoritesRequest = {}
): Promise<IGetFavoritePodcastsResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/favorite/podcasts`,
    method: 'GET',
    params,
  });
  return {
    data: data.data,
    pagination: data.meta,
  };
};

export const getFavoriteEpisodesRequest = async (
  params: IGetFavoritesRequest = {}
): Promise<IGetFavoriteEpisodesResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/favorite/episodes`,
    method: 'GET',
    params,
  });
  return {
    data: data.data,
    pagination: data.meta,
  };
};
