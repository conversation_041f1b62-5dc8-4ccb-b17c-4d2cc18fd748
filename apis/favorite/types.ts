export interface IFavoritePodcast {
  id: number;
  order: number;
  podcastId: number;
  title: string;
  imageUrl: string;
  authorName: string;
  authorEmail: string;
  avgRate: number;
  userRate?: number;
}

export interface IFavoriteEpisode {
  id: number;
  order: number;
  episodeId: number;
  title: string;
  imageUrl: string;
  podcastTitle: string;
  podcastImageUrl: string;
  avgRate: number;
  userRate?: number;
}

export interface IPaginationParams {
  page?: number;
  limit?: number;
}

export interface IPaginationResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Infinite query response structure
export interface IInfiniteQueryResponse<T> {
  pages: T[];
  pageParams: unknown[];
}

// Request types
export interface IAddFavoritePodcastRequest {
  podcastId: number;
}

export interface IAddFavoriteEpisodeRequest {
  episodeId: number;
}

export interface IUpdateFavoriteOrderRequest {
  id: number;
  index: number;
}

export interface IGetFavoritesRequest extends IPaginationParams {
  userId?: number;
}

// Response types
export interface IFavoritePodcastResponse {
  id: number;
  podcastId: number;
  userId: number;
  order: number;
  createdAt: string;
  updatedAt: string;
  title: string;
  imageUrl: string;
  authorName: string;
  authorEmail: string;
  avgRate: number;
  userRate: number;
}

export interface IFavoriteEpisodeResponse {
  id: number;
  episodeId: number;
  userId: number;
  order: number;
  createdAt: string;
  updatedAt: string;
  title: string;
  imageUrl: string;
  authorName: string;
  authorEmail: string;
  avgRate: number;
  userRate: number;
  podcastTitle: string;
}

export type IGetFavoritePodcastsResponse = IPaginationResponse<IFavoritePodcast>;
export type IGetFavoriteEpisodesResponse = IPaginationResponse<IFavoriteEpisode>;

// Infinite query response types
export type IGetFavoritePodcastsInfiniteResponse = IInfiniteQueryResponse<IGetFavoritePodcastsResponse>;
export type IGetFavoriteEpisodesInfiniteResponse = IInfiniteQueryResponse<IGetFavoriteEpisodesResponse>;
