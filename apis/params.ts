export type IPaginationParams = {
  page: number;
  limit: number;
};

export type IPaginationMeta = {
  itemCount: number;
  totalItems: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
};

export type IPaginationResponse<T> = {
  meta: IPaginationMeta;
  data: T[];
};

export type IQueryParams = {
  search?: string;
  limit: number;
  page: number;
  sort?: 'ASC' | 'DESC';
  fromDate?: string;
  toDate?: string;
};

export type ISource = 'local' | 'podchaser';
