import queryKeys from '@/utils/queryKeys';
import { QueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';

export const handleToggleFollowUser = (
  queryClient: QueryClient,
  error: AxiosError<unknown, any> | null,
  userId: string | number
) => {
  queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.byUserId(userId) });
  queryClient.invalidateQueries({ queryKey: queryKeys.follow.followersInfinite() });
  queryClient.invalidateQueries({ queryKey: queryKeys.follow.followingInfinite() });
  queryClient.invalidateQueries({ queryKey: queryKeys.follow.followersList() });
  queryClient.invalidateQueries({ queryKey: queryKeys.follow.followingList() });
  queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.byUserId() });

  if (error) {
    queryClient.invalidateQueries({ queryKey: queryKeys.referral.friends() });
  }
};
