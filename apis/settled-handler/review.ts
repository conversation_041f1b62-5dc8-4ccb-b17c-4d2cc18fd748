import queryKeys from '@/utils/queryKeys';
import { InfiniteData, QueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { CustomVariables } from './like';
import { EpisodeQueryResponse } from '../episode';

export const handlePodcastRate = (queryClient: QueryClient, error?: AxiosError<unknown, any> | null) => {
  queryClient.invalidateQueries({ queryKey: queryKeys.rates.stat() });
  queryClient.invalidateQueries({ queryKey: queryKeys.podcasts.item() });
  queryClient.invalidateQueries({ queryKey: queryKeys.rates.list() });
  queryClient.invalidateQueries({ queryKey: queryKeys.rates.byPodcastId() });
  queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.getUserRatesReviewInfinite() });
  queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.getUserRateStatsReview() });
  queryClient.invalidateQueries({ queryKey: queryKeys.favorites.podcastsInfinite() });
  queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.postUserHistoryInfinite() }); // need check
};

export const handleDeletePodcastRate = (queryClient: QueryClient, error?: AxiosError<unknown, any> | null) => {
  handlePodcastRate(queryClient, error);
};

type CustomVariablesEpisodeRate = {
  rate?: number;
} & CustomVariables;

export const handleEpisodeRate = (
  queryClient: QueryClient,
  error?: AxiosError<unknown, any> | null,
  variables?: CustomVariablesEpisodeRate
) => {
  queryClient.invalidateQueries({ queryKey: queryKeys.rates.episodeRateList() });
  queryClient.invalidateQueries({ queryKey: queryKeys.rates.episodeRateStats() });
  // queryClient.invalidateQueries({ queryKey: queryKeys.episodes.getEpisodesListInfiniteRequest() });
  queryClient.invalidateQueries({ queryKey: queryKeys.episodes.getEpisodeRateByIdRequest() });
  queryClient.invalidateQueries({ queryKey: queryKeys.episodes.getEpisodeByIdRequest() });
  queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.getUserRatesReviewInfinite() });
  queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.getUserRateStatsReview() });
  queryClient.invalidateQueries({ queryKey: queryKeys.favorites.episodesInfinite() });
  queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.postUserHistoryInfinite() }); // need check

  if (variables?.episodeId) {
    if (variables?.rate !== undefined) {
      const rate = Number(variables.rate || 0);

      queryClient.setQueriesData<InfiniteData<EpisodeQueryResponse>>(
        { queryKey: queryKeys.episodes.getEpisodesListInfiniteRequest() },
        (oldData) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            pages: oldData.pages.map((page) => ({
              ...page,
              data: page.data.map((item) => {
                return item.id === variables?.episodeId ? { ...item, hasRated: rate > 0, userRate: rate } : item;
              }),
            })),
          };
        }
      );
    }
  } else {
    queryClient.invalidateQueries({ queryKey: queryKeys.episodes.getEpisodesListInfiniteRequest() });
  }
};

export const handleDeleteEpisodeRate = (
  queryClient: QueryClient,
  error?: AxiosError<unknown, any> | null,
  variables?: CustomVariablesEpisodeRate
) => {
  handleEpisodeRate(queryClient, error, variables);
};
