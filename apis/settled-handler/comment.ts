import queryKeys from '@/utils/queryKeys';
import { InfiniteData, QueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import {
  ICommentEpisodeResponse,
  ICommentResponse,
  IGetCommentsResponse,
  IUpdateCommentParams,
} from '../comment/types';
import { IPostHistoryResponse } from '../user';

export const handleDeleteComment = (queryClient: QueryClient, error?: AxiosError<unknown, any> | null) => {
  // queryClient.resetQueries({ queryKey: queryKeys.comments.listEpisode() });
  // queryClient.resetQueries({ queryKey: queryKeys.comments.list() });
  queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.postUserHistoryInfinite() });
  // queryClient.invalidateQueries({ queryKey: queryKeys.favorites.podcastsInfinite() });
  queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.replyUserHistoryInfinite() });
};

export const handleCreateComment = async (queryClient: QueryClient, error?: AxiosError<unknown, any> | null) => {
  return await Promise.all([
    queryClient.resetQueries({ queryKey: queryKeys.comments.listEpisode() }),
    queryClient.resetQueries({ queryKey: queryKeys.comments.list() }),
    queryClient.resetQueries({ queryKey: queryKeys.userProfile.postUserHistoryInfinite() }),
  ]);
};

export const handleUpdateComment = (queryClient: QueryClient, error?: AxiosError<unknown, any> | null) => {
  // queryClient.invalidateQueries({ queryKey: queryKeys.comments.list() });
  // queryClient.invalidateQueries({ queryKey: queryKeys.comments.listEpisode() });
  // queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.postUserHistoryInfinite() });
  // queryClient.invalidateQueries({ queryKey: queryKeys.comments.item() });
  // queryClient.invalidateQueries({ queryKey: queryKeys.comments.itemEpisode() });
};

export const handleCreateReplyComment = (queryClient: QueryClient, error?: AxiosError<unknown, any> | null) => {
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.item() });
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.list() });
  queryClient.invalidateQueries({ queryKey: queryKeys.commentsReply.list() });
  queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.replyUserHistoryInfinite() });
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.itemEpisode() });
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.listEpisode() });
  queryClient.invalidateQueries({ queryKey: queryKeys.commentsReply.listEpisode() });
};

export const handleUpdateReplyComment = (queryClient: QueryClient, error?: AxiosError<unknown, any> | null) => {
  queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.replyUserHistoryInfinite() });
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.itemEpisode() });
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.listEpisode() });
  queryClient.invalidateQueries({ queryKey: queryKeys.commentsReply.listEpisode() });

  queryClient.invalidateQueries({ queryKey: queryKeys.comments.item() });
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.list() });
  queryClient.invalidateQueries({ queryKey: queryKeys.commentsReply.list() });
};

export const handleDeleteReplyComment = (queryClient: QueryClient, error?: AxiosError<unknown, any> | null) => {
  queryClient.invalidateQueries({ queryKey: queryKeys.commentsReply.list() });
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.item() });
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.list() });
  queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.replyUserHistoryInfinite() });
  queryClient.invalidateQueries({ queryKey: queryKeys.commentsReply.listEpisode() });
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.itemEpisode() });
  queryClient.invalidateQueries({ queryKey: queryKeys.comments.listEpisode() });
};

export const updateCommentCache = async (
  queryClient: QueryClient,
  variables: Pick<IUpdateCommentParams, 'id' | 'content' | 'title' | 'images'> & { episodeId?: string }
) => {
  const isUpdateCommentEpisode = !!variables.episodeId;

  // infinite comment show detail
  await queryClient.cancelQueries({ queryKey: queryKeys.comments.list() });

  const snapshotCommentsShowDetail = queryClient.getQueriesData<InfiniteData<IGetCommentsResponse>>({
    queryKey: queryKeys.comments.list(),
  });

  queryClient.setQueriesData<InfiniteData<IGetCommentsResponse>>({ queryKey: queryKeys.comments.list() }, (oldData) => {
    if (!oldData) return oldData;

    let newSnapshot = { ...oldData };

    oldData.pages.some((page, pageIndex) => {
      const commentIndex = page.data.findIndex(
        (comment) => comment.id === variables.id && comment.type === (isUpdateCommentEpisode ? 'episode' : 'podcast')
      );

      if (commentIndex === -1) return false;

      newSnapshot['pages'][pageIndex]['data'][commentIndex] = {
        ...newSnapshot['pages'][pageIndex]['data'][commentIndex],
        content: variables.content,
        title: variables?.title,
        images: variables?.images || [],
        updatedAt: new Date().toISOString(),
        isEdited: true,
      };

      return true;
    });

    console.log(1231, newSnapshot['pages'][0].data[0].content);

    console.log({ newSnapshot });

    return newSnapshot;

    // return {
    //   ...oldData,
    //   pages: oldData.pages.map((page) => ({
    //     ...page,
    //     data: page.data.map((comment) => {
    //       if (isUpdateCommentEpisode) {
    //         if (comment.type !== 'episode') return comment;
    //         return comment.id === variables.id
    //           ? {
    //               ...comment,
    //               content: variables.content,
    //               title: variables?.title,
    //               images: variables?.images || [],
    //               updatedAt: new Date().toISOString(),
    //               isEdited: true,
    //             }
    //           : comment;
    //       } else {
    //         if (comment.type !== 'podcast') return comment;
    //         return comment.id === variables.id
    //           ? {
    //               ...comment,
    //               content: variables.content,
    //               title: variables?.title,
    //               images: variables?.images || [],
    //               updatedAt: new Date().toISOString(),
    //               isEdited: true,
    //             }
    //           : comment;
    //       }
    //     }),
    //   })),
    // };
  });

  // infinite comment episode
  await queryClient.cancelQueries({ queryKey: queryKeys.comments.listEpisode() });

  const snapshotCommentsEpisode = queryClient.getQueriesData<InfiniteData<IGetCommentsResponse>>({
    queryKey: queryKeys.comments.listEpisode(),
  });

  queryClient.setQueriesData<InfiniteData<IGetCommentsResponse>>(
    { queryKey: queryKeys.comments.listEpisode() },
    (oldData) => {
      if (!oldData) return oldData;

      return {
        ...oldData,
        pages: oldData.pages.map((page) => ({
          ...page,
          data: page.data.map((comment) =>
            comment.id === variables.id
              ? {
                  ...comment,
                  content: variables.content,
                  title: variables?.title,
                  images: variables?.images || [],
                  updatedAt: new Date().toISOString(),
                  isEdited: true,
                }
              : comment
          ),
        })),
      };
    }
  );

  // comment detail
  await queryClient.cancelQueries({ queryKey: queryKeys.comments.item() });

  const snapshotCommentPodcastItem = queryClient.getQueriesData<ICommentResponse>({
    queryKey: queryKeys.comments.item(),
  });

  queryClient.setQueriesData<ICommentResponse>({ queryKey: queryKeys.comments.item() }, (oldData) => {
    if (!oldData) return oldData;
    if (oldData.id !== variables.id) return oldData;

    return {
      ...oldData,
      content: variables.content,
      title: variables?.title,
      images: variables?.images || [],
      updatedAt: new Date().toISOString(),
      isEdited: true,
    };
  });

  // episode comment detail
  await queryClient.cancelQueries({ queryKey: queryKeys.comments.itemEpisode() });

  const snapshotCommentEpisodeItem = queryClient.getQueriesData<ICommentEpisodeResponse>({
    queryKey: queryKeys.comments.itemEpisode(),
  });

  queryClient.setQueriesData<ICommentEpisodeResponse>({ queryKey: queryKeys.comments.itemEpisode() }, (oldData) => {
    if (!oldData) return oldData;
    if (oldData.id !== variables.id) return oldData;

    return {
      ...oldData,
      content: variables.content,
      title: variables?.title,
      images: variables?.images || [],
      updatedAt: new Date().toISOString(),
      isEdited: true,
    };
  });

  // post user history
  await queryClient.cancelQueries({ queryKey: queryKeys.userProfile.postUserHistoryInfinite() });

  const snapshotPostUserHistory = queryClient.getQueriesData<InfiniteData<IPostHistoryResponse>>({
    queryKey: queryKeys.userProfile.postUserHistoryInfinite(),
  });

  queryClient.setQueriesData<InfiniteData<IGetCommentsResponse>>(
    { queryKey: queryKeys.userProfile.postUserHistoryInfinite() },
    (oldData) => {
      if (!oldData) return oldData;

      return {
        ...oldData,
        pages: oldData.pages.map((page) => ({
          ...page,
          data: page.data.map((comment) => {
            if (isUpdateCommentEpisode) {
              if (comment.type !== 'episode') return comment;
              return comment.id === variables.id
                ? {
                    ...comment,
                    content: variables.content,
                    title: variables?.title,
                    images: variables?.images || [],
                    updatedAt: new Date().toISOString(),
                    isEdited: true,
                  }
                : comment;
            } else {
              if (comment.type !== 'podcast') return comment;
              return comment.id === variables.id
                ? {
                    ...comment,
                    content: variables.content,
                    title: variables?.title,
                    images: variables?.images || [],
                    updatedAt: new Date().toISOString(),
                    isEdited: true,
                  }
                : comment;
            }
          }),
        })),
      };
    }
  );

  return {
    snapshotCommentsShowDetail,
    snapshotCommentsEpisode,
    snapshotCommentPodcastItem,
    snapshotCommentEpisodeItem,
    snapshotPostUserHistory,
  };
};
