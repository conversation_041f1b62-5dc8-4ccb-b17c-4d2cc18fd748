import queryKeys from '@/utils/queryKeys';
import { QueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';

export const handleToggleWatchlist = (queryClient: QueryClient, error?: AxiosError<unknown, any> | null) => {
  queryClient.invalidateQueries({ queryKey: queryKeys.episodes.getEpisodesListInfiniteRequest() });
  queryClient.invalidateQueries({ queryKey: queryKeys.watchlist.getEpisodeWatchlistRequest() });
  queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.getUserWatchlistHistoryInfinite() });
};

export const handleToggleWatchlistPodcast = (queryClient: QueryClient, error?: AxiosError<unknown, any> | null) => {
  queryClient.invalidateQueries({ queryKey: queryKeys.watchlist.byPodcastId() });
  queryClient.invalidateQueries({ queryKey: queryKeys.rates.byPodcastId() });
  queryClient.invalidateQueries({ queryKey: queryKeys.userProfile.getUserWatchlistHistoryInfinite() });
};
