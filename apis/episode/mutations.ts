import queryKeys from '@/utils/queryKeys';
import { InfiniteData, UseMutationOptions, useMutation, useQueryClient } from '@tanstack/react-query';
import { useGetProfileQuery } from '../auth/queries';
import { IEpisode } from '../podcast';
import {
  deleteEpisodeRateRequest,
  rateEpisodeRequest,
  toggleLikeRequest,
  toggleWatchedRequest,
  toggleWatchlistRequest,
} from './requests';
import { CreateEpisodeRateDto, Episode, IEpisodeMarkAsWatchResponse, IEpisodeWatchlistResponse } from './types';
import { handleToggleLikeEpisode } from '../settled-handler/like';
import { AxiosError } from 'axios';
import { handleDeleteEpisodeRate, handleEpisodeRate } from '../settled-handler/review';
import { handleToggleWatchlist } from '../settled-handler/watchlist';
import { handleToggleWatched } from '../settled-handler/watched';

export const useToggleLikeEpisode = (
  options?: UseMutationOptions<void, AxiosError, { episodeId: string; isLiked: boolean }>
) => {
  const queryClient = useQueryClient();

  return useMutation<void, AxiosError, { episodeId: string; isLiked: boolean }>({
    mutationFn: ({ episodeId }) => toggleLikeRequest(episodeId),
    onMutate: async (variables) => {
      await queryClient.cancelQueries({ queryKey: queryKeys.episodes.getEpisodesListInfiniteRequest() });
      await queryClient.cancelQueries({ queryKey: queryKeys.episodes.getEpisodeByIdRequest(variables.episodeId) });

      queryClient.setQueriesData<InfiniteData<{ data: Episode[] }>>(
        { queryKey: queryKeys.episodes.getEpisodesListInfiniteRequest() },
        (oldData) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            pages: oldData.pages?.map((page) => ({
              ...page,
              data: page.data.map((episode) =>
                episode.id === variables.episodeId ? { ...episode, hasLiked: variables.isLiked } : episode
              ),
            })),
          };
        }
      );

      queryClient.setQueriesData<IEpisode>(
        { queryKey: queryKeys.episodes.getEpisodeByIdRequest(variables.episodeId) },
        (oldData) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            hasLiked: !oldData.hasLiked,
          };
        }
      );
    },
    onSettled(data, error, variables) {
      handleToggleLikeEpisode(queryClient, error, { episodeId: variables.episodeId });
    },
    ...options,
  });
};

export const useRateEpisode = (options?: UseMutationOptions<void, AxiosError, CreateEpisodeRateDto>) => {
  const queryClient = useQueryClient();

  return useMutation<void, AxiosError, CreateEpisodeRateDto>({
    mutationFn: (data) => rateEpisodeRequest(data),
    onSettled(data, error, variables) {
      handleEpisodeRate(queryClient, error, { episodeId: variables.episodeId, rate: variables.rate });
    },
    ...options,
  });
};

export const useDeleteEpisodeRate = (options?: UseMutationOptions<void, AxiosError, string>) => {
  const queryClient = useQueryClient();

  return useMutation<void, AxiosError, string>({
    mutationFn: (episodeId) => deleteEpisodeRateRequest(episodeId),
    onSettled(data, error, episodeId) {
      handleDeleteEpisodeRate(queryClient, error, {
        episodeId,
        rate: 0,
      });
    },
    ...options,
  });
};

export const useToggleWatchlist = (
  options?: UseMutationOptions<void, AxiosError, { episodeId: string; isInWatchlist: boolean }>
) => {
  const queryClient = useQueryClient();
  const { data: userProfile } = useGetProfileQuery();

  return useMutation<void, AxiosError, { episodeId: string; isInWatchlist: boolean }>({
    mutationFn: ({ episodeId, isInWatchlist }) => toggleWatchlistRequest(episodeId, isInWatchlist),
    onMutate(variables) {
      queryClient.setQueriesData<InfiniteData<{ data: Episode[] }>>(
        { queryKey: queryKeys.episodes.getEpisodesListInfiniteRequest() },
        (oldData) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            pages: oldData.pages?.map((page) => ({
              ...page,
              data: page.data.map((episode) => {
                return +episode.id === +variables.episodeId
                  ? { ...episode, isWatchlist: !episode.isWatchlist }
                  : episode;
              }),
            })),
          };
        }
      );

      queryClient.setQueriesData<IEpisodeWatchlistResponse>(
        { queryKey: queryKeys.watchlist.getEpisodeWatchlistRequest(variables.episodeId) },
        () => {
          if (variables.isInWatchlist) return null as any;

          return {
            id: '1',
            userId: userProfile?.id?.toString() ?? '-1',
            episodeId: variables.episodeId,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            deletedAt: new Date().toISOString(),
          };
        }
      );
    },
    onSettled(data, error) {
      handleToggleWatchlist(queryClient, error);
    },
    ...options,
  });
};

export const useToggleWatched = (
  options?: UseMutationOptions<void, Error, { episodeId: string; isWatched: boolean }>
) => {
  const queryClient = useQueryClient();
  const { data: userProfile } = useGetProfileQuery();

  return useMutation<void, AxiosError, { episodeId: string; isWatched: boolean }>({
    mutationFn: ({ episodeId, isWatched }) => toggleWatchedRequest(episodeId, isWatched),
    onMutate(variables) {
      queryClient.setQueriesData<InfiniteData<{ data: Episode[] }>>(
        { queryKey: queryKeys.episodes.getEpisodesListInfiniteRequest() },
        (oldData) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            pages: oldData.pages?.map((page) => ({
              ...page,
              data: page.data.map((episode) => {
                return +episode.id === +variables.episodeId ? { ...episode, isWatched: !episode.isWatched } : episode;
              }),
            })),
          };
        }
      );

      queryClient.setQueriesData<IEpisodeMarkAsWatchResponse>(
        { queryKey: queryKeys.markAsWatched.getEpisodeMarkAsWatchedRequest(variables.episodeId) },
        () => {
          if (variables.isWatched) return null as any;

          return {
            id: '1',
            userId: userProfile?.id?.toString() ?? '-1',
            episodeId: variables.episodeId,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            deletedAt: new Date().toISOString(),
          };
        }
      );
    },
    onSettled(data, error, variables) {
      handleToggleWatched(queryClient, error);
    },
    ...options,
  });
};
