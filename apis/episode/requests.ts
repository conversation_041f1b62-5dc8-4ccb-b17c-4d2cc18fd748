import { env } from '@/utils/const';
import { request } from '../axios';
import { IEpisode } from '../podcast';
import {
  CreateEpisodeRateDto,
  EpisodeQueryParams,
  EpisodeQueryResponse,
  IEpisodeMarkAsWatchResponse,
  IEpisodeRate,
  IEpisodeWatchlistResponse,
  IGetEpisodeRateListParams,
  IGetEpisodeRateStatsResponse,
  IGetEpisodeRatesResponse,
} from './types';

export const getEpisodesRequest = async (params: EpisodeQueryParams): Promise<EpisodeQueryResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/episode`,
    method: 'GET',
    params,
  });
  return data;
};

export const toggleLikeRequest = async (id: string): Promise<void> => {
  await request({
    url: `${env.API_VERSION}/episode/${id}/like`,
    method: 'POST',
  });
};

export const getEpisodeRateByIdRequest = async (episodeId: string): Promise<IEpisodeRate> => {
  const { data } = await request({ url: `${env.API_VERSION}/episode-rate/${episodeId}`, method: 'GET' });
  return data.data;
};

export const rateEpisodeRequest = async (params: CreateEpisodeRateDto): Promise<any> => {
  const { data } = await request({
    url: `${env.API_VERSION}/episode-rate`,
    method: 'POST',
    data: params,
  });
  return data;
};

export const deleteEpisodeRateRequest = async (episodeId: string): Promise<void> => {
  await request({
    url: `${env.API_VERSION}/episode-rate/${episodeId}`,
    method: 'DELETE',
  });
};

export const getEpisodeWatchlistRequest = async (episodeId: string): Promise<IEpisodeWatchlistResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/watch-list/episode/${episodeId}`,
    method: 'GET',
  });
  return data.data;
};

export const addToWatchlistRequest = async (episodeId: string): Promise<void> => {
  await request({
    url: `${env.API_VERSION}/watch-list/episode/add`,
    method: 'POST',
    data: { episodeId: Number(episodeId) },
  });
};

export const removeFromWatchlistRequest = async (episodeId: string): Promise<void> => {
  await request({
    url: `${env.API_VERSION}/watch-list/episode/${episodeId}`,
    method: 'DELETE',
  });
};

export const toggleWatchlistRequest = async (episodeId: string, isInWatchlist: boolean): Promise<void> => {
  if (isInWatchlist) {
    await removeFromWatchlistRequest(episodeId);
  } else {
    await addToWatchlistRequest(episodeId);
  }
};

export const markAsWatchedRequest = async (episodeId: string): Promise<void> => {
  await request({
    url: `${env.API_VERSION}/mark-as-watched/episode/add`,
    method: 'POST',
    data: { episodeId: Number(episodeId) },
  });
};

export const unmarkAsWatchedRequest = async (episodeId: string): Promise<void> => {
  await request({
    url: `${env.API_VERSION}/mark-as-watched/episode/${episodeId}`,
    method: 'DELETE',
  });
};

export const toggleWatchedRequest = async (episodeId: string, isWatched: boolean): Promise<void> => {
  if (isWatched) {
    await unmarkAsWatchedRequest(episodeId);
  } else {
    await markAsWatchedRequest(episodeId);
  }
};

export const getWatchlistRequest = async (params: EpisodeQueryParams): Promise<any> => {
  const { data } = await request({
    url: `${env.API_VERSION}/episode/watchlist`,
    method: 'GET',
    params,
  });
  return data.data;
};

export const getWatchedRequest = async (params: EpisodeQueryParams): Promise<any> => {
  const { data } = await request({
    url: `${env.API_VERSION}/episode/watched`,
    method: 'GET',
    params,
  });
  return data.data;
};

export const getEpisodeByIdRequest = async (episodeId: string): Promise<IEpisode> => {
  const { data } = await request({
    url: `${env.API_VERSION}/episode/${episodeId}`,
    method: 'GET',
  });

  return data.data;
};

export const getEpisodeRateListRequest = async (
  params: IGetEpisodeRateListParams
): Promise<IGetEpisodeRatesResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/episode-rate/list/${params.episodeId}`,
    method: 'GET',
    params,
  });

  return data;
};

export const getEpisodeRateStatsRequest = async (episodeId: string): Promise<IGetEpisodeRateStatsResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/episode-rate/stats/${episodeId}`,
    method: 'GET',
  });

  return data.data;
};

export const getEpisodeMarkAsWatchedRequest = async (episodeId: string): Promise<IEpisodeMarkAsWatchResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/mark-as-watched/episode/${episodeId}`,
    method: 'GET',
  });
  return data.data;
};
