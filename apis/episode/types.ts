import { IPaginationMeta, IPaginationResponse } from '../params';
import { TUserSource } from '../user';

export interface EpisodeQueryDto {
  podcastId?: number;
  search?: string;
  sort?: string;
  fromDate?: string;
  toDate?: string;
}

export interface QueryPaginationDto {
  page: number;
  limit: number;
}
export interface EpisodeQueryParams extends EpisodeQueryDto, QueryPaginationDto {}
export interface EpisodeQueryResponse extends IPaginationResponse<Episode> {}

export interface CreateEpisodeRateDto {
  episodeId: string;
  rate: number;
}

export interface EpisodeWatchlistDto {
  episodeId: number;
}

export interface MarkAsWatchedDto {
  episodeId: number;
}

export interface Episode {
  id: string;
  title: string;
  description: string;
  duration: number;
  publishDate: string;
  imageUrl?: string;
  audioUrl?: string;
  likesCount: number;
  isLiked: boolean;
  ratingCount?: number;
  userRating?: number;
  podcastId: string;
  releaseDate: string;
  podcastTitle: string;
  reviewCount: number;
  hasLiked: boolean;
  hasRated: boolean;
  isWatchlist?: boolean;
  isWatched?: boolean;
  commentCount?: string;
  avgRate?: string;
  rateCount?: string;
  ratingAverage?: string;
  userRate?: number;
}

export interface IGetEpisodeRateListParams {
  limit: number;
  page: number;
  episodeId: string;
}

export interface IEpisodeRate {
  id: string;
  episodeId: number;
  rate: number;
  userId: string;
  username: string;
  avatar: string;
  createdAt: string;
  source: TUserSource;
  tag?: string;
}

export interface IGetEpisodeRatesResponse {
  meta: IPaginationMeta;
  data: IEpisodeRate[];
}

export interface IGetEpisodeRateStatsResponse {
  averageRating: number;
  distribution: { count: string; rateValue: number }[];
  totalRatings: number;
  totalUsers: number;
}

export interface IEpisodeWatchlistResponse {
  createdAt: string;
  updatedAt: string;
  deletedAt: string;
  id: string;
  userId: string;
  episodeId: string;
}

export interface IEpisodeMarkAsWatchResponse extends IEpisodeWatchlistResponse {}
