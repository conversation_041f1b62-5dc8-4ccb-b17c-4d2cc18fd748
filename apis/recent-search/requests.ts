import { env } from '@/utils/const';
import { request } from '../axios';
import {
  ICreateRecentSearchRequest,
  IDeleteRecentSearchParams,
  IGetRecentSearchParams,
  IRecentSearchResponse,
} from './types';

export const createRecentSearchRequest = async (params: ICreateRecentSearchRequest): Promise<void> => {
  await request({
    url: `${env.API_VERSION}/recent-search`,
    method: 'POST',
    data: params,
  });
};

export const getRecentSearchesRequest = async (params?: IGetRecentSearchParams): Promise<IRecentSearchResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/recent-search`,
    method: 'GET',
    params,
  });

  return data;
};

export const deleteRecentSearchRequest = async (params: IDeleteRecentSearchParams): Promise<void> => {
  await request({
    url: `${env.API_VERSION}/recent-search/${params.id}`,
    method: 'DELETE',
  });
};
