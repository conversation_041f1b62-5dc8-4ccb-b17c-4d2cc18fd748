import { useQueryClient } from '@tanstack/react-query';
import queryKeys from '@/utils/queryKeys';
import { useCreateRecentSearch } from './mutations';
import { RecentSearchType, IRecentSearchResponse, IRecentSearchItem } from './types';

export const useAddRecentSearch = () => {
  const { mutate: createRecentSearch } = useCreateRecentSearch();
  const queryClient = useQueryClient();

  const addRecentSearch = (
    type: RecentSearchType,
    targetId: number,
    target: 'discover' | 'favorite',
    optimisticData?: { title: string; imageUrl: string }
  ) => {
    // Add optimistic data to cache immediately if provided
    if (optimisticData) {
      const queryKey = queryKeys.recentSearch.list({ limit: 10, sort: 'DESC' });
      const previousData = queryClient.getQueryData<IRecentSearchResponse>(queryKey);
      if (previousData) {
        const optimisticItem: IRecentSearchItem = {
          id: `temp-${Date.now()}`,
          targetId: targetId.toString(),
          updatedAt: new Date().toISOString(),
          title: optimisticData.title,
          imageUrl: optimisticData.imageUrl,
          subTitle: type === 'podcast' ? 'Show' : 'Episode',
          type,
        };

        // Remove any existing item with same targetId and type, then add new one at beginning
        const filteredData = previousData.data.filter(
          (item) =>
            !(
              item.targetId === targetId.toString() &&
              ((type === 'podcast' && item.subTitle === 'Show') ||
                (type === 'episode' && item?.subTitle?.startsWith('Episode')))
            )
        );

        queryClient.setQueryData<IRecentSearchResponse>(queryKey, {
          ...previousData,
          data: [optimisticItem, ...filteredData].slice(0, 10), // Keep only 10 most recent
        });
      }
    }

    // Create the actual request
    createRecentSearch({
      type,
      targetId,
      target,
    });
  };

  return { addRecentSearch };
};
