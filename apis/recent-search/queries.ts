import { useQuery } from '@tanstack/react-query';
import queryKeys from '@/utils/queryKeys';
import { IGetRecentSearchParams } from './types';
import { getRecentSearchesRequest } from './requests';

export const useGetRecentSearches = (params?: IGetRecentSearchParams) => {
  return useQuery({
    queryKey: queryKeys.recentSearch.list(params),
    queryFn: () => getRecentSearchesRequest(params),
  });
};
