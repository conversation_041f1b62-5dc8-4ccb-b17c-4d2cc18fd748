import { IPaginationResponse } from '../params';

export type RecentSearchType = 'podcast' | 'episode';

export interface IRecentSearchItem {
  id: string;
  targetId: string;
  updatedAt: string;
  title: string;
  imageUrl: string;
  subTitle?: string;
  type?: string;
}

export interface IRecentSearchResponse extends IPaginationResponse<IRecentSearchItem> {}

export interface ICreateRecentSearchRequest {
  type: RecentSearchType;
  targetId: number;
  target: 'discover' | 'favorite';
}

export interface IUpdateRecentSearchRequest {
  type?: RecentSearchType;
  targetId?: number;
}

export interface IGetRecentSearchParams {
  limit?: number;
  page?: number;
  fromDate?: string;
  toDate?: string;
  sort?: 'ASC' | 'DESC';
  type?: RecentSearchType;
  target?: 'discover' | 'favorite';
}

export interface IDeleteRecentSearchParams {
  id: number;
}
