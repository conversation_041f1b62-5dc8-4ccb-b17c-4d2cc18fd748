import { useMutation, useQueryClient } from '@tanstack/react-query';
import queryKeys from '@/utils/queryKeys';
import { createRecentSearchRequest, deleteRecentSearchRequest } from './requests';
import { ICreateRecentSearchRequest, IDeleteRecentSearchParams, IRecentSearchResponse } from './types';

export const useCreateRecentSearch = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: ICreateRecentSearchRequest) => createRecentSearchRequest(params),
    onMutate: async (newSearch) => {
      await queryClient.cancelQueries({ queryKey: queryKeys.recentSearch.list() });

      const previousData = queryClient.getQueryData<IRecentSearchResponse>(
        queryKeys.recentSearch.list({ limit: 10, sort: 'DESC' })
      );

      return { previousData };
    },
    onError: (err, newSearch, context) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.recentSearch.list(),
      });
    },
    onSettled: () => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.recentSearch.list(),
      });
    },
  });
};

export const useDeleteRecentSearch = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: IDeleteRecentSearchParams) => deleteRecentSearchRequest(params),
    onMutate: async (deletedSearch) => {
      await queryClient.cancelQueries({ queryKey: queryKeys.recentSearch.list() });

      const previousData = queryClient.getQueriesData<IRecentSearchResponse>({
        queryKey: queryKeys.recentSearch.list(),
      });

      queryClient.setQueriesData<IRecentSearchResponse>({ queryKey: queryKeys.recentSearch.list() }, (oldData) => {
        if (!oldData) return oldData;
        return {
          ...oldData,
          data: oldData.data.filter((item) => Number(item.id) !== Number(deletedSearch.id)),
        };
      });

      return { previousData };
    },
    onError: (err, deletedSearch, context) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.recentSearch.list(),
      });
    },
    onSettled: (data, error) => {
      queryClient.invalidateQueries({
        queryKey: queryKeys.recentSearch.list(),
      });
    },
  });
};
