import { env } from '@/utils/const';
import { request } from '../axios';
import { IGetReferralFriendsParams, IGetReferralFriendsResponse } from './types';

export const getReferralFriendsRequest = async (
  params: IGetReferralFriendsParams
): Promise<IGetReferralFriendsResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/referral/list-friends`,
    method: 'GET',
    params,
  });

  return {
    data: data.data,
    meta: data.meta,
  };
};
