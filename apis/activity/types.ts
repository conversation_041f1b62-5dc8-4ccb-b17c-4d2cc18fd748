export interface IPaginationParams {
  page?: number;
  limit?: number;
}

export interface IPaginationMeta {
  totalItems: number;
  itemCount: number;
  itemsPerPage: number;
  totalPages: number;
  currentPage: number;
}

export interface IPaginatedResponse<T> {
  data: T[];
  meta: IPaginationMeta;
}

// Backend enum values - ch<PERSON>h xác từ server
export enum ActivityTypeEnum {
  RATE = 'rate',
  EDIT_RATE = 'edit_rate',
  DELETE_RATE = 'delete_rate',
  LIKE = 'like',
  UNLIKE = 'unlike',
  ADD_WATCHLIST = 'add_watchlist',
  REMOVE_WATCHLIST = 'remove_watchlist',
  MARK_WATCHED = 'mark_watched',
  UNMARK_WATCHED = 'unmark_watched',
  COMMENT = 'comment',
  EDIT_COMMENT = 'edit_comment',
  DELETE_COMMENT = 'delete_comment',
  REPLY = 'reply',
  EDIT_REPLY = 'edit_reply',
  DELETE_REPLY = 'delete_reply',
  FOLLOW = 'follow',
  UNFOLLOW = 'unfollow',
}

export enum TargetTypeEnum {
  PODCAST = 'podcast',
  EPISODE = 'episode',
  COMMENT = 'comment',
  USER = 'user',
}

// Metadata structures based on backend implementation
export interface RateMetadata {
  rating: number;
  title: string;
}

export interface LikeMetadata {
  title: string;
}

export interface WatchlistMetadata {
  title: string;
}

export interface WatchedMetadata {
  title: string;
}

export interface CommentMetadata {
  source: 'local' | 'podchaser';
  type: 'podcast' | 'episode';
  podcastId: number;
  episodeId: number;
  title: string;
  commentTitle: string;
}

export interface ReplyMetadata {
  user: {
    id: number;
    username: string;
    avatar?: string;
  };
  source: 'local' | 'podchaser';
  type: 'podcast' | 'episode';
  podcastId?: number;
  episodeId?: number;
  title: string;
  content: string;
}

export interface FollowMetadata {
  username: string;
}

export type ActivityMetadata =
  | RateMetadata
  | LikeMetadata
  | WatchlistMetadata
  | WatchedMetadata
  | CommentMetadata
  | ReplyMetadata
  | FollowMetadata;

export interface IActivityLog {
  id: number;
  userId: number;
  activityType: ActivityTypeEnum;
  targetType: TargetTypeEnum;
  targetId: number;
  metadata?: ActivityMetadata;
  createdAt: string;
  updatedAt: string;
  username?: string;
  avatar?: string;
}

export interface IGetMyActivitiesParams extends IPaginationParams {}

export interface IGetMyActivitiesResponse extends IPaginatedResponse<IActivityLog> {}

export interface IGetFollowingActivitiesParams extends IPaginationParams {}

export interface IGetFollowingActivitiesResponse extends IPaginatedResponse<IActivityLog> {}
