import { useInfiniteQuery } from '@tanstack/react-query';
import queryKeys from '@/utils/queryKeys';
import { getMyActivitiesRequest, getFollowingActivitiesRequest } from './requests';
import { IGetMyActivitiesParams, IGetFollowingActivitiesParams } from './types';

export const useGetInfiniteMyActivitiesQuery = (params: Omit<IGetMyActivitiesParams, 'page'>, { enabled = true }) => {
  return useInfiniteQuery({
    refetchOnMount: true,
    initialPageParam: 1,
    queryKey: queryKeys.activities.myActivitiesInfinite(params),
    queryFn: ({ pageParam = 1 }) => getMyActivitiesRequest({ ...params, page: pageParam }),
    getNextPageParam: (lastPage) => {
      return lastPage.meta.currentPage < lastPage.meta.totalPages ? lastPage.meta.currentPage + 1 : undefined;
    },
    getPreviousPageParam: (firstPage) => {
      return firstPage.meta.currentPage > 1 ? firstPage.meta.currentPage - 1 : undefined;
    },
    enabled,
  });
};

export const useGetInfiniteFollowingActivitiesQuery = (
  params: Omit<IGetFollowingActivitiesParams, 'page'>,
  { enabled = true }
) => {
  return useInfiniteQuery({
    refetchOnMount: true,
    initialPageParam: 1,
    queryKey: queryKeys.activities.followingActivitiesInfinite(params),
    queryFn: ({ pageParam = 1 }) => getFollowingActivitiesRequest({ ...params, page: pageParam }),
    getNextPageParam: (lastPage) => {
      return lastPage.meta.currentPage < lastPage.meta.totalPages ? lastPage.meta.currentPage + 1 : undefined;
    },
    getPreviousPageParam: (firstPage) => {
      return firstPage.meta.currentPage > 1 ? firstPage.meta.currentPage - 1 : undefined;
    },
    enabled,
  });
};
