import { env } from '@/utils/const';
import { request } from '../axios';
import {
  IGetMyActivitiesParams,
  IGetMyActivitiesResponse,
  IGetFollowingActivitiesParams,
  IGetFollowingActivitiesResponse,
} from './types';

export const getMyActivitiesRequest = async (params: IGetMyActivitiesParams): Promise<IGetMyActivitiesResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/activities/my`,
    method: 'GET',
    params,
  });

  return data;
};

export const getFollowingActivitiesRequest = async (
  params: IGetFollowingActivitiesParams
): Promise<IGetFollowingActivitiesResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/activities/following`,
    method: 'GET',
    params,
  });

  return data;
};
