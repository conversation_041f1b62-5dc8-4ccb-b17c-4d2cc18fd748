import { useLoggedIn } from '@/hooks/useLoggedIn';
import { UseQueryOptions, useQuery } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { getUserProfileRequest } from './requests';
import { IUserProfileResponse } from './types';
import queryKeys from '@/utils/queryKeys';

export const useGetProfileQuery = (
  options?: Omit<UseQueryOptions<any, AxiosError, IUserProfileResponse, any>, 'queryKey'>
) => {
  const { isLoggedIn, accessToken } = useLoggedIn();

  return useQuery({
    queryKey: queryKeys.auth.profile(accessToken, isLoggedIn),
    queryFn: getUserProfileRequest,
    enabled: isLoggedIn,
    ...options,
  });
};
