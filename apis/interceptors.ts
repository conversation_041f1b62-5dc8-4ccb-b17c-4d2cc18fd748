import { useUserStore } from '@/store/user';
import type { AxiosError, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { router } from 'expo-router';
import { IUserProfileResponse, refreshTokenRequest } from './auth';
import { request } from './axios';
import { useCommonStore } from '@/store/common';
import { env } from '@/utils/const';

export const requestInterceptor = (config: InternalAxiosRequestConfig): InternalAxiosRequestConfig => {
  const userStore = useUserStore.getState();
  const commonStore = useCommonStore.getState();
  const isStagingENV = commonStore.isStagingENV;
  config.baseURL = (isStagingENV ? env.API_URL_STAGING : env.API_URL_DEV) + env.API_PREFIX;

  if (userStore) {
    const accessToken = userStore.accessToken;

    if (accessToken) {
      config.headers.set('Authorization', `Bearer ${accessToken}`);
    }
  }

  return config;
};

export const successInterceptor = (response: AxiosResponse): AxiosResponse => {
  const isGetProfileRequest =
    response.config.url === '/v1/user/profile' && response.config.method?.toLowerCase() === 'get';

  if (isGetProfileRequest) {
    const responseUserInfo: IUserProfileResponse = response?.data?.data?.user;
    const userStore = useUserStore.getState();

    if (responseUserInfo?.isActive) {
      userStore.setAlertProfileRestricted(false);
      userStore.setIsShowWarningRestricted(false);
    } else if (!userStore.alertProfileRestricted) {
      userStore.setAlertProfileRestricted(true);
      userStore.setIsShowWarningRestricted(true);
    }
  }
  return response;
};

const onRefreshToken = async () => {
  const store = useUserStore.getState();
  const refreshToken = store?.refreshToken;

  if (refreshToken) {
    try {
      const {
        data: { accessToken, refreshToken: newRefreshToken },
      } = await refreshTokenRequest(refreshToken);

      store?.setAccessToken(accessToken);
      store?.setRefreshToken(newRefreshToken);

      return accessToken;
    } catch (e) {
      router.canDismiss() && router.dismissAll();
      store.signOut();
      router.replace('/sign-in');
    }
  }

  return null;
};

export const errorInterceptor = async (error: AxiosError): Promise<void> => {
  const originalRequest = error.config! as any;
  const data = error?.response?.data as any;
  const meta = data?.meta;
  const statusCode = error?.response?.status;
  const userStore = useUserStore.getState();
  const accessToken = userStore.accessToken;

  if (statusCode === 403) {
    const alertProfileRestricted = userStore.alertProfileRestricted;

    if (!alertProfileRestricted) {
      userStore.setAlertProfileRestricted(true);
      userStore.setIsShowWarningRestricted(false);
    }
  }

  if (statusCode === 401 && !!accessToken && !originalRequest._retry) {
    originalRequest._retry = true;

    try {
      const newAccessToken = await onRefreshToken();

      originalRequest.headers.set('Authorization', `Bearer ${newAccessToken}`);

      return request(originalRequest);
    } catch (error) {
    } finally {
      originalRequest._retry = false;
    }
  }

  return Promise.reject(meta || data || error);
};
