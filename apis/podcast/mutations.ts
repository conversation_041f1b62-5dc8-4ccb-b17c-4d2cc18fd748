import { UseMutationOptions, useMutation, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import {
  addFavoritePodcastBulkRequest,
  addPodcastRateRequest,
  addToWatchlistRequest,
  deletePodcastRateRequest,
  likePodcastRequest,
  markAsWatchedRequest,
  removeFromWatchlistRequest,
  removeMarkAsWatchedRequest,
} from './requests';
import {
  IAddFavoritePodcastBulkRequest,
  IAddPodcastRateParams,
  IDeletePodcastRateParams,
  IMarkAsWatchRequest,
  IMarkAsWatchResponse,
  IPodcast,
  IWatchlistAddRequest,
  IWatchlistResponse,
} from './types';
import queryKeys from '@/utils/queryKeys';
import { handleToggleLikePodcast } from '../settled-handler/like';
import { handleDeletePodcastRate, handlePodcastRate } from '../settled-handler/review';
import { handleToggleWatchlistPodcast } from '../settled-handler/watchlist';
import { handleToggleWatchedPodcast } from '../settled-handler/watched';

// Add podcast rate mutation
export const useAddPodcastRateMutation = (options?: UseMutationOptions<any, AxiosError, IAddPodcastRateParams>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: IAddPodcastRateParams) => addPodcastRateRequest(params),
    onSettled(data, error, variables) {
      handlePodcastRate(queryClient, error);
    },
    ...options,
  });
};

// Delete podcast rate mutation
export const useDeletePodcastRateMutation = (
  options?: UseMutationOptions<any, AxiosError, IDeletePodcastRateParams>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: IDeletePodcastRateParams) => deletePodcastRateRequest(params),
    onSettled(data, error, variables) {
      handleDeletePodcastRate(queryClient, error);
    },
    ...options,
  });
};

// Watchlist mutations
export const useAddToWatchlistMutation = (
  options?: UseMutationOptions<IWatchlistResponse, AxiosError, IWatchlistAddRequest>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: IWatchlistAddRequest) => addToWatchlistRequest(params),
    onSettled(data, error, variables) {
      handleToggleWatchlistPodcast(queryClient, error);
    },
    ...options,
  });
};

export const useRemoveFromWatchlistMutation = (options?: UseMutationOptions<void, AxiosError, string>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => removeFromWatchlistRequest(id),
    onSettled(data, error, variables) {
      handleToggleWatchlistPodcast(queryClient, error);
    },
    ...options,
  });
};

// Mark as watched mutations
export const useMarkAsWatchedMutation = (
  options?: UseMutationOptions<IMarkAsWatchResponse, AxiosError, IMarkAsWatchRequest>
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: IMarkAsWatchRequest) => markAsWatchedRequest(params),
    onSettled(data, error, variables) {
      handleToggleWatchedPodcast(queryClient, error);
    },
    ...options,
  });
};

export const useRemoveMarkAsWatchedMutation = (options?: UseMutationOptions<void, AxiosError, string>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => removeMarkAsWatchedRequest(id),
    onSettled(data, error, variables) {
      handleToggleWatchedPodcast(queryClient, error);
    },
    ...options,
  });
};

export const useLikePodcastMutation = (options?: UseMutationOptions<void, AxiosError, string>) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (podcastId: string) => likePodcastRequest(podcastId),
    onMutate: async (podcastId) => {
      await queryClient.cancelQueries({ queryKey: queryKeys.podcasts.item() });

      queryClient.setQueriesData<IPodcast>({ queryKey: queryKeys.podcasts.item() }, (oldData) => {
        if (!oldData || oldData.id !== podcastId) return oldData;

        return {
          ...oldData,
          hasLiked: !oldData.hasLiked,
        };
      });
    },
    onSettled: (data, error, podcastId) => {
      handleToggleLikePodcast(queryClient, error, { podcastId });
    },
    ...options,
  });
};
export const useAddFavoritePodcastBulkMutation = (
  options?: UseMutationOptions<void, AxiosError, IAddFavoritePodcastBulkRequest>
) => {
  return useMutation({
    mutationFn: (params) => addFavoritePodcastBulkRequest(params),
    ...options,
  });
};
