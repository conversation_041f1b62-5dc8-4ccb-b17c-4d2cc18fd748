export interface IFollowRequest {
  targetUserId?: number;
  page?: number;
  limit?: number;
  search?: string;
}

export const EUserType = {
  FREE: 'free',
  PREMIUM: 'premium',
} as const;

export type TUserType = (typeof EUserType)[keyof typeof EUserType];

export interface IFollow {
  id: number;
  username: string;
  avatar: string;
  isFollowed: boolean;
  type: TUserType;
}

export interface IPaginationResponse {
  totalItems: number;
  itemsPerPage: number;
  currentPage: number;
  totalPages: number;
}

export interface IGetFollowersResponse {
  data: IFollow[];
  pagination: IPaginationResponse;
}

export interface IGetFollowingResponse {
  data: IFollow[];
  pagination: IPaginationResponse;
}

export interface IToggleFollowResponse {
  data: boolean;
}
