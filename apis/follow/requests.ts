import { env } from '@/utils/const';
import { request } from '../axios';
import { IFollowRequest, IGetFollowersResponse, IGetFollowingResponse, IToggleFollowResponse } from './types';

export const toggleFollowRequest = async (followingId: number): Promise<IToggleFollowResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/follow/${followingId}`,
    method: 'POST',
  });
  return data;
};

export const getFollowersRequest = async (params: IFollowRequest = {}): Promise<IGetFollowersResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/follow/followers`,
    method: 'GET',
    params,
  });
  return {
    data: data.data,
    pagination: data.meta,
  };
};

export const getFollowingRequest = async (params: IFollowRequest = {}): Promise<IGetFollowingResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/follow/following`,
    method: 'GET',
    params,
  });
  return {
    data: data.data,
    pagination: data.meta,
  };
};
