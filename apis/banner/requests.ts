import { env } from '@/utils/const';
import { request } from '../axios';
import { IBannerData } from './types';
import { Image } from 'expo-image';

export const getBannerRequest = async (): Promise<IBannerData> => {
  const { data } = await request({
    url: `${env.API_VERSION}/banner`,
    method: 'GET',
  });

  const bannerImageUrl = data?.data?.imageUrl;

  if (bannerImageUrl) {
    Image.prefetch(bannerImageUrl);
  }

  return data.data;
};
