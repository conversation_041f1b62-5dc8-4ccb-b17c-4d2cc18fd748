import queryKeys from '@/utils/queryKeys';
import { UseQueryOptions, useQuery } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { getBannerRequest } from './requests';
import { IBannerData } from './types';

export const useGetBannerQuery = (options?: UseQueryOptions<IBannerData, AxiosError, IBannerData>) => {
  return useQuery({
    refetchOnMount: true,
    queryKey: queryKeys.banner.item(),
    queryFn: () => getBannerRequest(),
    ...options,
  });
};
