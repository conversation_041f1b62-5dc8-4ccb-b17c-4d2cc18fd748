import { InfiniteData, QueryOptions, UseQueryOptions, useInfiniteQuery, useQuery } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import {
  getPostUserHistoryRequest,
  getRegionInfoRequest,
  getReplyUserHistoryRequest,
  getUserLikesEpisodeHistoryRequest,
  getUserLikesPodcastHistoryRequest,
  getUserLikesPostHistoryRequest,
  getUserProfileByIdRequest,
  getUserRatesReviewRequest,
  getUserRateStatsReviewRequest,
  getUserWatchedHistoryRequest,
  getUserWatchlistHistoryRequest,
  fetchUserIdentitiesRequest,
  getSubscriptionStatus,
  getUserUpVoteHistoryRequest,
} from './requests';
import {
  IGetPostUserHistoryParams,
  IGetReplyUserHistoryParams,
  IGetUserLikesEpisodesHistoryParams,
  IGetUserLikesPodcastsHistoryParams,
  IGetUserLikesPostsHistoryParams,
  IGetUserRatesReviewParams,
  IGetUserRateStatsReview,
  IGetUserRateStatsReviewParams,
  IGetUserWatchedHistoryParams,
  IGetUserWatchedHistoryResponse,
  IGetUserWatchlistHistoryParams,
  IGetUserWatchlistHistoryResponse,
  IRegionInfoResponse,
  IUserProfileById,
  IUserIdentity,
  ISubscriptionStatus,
} from './types';
import queryKeys from '@/utils/queryKeys';

export const useGetRegionInfoQuery = (
  options?: Omit<UseQueryOptions<any, AxiosError, IRegionInfoResponse, any>, 'queryKey'>
) => {
  return useQuery({
    queryKey: ['my-region-SignInEmailFormData'],
    queryFn: getRegionInfoRequest,
    ...options,
  });
};

export const useGetUserProfileByIdQuery = (
  userId: string | number,
  options?: Omit<UseQueryOptions<IUserProfileById, AxiosError, IUserProfileById>, 'queryKey'>
) => {
  return useQuery({
    queryKey: queryKeys.userProfile.byUserId(userId),
    queryFn: () => getUserProfileByIdRequest(userId),
    ...options,
  });
};

export const useGetInfinitePostHistoryQuery = ({
  limit,
  userId,
  ...params
}: Omit<IGetPostUserHistoryParams, 'page'>) => {
  return useInfiniteQuery({
    refetchOnMount: true,
    enabled: !!userId,
    queryKey: queryKeys.userProfile.postUserHistoryInfinite({ userId, limit, ...params }),
    queryFn: ({ pageParam }) => getPostUserHistoryRequest({ limit, userId, page: pageParam }),
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      return lastPage.meta.currentPage < lastPage.meta.totalPages ? lastPage.meta.currentPage + 1 : undefined;
    },
    getPreviousPageParam: (firstPage) => {
      return firstPage.meta.currentPage > 1 ? firstPage.meta.currentPage - 1 : undefined;
    },
  });
};

export const useGetInfiniteReplyUserHistoryQuery = (params: IGetReplyUserHistoryParams) => {
  return useInfiniteQuery({
    initialPageParam: 1,
    enabled: !!params.userId,
    queryKey: queryKeys.userProfile.replyUserHistoryInfinite(params),
    queryFn: ({ pageParam = 1 }) => getReplyUserHistoryRequest({ ...params, page: pageParam }),
    getNextPageParam: (lastPage) => {
      return lastPage.meta.currentPage < lastPage.meta.totalPages ? lastPage.meta.currentPage + 1 : undefined;
    },
    getPreviousPageParam: (firstPage) => {
      return firstPage.meta.currentPage > 1 ? firstPage.meta.currentPage - 1 : undefined;
    },
  });
};

export const useGetInfiniteUserLikesPodcastsHistoryQuery = (params: IGetUserLikesPodcastsHistoryParams) => {
  return useInfiniteQuery({
    initialPageParam: 1,
    enabled: !!params.userId,
    queryKey: queryKeys.userProfile.getUserLikesPodcastsHistoryInfinite(params),
    queryFn: ({ pageParam = 1 }) => getUserLikesPodcastHistoryRequest({ ...params, page: pageParam }),
    getNextPageParam: (lastPage) => {
      return lastPage.meta.currentPage < lastPage.meta.totalPages ? lastPage.meta.currentPage + 1 : undefined;
    },
    getPreviousPageParam: (firstPage) => {
      return firstPage.meta.currentPage > 1 ? firstPage.meta.currentPage - 1 : undefined;
    },
  });
};

export const useGetInfiniteUserLikesEpisodesHistoryQuery = (params: IGetUserLikesEpisodesHistoryParams) => {
  return useInfiniteQuery({
    initialPageParam: 1,
    enabled: !!params.userId,
    queryKey: queryKeys.userProfile.getUserLikesEpisodesHistoryInfinite(params),
    queryFn: ({ pageParam = 1 }) => getUserLikesEpisodeHistoryRequest({ ...params, page: pageParam }),
    getNextPageParam: (lastPage) => {
      return lastPage.meta.currentPage < lastPage.meta.totalPages ? lastPage.meta.currentPage + 1 : undefined;
    },
    getPreviousPageParam: (firstPage) => {
      return firstPage.meta.currentPage > 1 ? firstPage.meta.currentPage - 1 : undefined;
    },
  });
};

export const useGetInfiniteUserLikesPostsHistoryQuery = (params: IGetUserLikesPostsHistoryParams) => {
  return useInfiniteQuery({
    initialPageParam: 1,
    enabled: !!params.userId,
    queryKey: queryKeys.userProfile.getUserLikesPostsHistoryInfinite(params),
    queryFn: ({ pageParam = 1 }) => getUserLikesPostHistoryRequest({ ...params, page: pageParam }),
    getNextPageParam: (lastPage) => {
      return lastPage.meta.currentPage < lastPage.meta.totalPages ? lastPage.meta.currentPage + 1 : undefined;
    },
    getPreviousPageParam: (firstPage) => {
      return firstPage.meta.currentPage > 1 ? firstPage.meta.currentPage - 1 : undefined;
    },
  });
};

export const useGetUserRateStatsReviewQuery = (
  params: IGetUserRateStatsReviewParams,
  options?: Omit<UseQueryOptions<IGetUserRateStatsReview, AxiosError, IGetUserRateStatsReview>, 'queryKey'>
) => {
  return useQuery({
    queryKey: queryKeys.userProfile.getUserRateStatsReview(params),
    queryFn: () => getUserRateStatsReviewRequest(params),
    ...options,
  });
};

export const useGetInfiniteUserRatesReviewQuery = (params: IGetUserRatesReviewParams, options?: QueryOptions) => {
  return useInfiniteQuery({
    initialPageParam: 1,
    enabled: !!params.userId,
    queryKey: queryKeys.userProfile.getUserRatesReviewInfinite(params),
    queryFn: ({ pageParam = 1 }) => getUserRatesReviewRequest({ ...params, page: pageParam }),
    getNextPageParam: (lastPage) => {
      return lastPage.meta.currentPage < lastPage.meta.totalPages ? lastPage.meta.currentPage + 1 : undefined;
    },
    getPreviousPageParam: (firstPage) => {
      return firstPage.meta.currentPage > 1 ? firstPage.meta.currentPage - 1 : undefined;
    },
    maxPages: 2,
  });
};

export const useGetInfiniteUserWatchlistHistoryQuery = (
  params: IGetUserWatchlistHistoryParams,
  options?: QueryOptions<
    IGetUserWatchlistHistoryResponse,
    AxiosError,
    InfiniteData<IGetUserWatchlistHistoryResponse, number>,
    any,
    number
  >
) => {
  return useInfiniteQuery({
    initialPageParam: 1,
    enabled: !!params.userId,
    queryKey: queryKeys.userProfile.getUserWatchlistHistoryInfinite(params),
    queryFn: ({ pageParam = 1 }) => getUserWatchlistHistoryRequest({ ...params, page: pageParam }),
    getNextPageParam: (lastPage) => {
      return lastPage.meta.currentPage < lastPage.meta.totalPages ? lastPage.meta.currentPage + 1 : undefined;
    },
    getPreviousPageParam: (firstPage) => {
      return firstPage.meta.currentPage > 1 ? firstPage.meta.currentPage - 1 : undefined;
    },
    ...options,
  });
};

export const useGetInfiniteUserWatchedHistoryQuery = (
  params: IGetUserWatchedHistoryParams,
  options?: QueryOptions<
    IGetUserWatchedHistoryResponse,
    AxiosError,
    InfiniteData<IGetUserWatchedHistoryResponse, number>,
    any,
    number
  >
) => {
  return useInfiniteQuery({
    initialPageParam: 1,
    enabled: !!params.userId,
    queryKey: queryKeys.userProfile.getUserWatchedHistoryInfinite(params),
    queryFn: ({ pageParam = 1 }) => getUserWatchedHistoryRequest({ ...params, page: pageParam }),
    getNextPageParam: (lastPage) => {
      return lastPage.meta.currentPage < lastPage.meta.totalPages ? lastPage.meta.currentPage + 1 : undefined;
    },
    getPreviousPageParam: (firstPage) => {
      return firstPage.meta.currentPage > 1 ? firstPage.meta.currentPage - 1 : undefined;
    },
    ...options,
  });
};

export const useGetUserIdentitiesQuery = (
  options?: Omit<UseQueryOptions<IUserIdentity[], Error, IUserIdentity[], readonly unknown[]>, 'queryKey' | 'queryFn'>
) => {
  return useQuery<IUserIdentity[], Error>({
    queryKey: queryKeys.userProfile.identities(),
    queryFn: fetchUserIdentitiesRequest,
    ...options,
  });
};

export const useGetSubscriptionStatusQuery = (
  options?: QueryOptions<ISubscriptionStatus, AxiosError, ISubscriptionStatus, any>
) => {
  return useQuery({
    queryKey: queryKeys.userProfile.getSubscriptionStatus(),
    queryFn: getSubscriptionStatus,
    ...options,
  });
};

export const useGetInfiniteUserUpVoteHistoryQuery = (params: IGetUserLikesPostsHistoryParams) => {
  return useInfiniteQuery({
    initialPageParam: 1,
    enabled: !!params.userId,
    queryKey: queryKeys.userProfile.getUserUpVoteHistoryInfinite(params),
    queryFn: ({ pageParam = 1 }) => getUserUpVoteHistoryRequest({ ...params, page: pageParam }),
    getNextPageParam: (lastPage) => {
      return lastPage.meta.currentPage < lastPage.meta.totalPages ? lastPage.meta.currentPage + 1 : undefined;
    },
  });
};
