import { env } from '@/utils/const';
import axios from 'axios';
import * as FileSystem from 'expo-file-system';
import { request } from '../axios';
import {
  ICheckUserProfileUpdateValidPayload,
  ICheckUserProfileUpdateValidResponse,
  IGetPostUserHistoryParams,
  IGetReplyUserHistoryParams,
  IGetReplyUserHistoryResponse,
  IGetUserLikesEpisodesHistoryParams,
  IGetUserLikesEpisodesHistoryResponse,
  IGetUserLikesPodcastsHistoryParams,
  IGetUserLikesPodcastsHistoryResponse,
  IGetUserLikesPostsHistoryParams,
  IGetUserLikesPostsHistoryResponse,
  IGetUserRatesReviewParams,
  IGetUserRatesReviewResponse,
  IGetUserRateStatsReview,
  IGetUserRateStatsReviewParams,
  IGetUserWatchedHistoryParams,
  IGetUserWatchedHistoryResponse,
  IGetUserWatchlistHistoryParams,
  IGetUserWatchlistHistoryResponse,
  IPlanPaymentParams,
  IPostHistoryResponse,
  IRegionInfoResponse,
  IToggleFollowUserResponse,
  IUpdateUserProfile,
  IUploadImageToS3Params,
  IUserProfileById,
  IUserIdentity,
  IAddMethodSocialDto,
  IUpdateMethodSocialDto,
  IGetUserUpVoteHistoryResponse,
} from './types';

export const updateUserProfileRequest = async ({ userId, ...rest }: IUpdateUserProfile) => {
  const { data } = await request({
    url: `${env.API_VERSION}/user/${userId}`,
    method: 'PATCH',
    data: rest,
  });

  return data;
};

export const updateUserProfileMeRequest = async ({ ...rest }: IUpdateUserProfile) => {
  const { data } = await request({
    url: `${env.API_VERSION}/user/profile`,
    method: 'PATCH',
    data: rest,
  });

  return data;
};

export const uploadUserImage = async () => {
  const { data } = await request({
    url: `${env.API_VERSION}/user/upload`,
    method: 'POST',
  });

  return data.data;
};

export const uploadImageToS3 = async ({ url, file }: IUploadImageToS3Params): Promise<any> => {
  await FileSystem.uploadAsync(url, file, {
    httpMethod: 'PUT',
    uploadType: FileSystem.FileSystemUploadType.BINARY_CONTENT,
  });
};

export const planPaymentRequest = async (params: IPlanPaymentParams): Promise<any> => {
  await request({
    method: 'PUT',
    url: `${env.API_VERSION}/user/profile-step`,
    data: params,
  });

  return;
};

export const checkUserProfileUpdateValidRequest = async (
  payload: ICheckUserProfileUpdateValidPayload
): Promise<ICheckUserProfileUpdateValidResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/auth/email-verification`,
    method: 'POST',
    data: payload,
  });

  return data?.data;
};

export const getRegionInfoRequest = async (): Promise<IRegionInfoResponse> => {
  const { data: ipAddress } = await axios({ url: 'https://api.ipify.org/?format=text', method: 'GET' });
  const { data } = await axios({ url: `https://ipwho.is/${ipAddress}`, method: 'GET' });

  return data;
};

export const getUserProfileByIdRequest = async (userId: string | number): Promise<IUserProfileById> => {
  const { data } = await request({
    url: `${env.API_VERSION}/user/${userId}/info`,
    method: 'GET',
  });

  return data.data;
};

export const toggleFollowUserRequest = async (userId: string | number): Promise<IToggleFollowUserResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/follow/${userId}`,
    method: 'POST',
  });

  return data?.data;
};

export const getPostUserHistoryRequest = async ({
  userId,
  ...params
}: IGetPostUserHistoryParams): Promise<IPostHistoryResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/user-history/${userId}/posts`,
    method: 'GET',
    params,
  });

  return data;
};

export const getReplyUserHistoryRequest = async ({
  userId,
  ...params
}: IGetReplyUserHistoryParams): Promise<IGetReplyUserHistoryResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/user-history/${userId}/comment-replies`,
    method: 'GET',
    params,
  });

  return data;
};

export const getUserLikesPodcastHistoryRequest = async ({
  userId,
  ...params
}: IGetUserLikesPodcastsHistoryParams): Promise<IGetUserLikesPodcastsHistoryResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/user-history/${userId}/liked-podcasts`,
    method: 'GET',
    params,
  });

  return data;
};

export const getUserLikesEpisodeHistoryRequest = async ({
  userId,
  ...params
}: IGetUserLikesEpisodesHistoryParams): Promise<IGetUserLikesEpisodesHistoryResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/user-history/${userId}/liked-episodes`,
    method: 'GET',
    params,
  });

  return data;
};

export const getUserLikesPostHistoryRequest = async ({
  userId,
  ...params
}: IGetUserLikesPostsHistoryParams): Promise<IGetUserLikesPostsHistoryResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/user-history/${userId}/liked-posts`,
    method: 'GET',
    params,
  });

  return data;
};

export const getUserRateStatsReviewRequest = async ({
  userId,
  ...params
}: IGetUserRateStatsReviewParams): Promise<IGetUserRateStatsReview> => {
  const { data } = await request({
    url: `${env.API_VERSION}/user-history/${userId}/rate-stats`,
    method: 'GET',
    params,
  });

  return data?.data;
};

export const getUserRatesReviewRequest = async ({
  userId,
  ...params
}: IGetUserRatesReviewParams): Promise<IGetUserRatesReviewResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/user-history/${userId}/rates`,
    method: 'GET',
    params,
  });

  return data;
};

export const getUserWatchlistHistoryRequest = async ({
  userId,
  ...params
}: IGetUserWatchlistHistoryParams): Promise<IGetUserWatchlistHistoryResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/user-history/${userId}/watchlist`,
    method: 'GET',
    params,
  });

  return data;
};

export const getUserWatchedHistoryRequest = async ({
  userId,
  ...params
}: IGetUserWatchedHistoryParams): Promise<IGetUserWatchedHistoryResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/user-history/${userId}/watched`,
    method: 'GET',
    params,
  });

  return data;
};

export const fetchUserIdentitiesRequest = async (): Promise<IUserIdentity[]> => {
  const { data } = await request({
    url: `${env.API_VERSION}/user/identity`,
    method: 'GET',
  });
  return data.data;
};

export const addMethodSocialRequest = async (payload: IAddMethodSocialDto): Promise<any> => {
  const { data } = await request({
    url: `${env.API_VERSION}/user/identity`,
    method: 'POST',
    data: payload,
  });
  return data.data;
};

export const updateMethodSocialRequest = async (payload: IUpdateMethodSocialDto): Promise<any> => {
  const { data } = await request({
    url: `${env.API_VERSION}/user/identity`,
    method: 'PUT',
    data: payload,
  });
  return data.data;
};

export const getSubscriptionStatus = async () => {
  const { data } = await request({
    url: `${env.API_VERSION}/subscription/status`,
    method: 'GET',
  });
  return data.data;
};

export const cancelPurchaseRequest = async () => {
  const { data } = await request({
    url: `${env.API_VERSION}/subscription/cancel`,
    method: 'POST',
  });
  return data.data;
};

export const getUserUpVoteHistoryRequest = async ({
  userId,
  ...params
}: IGetUserLikesPostsHistoryParams): Promise<IGetUserUpVoteHistoryResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/user-history/${userId}/up-votes`,
    method: 'GET',
    params,
  });

  return data;
};
