import { createSafeContext } from '@/utils/create-safe-context';
import { RefObject } from 'react';
import { ScrollViewProps, ViewProps, ViewStyle, StyleProp } from 'react-native';
import PagerView from 'react-native-pager-view';
import { SharedValue } from 'react-native-reanimated';

type CollapsingTabsContextProps = {
  sharedProps: Partial<ScrollViewProps>;
  tabIndex: number;
  headerDiff: number;
  headerContainerStyle: StyleProp<ViewStyle>;
  tabBarStyle: StyleProp<ViewStyle>;
  currentScroll: SharedValue<number>;
  pullPosition: SharedValue<number>;
  isScrolling: SharedValue<number>;
  progressIndicator: SharedValue<number>;
  translateY: SharedValue<number>;
  refPager: RefObject<PagerView | null>;
  handleHeaderLayout: ViewProps['onLayout'];
  handleTabBarLayout: ViewProps['onLayout'];
  onIndexChange: (index: number) => void;
};

export const [CollapsingTabsContextProvider, useCollapsingTabsContext] = createSafeContext<CollapsingTabsContextProps>(
  'CollapsingTabsContextProvider component was not found in tree'
);
